package com.tsintergy.lf.web;

import com.tsieframework.boot.autoconfigure.TsieBootApplication;
import com.ulisesbocchio.jasyptspringboot.environment.StandardEncryptableEnvironment;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@TsieBootApplication
@EnableDubbo
public class WebRunJobApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder()
                .environment(new StandardEncryptableEnvironment())
                .sources(WebRunJobApplication.class)
                .run(args);
    }

//    @Bean
//    public JobFailMonitorHelper jobFailMonitorHelper() {
//        return new JobFailMonitorHelper();
//    }
}
