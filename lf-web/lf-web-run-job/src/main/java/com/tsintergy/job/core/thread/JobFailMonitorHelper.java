//package com.tsintergy.job.core.thread;
//
//import cn.hutool.core.date.DatePattern;
//import cn.hutool.core.date.DateUtil;
//import com.epri.sgcc.dcloud.MessageService;
//import com.epri.sgcc.dcloud.vo.SendMessageReq2VO;
//import com.tsieframework.core.base.SpringContextManager;
//import com.tsintergy.job.core.trigger.TriggerTypeEnum;
//import com.tsintergy.job.core.util.I18nUtil;
//import com.tsintergy.job.serviceapi.base.pojo.XxlJobInfo;
//import com.tsintergy.job.serviceapi.base.pojo.XxlJobLog;
//import com.tsintergy.job.web.base.configure.XxlJobAdminConfig;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//public class JobFailMonitorHelper {
//
//	@Value("#{'${jobAlarm.phone-num-list}'.split(',')}")
//	private List<String> phoneNumList;
//
//	@Value("#{'${jobAlarm.job-names}'.split(',')}")
//	private List<String> jobNames;
//
//	@Value("${jobAlarm.template}")
//	private String template;
//
//	@Value("${jobAlarm.system-name}")
//	private String systemName;
//
//	@Value("${jobAlarm.ip}")
//	private String ip;
//
//	@DubboReference(timeout = 30000, interfaceName = "com.epri.sgcc.dcloud.MessageService")
//	private MessageService messageService;
//
//	public static JobFailMonitorHelper getInstance() {
//		return SpringContextManager.getApplicationContext().getBean(JobFailMonitorHelper.class);
//	}
//
//	private static Logger logger = LoggerFactory.getLogger(JobFailMonitorHelper.class);
//
//	// ---------------------- monitor ----------------------
//
//	private Thread monitorThread;
//	private volatile boolean toStop = false;
//
//
//	public void start(){
//		monitorThread = new Thread(new Runnable() {
//
//			@Override
//			public void run() {
//
//				// monitor
//				while (!toStop) {
//					try {
//
//						List<Long> failLogIds = XxlJobAdminConfig.getAdminConfig().getXxlJobLogService().findFailJobLogIds(1000);
//						if (failLogIds!=null && !failLogIds.isEmpty()) {
//							for (Long failLogId: failLogIds) {
//
//								// lock log
//								int lockRet = XxlJobAdminConfig.getAdminConfig().getXxlJobLogService().updateAlarmStatus(failLogId, 0, -1);
//								if (lockRet < 1) {
//									continue;
//								}
//								XxlJobLog log = XxlJobAdminConfig.getAdminConfig().getXxlJobLogService().load(failLogId);
//								XxlJobInfo info = XxlJobAdminConfig.getAdminConfig().getXxlJobInfoService().loadById(log.getJobId());
//
//								// 1、fail retry monitor
//								if (log.getExecutorFailRetryCount() > 0) {
//									JobTriggerPoolHelper.trigger(log.getJobId(), TriggerTypeEnum.RETRY, (log.getExecutorFailRetryCount()-1), log.getExecutorShardingParam(), null);
//									String retryMsg = "<br><br><span style=\"color:#F39C12;\" > >>>>>>>>>>>"+ I18nUtil.getString("jobconf_trigger_type_retry") +"<<<<<<<<<<< </span><br>";
//									log.setTriggerMsg(log.getTriggerMsg() + retryMsg);
//									XxlJobAdminConfig.getAdminConfig().getXxlJobLogService().updateTriggerInfo(log);
//								}
//
//								// 2、fail alarm monitor
//								int newAlarmStatus = 0;		// 告警状态：0-默认、-1=锁定状态、1-无需告警、2-告警成功、3-告警失败
//								if (info != null) {
//									boolean alarmResult = true;
//									try {
//										alarmResult = failAlarm(info, log);
//									} catch (Exception e) {
//										alarmResult = false;
//										logger.error(e.getMessage(), e);
//									}
//									newAlarmStatus = alarmResult?2:3;
//								} else {
//									newAlarmStatus = 1;
//								}
//
//								XxlJobAdminConfig.getAdminConfig().getXxlJobLogService().updateAlarmStatus(failLogId, -1, newAlarmStatus);
//							}
//						}
//
//						TimeUnit.SECONDS.sleep(10);
//					} catch (Exception e) {
//						if (!toStop) {
//							logger.error(">>>>>>>>>>> xxl-job, job fail monitor thread error:{}", e);
//						}
//					}
//				}
//
//				logger.info(">>>>>>>>>>> xxl-job, job fail monitor thread stop");
//
//			}
//		});
//		monitorThread.setDaemon(true);
//		monitorThread.setName("xxl-job, admin JobFailMonitorHelper");
//		monitorThread.start();
//	}
//
//	public void toStop(){
//		toStop = true;
//		// interrupt and wait
//		monitorThread.interrupt();
//		try {
//			monitorThread.join();
//		} catch (InterruptedException e) {
//			logger.error(e.getMessage(), e);
//		}
//	}
//
//
//	// ---------------------- alarm ----------------------
//
//	/**
//	 * fail alarm
//	 *
//	 * @param jobLog
//	 */
//	private boolean failAlarm(XxlJobInfo info, XxlJobLog jobLog) {
//		if (messageService == null) {
//			logger.error("messageService为null");
//			return false;
//		}
//
//		String triggerMsg = jobLog.getTriggerMsg();
//		// 当triggerMsg包含"block strategy effect：Discard Later"时，表示任务堆积
//		if (StringUtils.isNotBlank(triggerMsg) && triggerMsg.contains("block strategy effect：Discard Later")) {
//			// 任务堆积紧急处理逻辑
//			logger.error("检测到任务堆积！任务【{}】触发了丢弃后续调度策略", info.getJobDesc());
//
//			// 发送紧急告警（可以添加额外的紧急处理逻辑）
//			String urgentContent = String.format("[紧急]%s 任务【%s】发生调度阻塞，已触发丢弃策略，请立即处理！",
//					DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
//					info.getJobDesc());
//
//			SendMessageReq2VO urgentReq = new SendMessageReq2VO();
//			urgentReq.setPhoneNumList(phoneNumList);
//			urgentReq.setSystemName(systemName);
//			urgentReq.setSourceIP(ip);
//			urgentReq.setContent(urgentContent);
//
//			try {
//				boolean urgentResult = messageService.sendMsgByPhone(urgentReq);
//				logger.info("任务【{}】堆积紧急告警发送结果：{}", info.getJobDesc(), urgentResult ? "成功" : "失败");
//				return urgentResult;
//			} catch (Exception e) {
//				logger.error("发送任务堆积紧急告警失败，任务ID：{}，错误：{}", info.getId(), e.getMessage());
//				return false;
//			}
//		}
//
//		// 普通任务失败处理逻辑 - 只有jobName在jobNames列表中才发送告警
//		if (jobNames != null && !jobNames.contains(info.getExecutorHandler())) {
//			logger.error("任务【{}】失败但不在告警名单中，跳过告警", info.getJobDesc());
//			return true;
//		}
//
//		try {
//			// 1. 格式化告警时间
//			String alarmTime = cn.hutool.core.date.DateUtil.format(
//					jobLog.getTriggerTime(),
//					DatePattern.NORM_DATETIME_PATTERN
//			);
//
//			// 2. 构建告警内容
//			String alarmContent = String.format(template, alarmTime, info.getExecutorHandler());
//
//			// 3. 发送短信告警
//			SendMessageReq2VO smsReq = new SendMessageReq2VO();
//			smsReq.setPhoneNumList(phoneNumList);
//			smsReq.setSystemName(systemName);
//			smsReq.setSourceIP(ip);
//			smsReq.setContent(alarmContent);
//
//			// 4. 调用短信服务发送告警
//			boolean smsResult = messageService.sendMsgByPhone(smsReq);
//
//			logger.info("任务【{}】失败告警发送结果：{}", info.getJobDesc(),
//					smsResult ? "成功" : "失败");
//			return smsResult;
//		} catch (Exception e) {
//			logger.error("发送短信告警失败，任务ID：{}，错误：{}", info.getId(), e.getMessage());
//			return false;
//		}
//	}
//}
