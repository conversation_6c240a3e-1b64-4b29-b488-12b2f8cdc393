/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/6/6 14:34
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.response;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/6/6 
 * @since 1.0.0
 */
@ApiModel
public class PowerWeatherHisYearMonthResp implements Serializable {
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷",example = "123121")
    private List<BigDecimal> maxLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷",example = "123121")
    private List<BigDecimal> minLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷",example = "123121")
    private List<BigDecimal> aveLoad;
    @ApiModelProperty(value = "负荷峰谷差率",example = "0.93")
    private List<BigDecimal> loadGradient;
    @ApiModelProperty(value = "雨量",example = "1231")
    private List<BigDecimal> rain;
    @ApiModelProperty(value = "最高温度",example = "38")
    private List<BigDecimal> highestTemperature;

    public List<BigDecimal> getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(List<BigDecimal> maxLoad) {
        this.maxLoad = maxLoad;
    }

    public List<BigDecimal> getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(List<BigDecimal> minLoad) {
        this.minLoad = minLoad;
    }

    public List<BigDecimal> getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(List<BigDecimal> aveLoad) {
        this.aveLoad = aveLoad;
    }

    public List<BigDecimal> getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(List<BigDecimal> loadGradient) {
        this.loadGradient = loadGradient;
    }

    public List<BigDecimal> getRain() {
        return rain;
    }

    public void setRain(List<BigDecimal> rain) {
        this.rain = rain;
    }
    public List<BigDecimal> getHighestTemperature() {
        return highestTemperature;
    }

    public void setHighestTemperature(List<BigDecimal> highestTemperature) {
        this.highestTemperature = highestTemperature;
    }
}
