package com.tsintergy.lf.web.base.datamanage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.tsintergy.lf.web.base.datamanage.request.ForecastWeatherExcelRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;


/**
 * 重庆 导入预测气象
 *
 * <AUTHOR>
 */
@Slf4j
public class ForecastWeatherExcelListener extends AnalysisEventListener<ForecastWeatherExcelRequest> {

    private List<ForecastWeatherExcelRequest> list = new ArrayList();

    private WeatherCityHisService weatherCityHisService;

    private WeatherCityFcService weatherCityFcService;

    private String cityId;

    private CityService cityService;

    private String type;

    private static final String FC = "1";
    private static final Boolean WEATHER_CURVE_START_WITH_ZERO = true;
    private static final Boolean startWithT = false;

    public ForecastWeatherExcelListener(
        WeatherCityHisService weatherCityHisService,
        WeatherCityFcService weatherCityFcService, CityService cityService,
        String cityId, String type) {
        this.weatherCityHisService = weatherCityHisService;
        this.weatherCityFcService = weatherCityFcService;
        this.cityId = cityId;
        this.type = type;
        this.cityService = cityService;
    }

    @Override
    public void invoke(ForecastWeatherExcelRequest data, AnalysisContext context) {
        list.add(data);
    }

    @SneakyThrows
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //分片  按照size进行分割
        List<List<ForecastWeatherExcelRequest>> partition = ListUtils.partition(list, 97);
        for (List<ForecastWeatherExcelRequest> excel : partition) {
            processExcelData(excel);
        }

    }

    private void processExcelData(List<ForecastWeatherExcelRequest> excelRequest) throws Exception {
        // 这里的true和false固定
        List<String> columns = ColumnUtil.getColumns(Constants.WEATHER_CURVE_POINT_NUM, WEATHER_CURVE_START_WITH_ZERO, startWithT);
        String trim = excelRequest.get(0).getDateTime().replace("\t", "").replace("\t", "").replace("  ", "")
            .substring(0, 8);
        String dateString = trim.trim().replace("\\s*", "");
        List<String> temperatureList = excelRequest.stream().map(ForecastWeatherExcelRequest::getTemperature)
            .collect(Collectors.toList());
        List<String> humidityList = excelRequest.stream().map(ForecastWeatherExcelRequest::getHumidity)
            .collect(Collectors.toList());
        List<String> rainList = excelRequest.stream().map(ForecastWeatherExcelRequest::getRain)
            .collect(Collectors.toList());
        List<String> windList = excelRequest.stream().map(ForecastWeatherExcelRequest::getWind)
            .collect(Collectors.toList());
        List<String> realTemperatureList = excelRequest.stream().map(ForecastWeatherExcelRequest::getRealTemperature)
            .collect(Collectors.toList());
        List<String> dampColdList = excelRequest.stream().map(ForecastWeatherExcelRequest::getDampCold)
            .collect(Collectors.toList());
        List<CityDO> allCitys = cityService.findAllCitys();
        if (type.equals(FC)) {
            WeatherCityFcDO fcTemperatureDO = getWeatherCityFcDO(dateString, WeatherEnum.TEMPERATURE.getType());
            WeatherCityFcDO fcHumidityDO = getWeatherCityFcDO(dateString, WeatherEnum.HUMIDITY.getType());
            WeatherCityFcDO rainDO = getWeatherCityFcDO(dateString, WeatherEnum.RAINFALL.getType());
            WeatherCityFcDO windDO = getWeatherCityFcDO(dateString, WeatherEnum.WINDSPEED.getType());
            WeatherCityFcDO realTemperatureDO = getWeatherCityFcDO(dateString,
                WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
            WeatherCityFcDO dampColdDO = getWeatherCityFcDO(dateString, WeatherEnum.COLD_DAMPNESS.getType());
            try {
                if (cityId.equals("") || cityId == null) {
                    for (CityDO city : allCitys) {
                        doInsertOrUpdateFc(columns, temperatureList, WeatherEnum.TEMPERATURE.getType(), fcTemperatureDO,
                            city.getId());
                        doInsertOrUpdateFc(columns, humidityList, WeatherEnum.HUMIDITY.getType(), fcHumidityDO,
                            city.getId());
                        doInsertOrUpdateFc(columns, rainList, WeatherEnum.RAINFALL.getType(), rainDO, city.getId());
                        doInsertOrUpdateFc(columns, windList, WeatherEnum.WINDSPEED.getType(), windDO, city.getId());
                        doInsertOrUpdateFc(columns, realTemperatureList, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                            realTemperatureDO, city.getId());
                        doInsertOrUpdateFc(columns, dampColdList, WeatherEnum.COLD_DAMPNESS.getType(), dampColdDO,
                            city.getId());
                    }
                } else {
                    doInsertOrUpdateFc(columns, temperatureList, WeatherEnum.TEMPERATURE.getType(), fcTemperatureDO,
                        cityId);
                    doInsertOrUpdateFc(columns, humidityList, WeatherEnum.HUMIDITY.getType(), fcHumidityDO, cityId);
                    doInsertOrUpdateFc(columns, rainList, WeatherEnum.RAINFALL.getType(), rainDO, cityId);
                    doInsertOrUpdateFc(columns, windList, WeatherEnum.WINDSPEED.getType(), windDO, cityId);
                    doInsertOrUpdateFc(columns, realTemperatureList, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                        realTemperatureDO, cityId);
                    doInsertOrUpdateFc(columns, dampColdList, WeatherEnum.COLD_DAMPNESS.getType(), dampColdDO, cityId);
                }
            } catch (Exception e) {
                log.error("预测气象数据导入异常");
                e.printStackTrace();
            }

        } else {
            WeatherCityHisDO hisTemperatureDO = getWeatherCityHisDO(dateString, WeatherEnum.TEMPERATURE.getType());
            WeatherCityHisDO hisHumidityDO = getWeatherCityHisDO(dateString, WeatherEnum.HUMIDITY.getType());
            WeatherCityHisDO rainDO = getWeatherCityHisDO(dateString, WeatherEnum.RAINFALL.getType());
            WeatherCityHisDO windDO = getWeatherCityHisDO(dateString, WeatherEnum.WINDSPEED.getType());
            WeatherCityHisDO realTemperatureDO = getWeatherCityHisDO(dateString,
                WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
            WeatherCityHisDO dampColdDO = getWeatherCityHisDO(dateString, WeatherEnum.COLD_DAMPNESS.getType());
            try {
                if (cityId.equals("") || cityId == null) {
                    for (CityDO city : allCitys) {
                        doInsertOrUpdateHis(columns, temperatureList, WeatherEnum.TEMPERATURE.getType(),
                            hisTemperatureDO, city.getId());
                        doInsertOrUpdateHis(columns, humidityList, WeatherEnum.HUMIDITY.getType(), hisHumidityDO,
                            city.getId());
                        doInsertOrUpdateHis(columns, rainList, WeatherEnum.RAINFALL.getType(), rainDO, city.getId());
                        doInsertOrUpdateHis(columns, windList, WeatherEnum.WINDSPEED.getType(), windDO, city.getId());
                        doInsertOrUpdateHis(columns, realTemperatureList, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                            realTemperatureDO, city.getId());
                        doInsertOrUpdateHis(columns, dampColdList, WeatherEnum.COLD_DAMPNESS.getType(), dampColdDO,
                            city.getId());
                    }
                } else {
                    doInsertOrUpdateHis(columns, temperatureList, WeatherEnum.TEMPERATURE.getType(), hisTemperatureDO,
                        cityId);
                    doInsertOrUpdateHis(columns, humidityList, WeatherEnum.HUMIDITY.getType(), hisHumidityDO, cityId);
                    doInsertOrUpdateHis(columns, rainList, WeatherEnum.RAINFALL.getType(), rainDO, cityId);
                    doInsertOrUpdateHis(columns, windList, WeatherEnum.WINDSPEED.getType(), windDO, cityId);
                    doInsertOrUpdateHis(columns, realTemperatureList, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                        realTemperatureDO, cityId);
                    doInsertOrUpdateHis(columns, dampColdList, WeatherEnum.COLD_DAMPNESS.getType(), dampColdDO, cityId);
                }

            } catch (Exception e) {
                log.error("实际气象数据导入异常");
                e.printStackTrace();
            }
        }

    }

    WeatherCityFcDO getWeatherCityFcDO(String dateString, Integer type) {
        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
        weatherCityFcDO.setDate(
            new Date(DateUtils.string2Date(dateString, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR).getTime()));
        weatherCityFcDO.setCityId(cityId);
        weatherCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityFcDO.setType(type);
        return weatherCityFcDO;
    }

    WeatherCityHisDO getWeatherCityHisDO(String dateString, Integer type) {
        WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
        weatherCityHisDO.setDate(
            new Date(DateUtils.string2Date(dateString, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR).getTime()));
        weatherCityHisDO.setCityId(cityId);
        weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityHisDO.setType(type);
        return weatherCityHisDO;
    }


    void doInsertOrUpdateFc(List<String> columns, List<String> weatherList, Integer type, WeatherCityFcDO baseWeatherDO,
        String cityId)
        throws Exception {
        WeatherCityFcDO weatherCityFcDO = weatherCityFcService
            .findWeatherCityFcDO(cityId, type,
                baseWeatherDO.getDate());
        if (weatherCityFcDO != null) {
            baseWeatherDO = weatherCityFcDO;
        }
        baseWeatherDO.setCityId(cityId);
        this.saveWeatherFc(columns, weatherList, baseWeatherDO);
    }

    void doInsertOrUpdateHis(List<String> columns, List<String> weatherList, Integer type,
        WeatherCityHisDO baseWeatherDO, String cityId)
        throws Exception {
        WeatherCityHisDO weatherCityHisDO = weatherCityHisService
            .findWeatherCityHisDO(cityId, type,
                baseWeatherDO.getDate());
        if (weatherCityHisDO != null) {
            baseWeatherDO = weatherCityHisDO;
        }
        baseWeatherDO.setCityId(cityId);
        this.saveWeatherHis(columns, weatherList, baseWeatherDO);
    }

    private WeatherCityFcDO saveWeatherFc(List<String> timeCloumns, List<String> dataList, WeatherCityFcDO fcDO)
        throws Exception {
        for (int i = 0; i < timeCloumns.size(); i++) {
            String methodName = "setT" + timeCloumns.get(i);
            Method method = BasePeriod96VO.class.getMethod(methodName, BigDecimal.class);
            method.invoke(fcDO, getBigDecimal(dataList.get(i)));
        }
        String methodName = "setT2400";
        Method method = BasePeriod96VO.class.getMethod(methodName, BigDecimal.class);
        method.invoke(fcDO, getBigDecimal(dataList.get(dataList.size() - 1)));
        this.weatherCityFcService.doInsertOrUpdate(fcDO);
        return fcDO;
    }

    BigDecimal getBigDecimal(String data) {
        BigDecimal bigDecimal = null;
        if (data != null) {
            bigDecimal = new BigDecimal(data);
        }
        return bigDecimal;
    }

    private WeatherCityHisDO saveWeatherHis(List<String> timeCloumns, List<String> dataList, WeatherCityHisDO hisDO)
        throws Exception {
        for (int i = 0; i < timeCloumns.size(); i++) {
            String methodName = "setT" + timeCloumns.get(i);
            Method method = BasePeriod96VO.class.getMethod(methodName, BigDecimal.class);

            method.invoke(hisDO, getBigDecimal(dataList.get(i)));
        }
        String methodName = "setT2400";
        Method method = BasePeriod96VO.class.getMethod(methodName, BigDecimal.class);
        method.invoke(hisDO, getBigDecimal(dataList.get(dataList.size() - 1)));
        this.weatherCityHisService.doInsertOrUpdate(hisDO);
        return hisDO;
    }
}