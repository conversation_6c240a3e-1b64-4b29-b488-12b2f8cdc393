/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: liu<PERSON>
 * Date: 2018/5/10 15:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.weather.support;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.tsintergy.lf.serviceapi.base.weather.bean.Result;
import com.tsintergy.lf.web.base.weather.io.ResourceLoader;
import com.tsintergy.lf.web.base.weather.io.SimpleResourceLoader;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Description: 气象文本解析器  <br>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TxtWeatherResolver extends AbstractFileWeatherResolver {

    List<Result> weatherResultList = Collections.synchronizedList(new ArrayList<Result>());

    private final String ROOT_NODE = "<City>";

    @Override
    public List<Result> resolveWeather(String path) throws Exception {
        ResourceLoader resourceLoader = new SimpleResourceLoader();
        List<File> files = resourceLoader.load(path);
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("thread-txtWeatherResolver").build();
        ThreadPoolExecutor pool = new ThreadPoolExecutor(5, Integer.MAX_VALUE, 0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<Runnable>(1024),
                threadFactory, new ThreadPoolExecutor.AbortPolicy());
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < files.size(); i++) {
            logger.info("处理进度{}{}{}", i + 1, "/", files.size());
            pool.execute(new Work(files.get(i)));
            logger.info("活动线程数:{}",pool.getActiveCount());
            logger.info("线程队列数：{}",pool.getQueue().size());
        }
        while (true) {
            if (pool.getActiveCount() == 0) {
                break;
            }
        }
        long endTime = System.currentTimeMillis();
        logger.info("解析完成，共耗时{}毫秒", endTime - startTime);
        logger.info("子线程处理完成,weatherResultList.size={}", weatherResultList.size());
        pool.shutdownNow();
        return weatherResultList;
    }


    class Work implements Runnable {
        private File file;

        Work(File file) {
            this.file = file;
        }

        @Override
        public void run() {
            try {
                logger.info("启动线程处理...");
                FileInputStream inputStream = new FileInputStream(file);
                InputStreamReader streamReader = new InputStreamReader(inputStream);
                BufferedReader reader = new BufferedReader(streamReader);
                StringBuilder content = new StringBuilder("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                int count = 0;
                String line;
                String cityName = null;
                boolean b = true;
                // 做出标准XML格式
                while ((line = reader.readLine()) != null) {
                    if (b && line.startsWith("<City=")) {
                        b = false;
                        cityName = line.substring(line.indexOf("=") + 1,line.length()-1);
                        line = ROOT_NODE;
                    }
                    // 第1、2处理成标准Xml格式
                    if (b) {
                        continue;
                    }
                    content.append(line);
                    count++;
                }
                Map<String, String> dataMap = xml2Map(content.toString());
                dataMap.put(CITY_NAME,cityName);
                String hisOrFc = "his";
                if (file.getName().toLowerCase().contains("fc")) {
                    hisOrFc = "fc";
                }
                dataMap.put(HIS_OR_FC,hisOrFc);
                List<Result> results = wrapResultFromWeatherMap(dataMap);
                if(!CollectionUtils.isEmpty(results)){
                    weatherResultList.addAll(results);
                }
            } catch (IOException ex) {
                logger.error(ex.getMessage());
            }
        }
    }

}
