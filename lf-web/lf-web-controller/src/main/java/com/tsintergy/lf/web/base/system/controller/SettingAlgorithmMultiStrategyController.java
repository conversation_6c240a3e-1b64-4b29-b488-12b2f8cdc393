package com.tsintergy.lf.web.base.system.controller;

import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.lf.serviceapi.base.system.api.SettingAlgorithmMultiStrategyService;
import com.tsintergy.lf.serviceapi.base.system.dto.SettingAlgorithmMultiStrategyDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SettingAlgorithmMultiStrategyUpdateDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RequestMapping("/system/multiStrategy")
@RestController
@Slf4j
@Api(tags = "预测上报")
public class SettingAlgorithmMultiStrategyController extends CommonBaseController {

    @Autowired
    private SettingAlgorithmMultiStrategyService settingAlgorithmMultiStrategyService;

    @ApiOperation("获取多策略设置")
    @GetMapping("/getEffectStrategySetting")
    public BaseResp<SettingAlgorithmMultiStrategyDTO> getEffectStrategySetting(String cityId, Date systemDate) {
        if (systemDate == null) {
            systemDate = getSystemDate();
        }
        try {
            return baseResp(settingAlgorithmMultiStrategyService.getEffectStrategySetting(cityId, systemDate));
        } catch (Exception e) {
            log.error("获取多策略设置失败", e);
            return baseResp(e);
        }
    }

    @ApiOperation("保存多策略设置")
    @RequestMapping(value = "/saveStrategy", method = RequestMethod.POST)
    public BaseResp saveStrategy(@RequestBody SettingAlgorithmMultiStrategyDTO setting) {
        TsieUserVO loginUser = getLoginUser();
        BaseResp resp = BaseRespBuilder.success().build();
        try {
            settingAlgorithmMultiStrategyService.saveStrategy(setting, loginUser.getId());
            resp.setRetMsg("保存成功");
            return resp;
        } catch (Exception e) {
            log.error("保存多策略设置失败", e);
            return baseResp(e);
        }
    }

    @ApiOperation("查询多策略设置修改日志")
    @RequestMapping(value = "/getUpdateLogList", method = RequestMethod.GET)
    public BaseResp<List<SettingAlgorithmMultiStrategyUpdateDTO>> getUpdateLogList(String cityId, Date updateDate, String updateUserId) throws Exception {
        List<SettingAlgorithmMultiStrategyUpdateDTO> updateLogList = settingAlgorithmMultiStrategyService.getUpdateLogList(cityId, updateDate, updateUserId);
        return BaseRespBuilder.success().setData(updateLogList).build();
    }

    @ApiOperation("获取历史日期多策略设置")
    @GetMapping("/getDailyStrategySetting")
    public BaseResp<SettingAlgorithmMultiStrategyDTO> getDailyStrategySetting(String cityId, Date systemDate) {
        if (systemDate == null) {
            systemDate = getSystemDate();
        }
        return baseResp(settingAlgorithmMultiStrategyService.getDailyStrategySetting(cityId, systemDate));
    }
}
