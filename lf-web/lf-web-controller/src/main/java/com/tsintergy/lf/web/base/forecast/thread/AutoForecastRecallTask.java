/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/21 1:55
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.thread;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/21
 * @since 1.0.0
 */
public class AutoForecastRecallTask implements Runnable {

    private String uid;

    private String cityId;

    private String caliberId;

    private Date startDate;

    private Date endDate;

    private AutoForecastService forecastService;

    private Integer forecastType;

    private List<AlgorithmEnum> algorithmEnums;

    private Integer fcstDayWeatherType;

    private Integer type;

    private Integer pointNum;

    public AutoForecastRecallTask(Integer forecastType, String uid, String cityId, String caliberId,
                                  AutoForecastService forecastService,
                                  Date startDate, Date endDate, List<AlgorithmEnum> algorithmEnums,
                                  Integer fcstDayWeatherType, Integer type, Integer pointNum) {
        this.uid = uid;
        this.cityId = cityId;
        this.caliberId = caliberId;
        this.startDate = startDate;
        this.endDate = endDate;
        this.forecastService = forecastService;
        this.forecastType = forecastType;
        this.algorithmEnums = algorithmEnums;
        this.fcstDayWeatherType = fcstDayWeatherType;
        this.type = type;
        this.pointNum = pointNum;
    }

    @Override
    public void run() {
        try {
            forecastService.autoForecastRecall(forecastType, uid, cityId, caliberId, startDate, endDate, algorithmEnums, fcstDayWeatherType, type, pointNum);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e.toString());
        }
    }
}