package com.tsintergy.lf.web.base.holiday.request;

import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayData;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * 节假日特性分析-特性波动分析
 *
 * <AUTHOR>
 */
public class HolidayAnalysisRequest {


    /**
     * 节假日数据
     */
    @ApiModelProperty(value = "节假日数据")
    private List<HolidayData> holiday;


    /**
     * 日负荷最大值 1、日负荷最小值 2、
     */
    @ApiModelProperty(value = "类型", example = "1")
    private String type;


    /**
     * 分析时段----节假日前x天
     */
    @ApiModelProperty(value = "节假日前x天", example = "33")
    private Integer beforeHoliday;

    @ApiModelProperty(value = "城市ID", example = "1")
    private String cityId;

    /**
     * 分析时段----节假日后x天
     */
    @ApiModelProperty(value = "节假日后x天", example = "3")
    private Integer afterHoliday;


    public List<HolidayData> getHoliday() {
        return holiday;
    }

    public void setHoliday(List<HolidayData> holiday) {
        this.holiday = holiday;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public Integer getBeforeHoliday() {
        return beforeHoliday;
    }

    public void setBeforeHoliday(Integer beforeHoliday) {
        this.beforeHoliday = beforeHoliday;
    }

    public Integer getAfterHoliday() {
        return afterHoliday;
    }

    public void setAfterHoliday(Integer afterHoliday) {
        this.afterHoliday = afterHoliday;
    }


    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }
}
