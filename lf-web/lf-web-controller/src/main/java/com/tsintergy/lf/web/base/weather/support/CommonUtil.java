/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/10/2116:55
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.weather.support;

import java.text.SimpleDateFormat;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2019/10/21
 *@since 1.0.0
 */
public class CommonUtil {


    //日期类型
    private static final String DAY_MONTH_YEAR = "1";
    private static final String MONTH_YEAR = "2";
    private static final String YEAR = "3";


    public   static SimpleDateFormat getSimpleDateFormateType(String type){
        if(DAY_MONTH_YEAR.equals(type)){
            return  new SimpleDateFormat("yyyy-MM-dd");
        }else if(MONTH_YEAR.equals(type)){
            return  new SimpleDateFormat("yyyy-MM");
        }else if(YEAR.equals(type)){
            return  new SimpleDateFormat("yyyy");
        }
        return null;
    }



}