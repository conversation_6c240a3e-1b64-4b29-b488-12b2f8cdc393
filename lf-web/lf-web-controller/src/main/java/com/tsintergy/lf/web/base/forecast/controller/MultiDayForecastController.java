package com.tsintergy.lf.web.base.forecast.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.MultiDayQueryDTO;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.MultiDayQueryWeatherDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.MultiDayForecastService;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 13:31
 **/
@Api(tags = "多日预测查询")
@RequestMapping("/multiDayForecast")
@RestController
public class MultiDayForecastController extends BaseController {

    @Autowired
    private MultiDayForecastService multiDayForecastService;


    @ApiOperation("多日负荷查询")
    @GetMapping(value = "/queryLoad")
    public BaseResp<MultiDayQueryDTO> findLoad(String cityId, String caliberId,Date forecastDate, Integer startDay, Integer endDay, String algorithmId, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        MultiDayQueryDTO queryDTO = multiDayForecastService.findMultiDayLoad(cityId,caliberId,forecastDate,startDay,endDay,algorithmId,batchId);
        baseResp.setData(queryDTO);
        return baseResp;
    }

    @ApiOperation("多日气象查询")
    @GetMapping(value = "/queryWeather")
    public BaseResp<MultiDayQueryWeatherDTO> queryWeather(String cityId, Date forecastDate, Integer startDay, Integer endDay,String weatherSource, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        MultiDayQueryWeatherDTO queryDTO = multiDayForecastService.findMultiDayWeather(cityId,forecastDate,startDay,endDay,weatherSource,batchId);
        baseResp.setData(queryDTO);
        return baseResp;
    }
}
