package com.tsintergy.lf.web.base.forecast.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @date: 3/21/18 4:41 PM
 * @author: angel
 **/
@Data
public class LoadAndWeatherFeatureFcResp implements Serializable {

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal aveLoad;

    @ApiModelProperty(value = "午间最大负荷")
    private BigDecimal noonMaxLoad;

    @ApiModelProperty(value = "夜间最大负荷")
    private BigDecimal eveningMaxLoad;

    /**
     * 最大负荷发生时刻
     */
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    private String minTime;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(scale = 4,percentConvert = 100)
    private BigDecimal gradient;


    /**
     * 积分电量
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal integralLoad;

    /**
     * 峰段电量
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal peakSectionLoad;


    /**
     * 最高温度
     */
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    private BigDecimal aveTemperature;

    /**
     * 最大风速
     */
    private BigDecimal maxWinds;


    /**
     * 降雨量
     */
    private BigDecimal rainfall;

    public LoadAndWeatherFeatureFcResp(WeatherFeatureCityDayFcDO weatherFeature, LoadFeatureFcDTO loadFeatureFcDTO) {
        if (loadFeatureFcDTO != null) {
            this.maxLoad = loadFeatureFcDTO.getMaxLoad();
            this.minLoad = loadFeatureFcDTO.getMinLoad();
            this.aveLoad = loadFeatureFcDTO.getAveLoad();
            this.maxTime = loadFeatureFcDTO.getMaxTime();
            this.minTime = loadFeatureFcDTO.getMinTime();
            this.different = loadFeatureFcDTO.getDifferent();
            this.gradient = loadFeatureFcDTO.getGradient();
            this.integralLoad = loadFeatureFcDTO.getIntegralLoad();
            this.peakSectionLoad = loadFeatureFcDTO.getPeakSectionLoad();
        }
        if (weatherFeature != null) {
            this.highestTemperature = weatherFeature.getHighestTemperature();
            this.lowestTemperature = weatherFeature.getLowestTemperature();
            this.aveTemperature = weatherFeature.getAveTemperature();
            this.maxWinds = weatherFeature.getMaxWinds();
            this.rainfall = weatherFeature.getRainfall();
        }
    }
}
