/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.aspect;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import com.tsieframework.cloud.security.web.businesslog.cuts.OperateLogAspect;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.cloud.security.web.common.security.UserTokenHelper;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.lf.web.base.common.handler.ProceedingJoinPointLogHandlerChain;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/28 20:01
 *@Version: 1.0.0
 */
@Slf4j
@Aspect
public class CustomOperateLogAspect extends OperateLogAspect {

    private TokenManager tokenManager;

    private ProceedingJoinPointLogHandlerChain proceedingJoinPointLogHandlerChain ;

    @Override
    @Around("controllerAspect()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        TsieOperateLogVO operateLog = new TsieOperateLogVO();

        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String userName = this.getUserName(pjp);
            if (StringUtils.isEmpty(userName)) {
                return BaseRespBuilder.fail("用户名为空").build();
            } else {
                Date beginTime = new Date();
                log.debug("用户名: {} 登录时间: {}  URI: {}", new Object[]{userName, (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")).format(beginTime), request.getRequestURI()});
                operateLog.setUserName(userName);

                String title = "";
                try {
                    //tsie3.1去掉了该方法，暂时屏蔽by wangh
//                    title = this.getOperate(pjp);
                } catch (Exception var8) {
                    log.debug("无法获取@OperateLog注解配置", var8);
                }

                operateLog.setId(this.getUuid());
                operateLog.setTitle(title);
                operateLog.setType("info");
                operateLog.setRemoteAddr(UserTokenHelper.getIPAddress());
                operateLog.setRequestUri(request.getRequestURI());
                operateLog.setException("无异常");
                operateLog.setCreateDate(beginTime);

                //记录详细日志
                proceedingJoinPointLogHandlerChain.joinPointLog(pjp,operateLog);
                this.saveOperate(operateLog);
                return pjp.proceed();
            }
        } catch (Throwable var9) {
            operateLog.setType("error");
            operateLog.setException(this.getExecptionContent(var9));
            this.saveExceptionOperate(operateLog);
            throw var9;
        }
    }

    @Override
    protected String getUserName(JoinPoint joinPoint) {
        try {
            return this.tokenManager.getCookieUserName();
        } catch (BusinessException var3) {
            if (log.isDebugEnabled()) {
                log.debug("获取登录账号失败", var3);
            }
            return null;
        }
    }

    public void setTokenManager(TokenManager tokenManager){
        this.tokenManager = tokenManager;
    }

    public void setProceedingJoinPointLogHandlerChain(ProceedingJoinPointLogHandlerChain handlerChain){
        this.proceedingJoinPointLogHandlerChain = handlerChain;
    }
}