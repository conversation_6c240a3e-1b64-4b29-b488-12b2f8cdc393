/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/2/13 16:21 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.common.interceptor;

import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.web.base.security.controller.SsoLoadForecastAccountManager;
import com.tsintergy.lf.web.base.security.properties.SecurityProperties;
import java.sql.Timestamp;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/2/13 
 * @since 1.0.0
 */
@Deprecated
public class PasswordVerificationInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisService redisService;

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        if (!isChangePassword(request)) {
            String username = this.tokenManager.getLoginUser().getUsername();

            Date lastUpdatePassword = (Date) redisService
                .redisGet(securityProperties.getTokenCacheKeyPrefix() + "_dict.lastUpdatePasswordTime." + username,
                    Date.class);
            if (lastUpdatePassword == null) {
                lastUpdatePassword = (Date) redisService
                    .redisGet(securityProperties.getTokenCacheKeyPrefix() + "_dict.createTime." + username, Date.class);
            }
            Integer resetInterval = securityProperties.getPassword().getResetInterval();
            //如果使用同一个密码超过resetInterval，重置密码
            if (System.currentTimeMillis() - lastUpdatePassword.getTime()
                > SsoLoadForecastAccountManager.DAY_MS * resetInterval) {
                throw TsieExceptionUtils.newBusinessException("T001", String.valueOf(resetInterval));
            }

            redisService.redisResetExpiredSecond(
                securityProperties.getTokenCacheKeyPrefix() + "_dict.lastUpdatePasswordTime." + username);
            redisService
                .redisResetExpiredSecond(securityProperties.getTokenCacheKeyPrefix() + "_dict.createTime." + username);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
        @Nullable ModelAndView modelAndView) throws Exception {
        if (isChangePassword(request)) {
            if (response.getStatus() == 200) {
                TsieUserVO tsieUserVO = tokenManager.getLoginUser();
                LoadUserDO loadUserDetailDO = this.userService.findUserById(tsieUserVO.getId());
                loadUserDetailDO.setLastUpdatePasswordTime(new Timestamp(System.currentTimeMillis()));
                userService.doSaveOrUpdateUser(loadUserDetailDO);
            }
        }
    }

    private boolean isChangePassword(HttpServletRequest request) {
        String uri = request.getRequestURI();
        if (StringUtils.isNotBlank(uri) && uri.endsWith("/sysUserManage/user/changeMyPassword")) {
            return true;
        }
        return false;
    }

    public void setRedisService(RedisService redisService) {
        this.redisService = redisService;
    }

    public void setSecurityProperties(SecurityProperties securityProperties) {
        this.securityProperties = securityProperties;
    }

    public void setTokenManager(TokenManager tokenManager) {
        this.tokenManager = tokenManager;
    }

    public UserService getUserService() {
        return userService;
    }

    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}
