/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/12/6
 * @since 1.0.0
 */
@Data
@ApiModel
public class ShortForecastResponse implements Serializable {

    @ApiModelProperty(value = "实际值",example = "[22,132,13]")
    private List<BigDecimal> real;

    @ApiModelProperty(value = "短期曲线",example = "[12,13,21,321]")
    private List<BigDecimal> shortLine;

    @ApiModelProperty(value = "时间集合",example = "0015,0030")
    private List<String> timeList;

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private List<BigDecimal> fc;

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private String time;

}