package com.tsintergy.lf.web.base.acload.response;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
public class AcLoadCharacterResp {

    @ApiModelProperty(value = "日期")
    private Date date;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷")
    private BigDecimal maxLoad;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大预测基础负荷")
    private BigDecimal maxFcBasicLoad;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大统计基础负荷")
    private BigDecimal maxStatisticsBaseLoad;



    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷")
    private BigDecimal minLoad;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小预测基础负荷")
    private BigDecimal minFcBasicLoad;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小统计基础负荷")
    private BigDecimal minStatisticsBaseLoad;



    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大基础负荷偏差率")
    private BigDecimal maxBaseLoadDevationRate;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小基础负荷偏差率")
    private BigDecimal minBaseLoadDevationRate;

}
