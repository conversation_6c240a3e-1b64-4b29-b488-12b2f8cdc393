/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: liu<PERSON>
 * Date: 2018/5/10 14:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.weather.io;

import com.google.common.collect.Lists;
import java.io.File;
import java.io.IOException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ResourceUtils;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SimpleResourceLoader implements ResourceLoader {

    private static Logger logger = LoggerFactory.getLogger(SimpleResourceLoader.class);

    @Override
    public List<File> load(String path) throws IOException {
        List<File> rst = Lists.newArrayList();
        File file = ResourceUtils.getFile(path);
        if (!file.exists()) {
            logger.error("请检查文件路径{}是否存在", path);
            return null;
        }
        File[] files = file.listFiles();
        if (files == null || files.length == 0) {
            logger.error("文件夹{}为空", path);
            return null;
        }
        for (File tempFile : files) {
            if (tempFile.isDirectory()) {
                rst.addAll(load(tempFile.getAbsolutePath()));
            } else {
                rst.add(tempFile);
            }
        }
        return rst;

    }
}