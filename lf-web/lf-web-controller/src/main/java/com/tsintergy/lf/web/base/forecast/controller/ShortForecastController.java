/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:02 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.controller;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.response.ShortForecastResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br> 超短期预测
 *
 * <AUTHOR>
 * @create 2019/12/6
 * @since 1.0.0
 */
@Api(tags = "超短期预测")
@RequestMapping("/short")
@RestController
public class ShortForecastController extends CommonBaseController {

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private LoadCityFcShortService loadCityFcShortService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcService loadCityFcService;


    /**
     * 超短期 手动预测接口
     *
     * @param timeSpan 时间间隔 5 or 15
     */
    @ApiOperation("超短期手动预测接口")
    @RequestMapping(value = "/forecast", method = RequestMethod.GET)
    public BaseResp getShortCurve(Integer timeSpan) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String cityId = this.getLoginCityId();
        String caliberId = this.getCaliberId();
        Date date = this.getSystemDate();
        int totalColums = 0;
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            totalColums = 288;
        } else {
            totalColums = 96;
        }
        String hour = DateUtil.getStrHourFromDate(new Date());
        Integer startTimePoint = Integer
            .valueOf(String.valueOf(DateUtil
                .getTimePoint(hour, totalColums)
            ));
        //超短期预测
        forecastService
            .doShortForecast(cityId, caliberId, date, timeSpan, startTimePoint);
        return baseResp;
    }

    /**
     * 当天 截止到此时 时刻点的预测数据
     */
    private List<BigDecimal> getBigDecimals(Integer timeSpan, String cityId, String caliberId, Date date,
        Integer startTimePoint) throws Exception {
        List<BigDecimal> reportLoadCityFcDO;
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            reportLoadCityFcDO = loadCityFcService
                .findReportLoadCityFc288DO(date, cityId, caliberId, AlgorithmEnum.SHORT_FORECAST.getId());
        } else {
            reportLoadCityFcDO = loadCityFcService
                .findReportLoadCityFcDO(date, cityId, caliberId, AlgorithmEnum.SHORT_FORECAST.getId(), null);
        }
        List<BigDecimal> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reportLoadCityFcDO)) {
            for (int i = 0; i < startTimePoint; i++) {
                if (reportLoadCityFcDO.get(i) == null) {
                    result.add(null);
                } else {
                    result.add(reportLoadCityFcDO.get(i));
                }
            }
        }
        return result;
    }

    private List<String> fixTimeList(List<Date> dates, Integer totalColums) {
        List<String> columns = ColumnUtil.getColumns(totalColums, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> result = new ArrayList<>();
        for (Date date : dates) {
            String dateString = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            for (String column : columns) {
                String finalTime = dateString + " " + column.substring(0, 2) + ":" + column.substring(2);
                result.add(finalTime);
            }
        }
        return result;
    }

    /**
     * 超短期 查询预测数据
     *
     * @param timeSpan 时间间隔 5 or 15
     * @return 数据集
     * <AUTHOR>
     */
    @ApiOperation("超短期查询预测数据")
    @RequestMapping(value = "/forecast/data", method = RequestMethod.GET)
    public BaseResp<ShortForecastResponse> getShortCurveData(Date startDate, Date endDate, String cityId, Integer timeSpan) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Date> dayList = DateUtil.getListBetweenDay(startDate, endDate);
        ShortForecastResponse response = new ShortForecastResponse();
        int totalColums = 0;
        int type;
        String caliberId = this.getCaliberId();
        List<BigDecimal> real = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        Date date = this.getSystemDateTime();
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            totalColums = 288;
            type = 1;
            for (Date day : dayList) {
                List<BigDecimal> realMid = loadCityHisService.findLoadCityHis288DO(day, cityId, caliberId);
                if (CollectionUtils.isEmpty(realMid)) {
                    real.addAll(ColumnUtil.getZeroOrNullList(288, null));
                } else {
                    real.addAll(realMid);
                }
                //短期预测从load_city_fc_basic中取上报的数据
                List<BigDecimal> fcMid = loadCityFcService
                    .findReportLoadCityFcDO(day, cityId, caliberId, null, true);
                if (CollectionUtils.isEmpty(fcMid)) {
                    fc.addAll(ColumnUtil.getZeroOrNullList(288, null));
                } else {
                    //转换成288点数据
                    fc.addAll(PeriodDataUtil.data96to288(fcMid));
                }
            }
        } else {
            totalColums = 96;
            type = 2;
            //实际负荷
            for (Date oneDay : dayList) {
                List<BigDecimal> realMid = loadCityHisService.findLoadCityHisDO(oneDay, cityId, caliberId);
                if (CollectionUtils.isEmpty(realMid)) {
                    real.addAll(ColumnUtil.getZeroOrNullList(96, null));
                } else {
                    real.addAll(realMid);
                }
                List<BigDecimal> fcMid = loadCityFcService
                    .findReportLoadCityFcDO(oneDay, cityId, caliberId, null, true);
                if (CollectionUtils.isEmpty(fcMid)) {
                    fc.addAll(ColumnUtil.getZeroOrNullList(96, null));
                } else {
                    fc.addAll(fcMid);
                }
            }
        }
        if (!CollectionUtils.isEmpty(real) && real.size() > 0) {
            response.setReal(real);
        }
        response.setFc(fc);
        String hour = DateUtil.getStrHourFromDate(date);
        Integer startTimePoint = Integer
            .valueOf(String.valueOf(DateUtil
                .getTimePoint(hour, totalColums)
            ));
        String startDayString = DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String endDayString = DateUtils.date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String today = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<BigDecimal> finalResult = new ArrayList<>();
        if (endDayString.equals(today)) {
            List<Date> midDateList = DateUtil
                .getListBetweenDay(startDate, startDate.equals(endDate) ? endDate : DateUtil.getMoveDay(endDate, -1));
            //多天查询时补充前几天数据
            if (!startDayString.equals(endDayString)) {
                //截止到昨天的超短期数据，从fcBasic表查询后 放到 finalResult
                findInFcBasic(cityId, timeSpan, caliberId, finalResult, midDateList);
            }
            List<String> columns = ColumnUtil
                .getColumns(ShortConstants.MINUTE.equals(timeSpan) ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO,
                    false);
            String fcStartTime = columns.get(startTimePoint);
            //超短期预测 当天的数据拼接
            LoadCityFcShortDO shortData = loadCityFcShortService
                .findShortData(fcStartTime, date, cityId, caliberId, type);
            if (shortData != null) {
                //填充最新生成时间
                response.setTime(
                    shortData.getUpdatetime() == null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(shortData
                        .getCreatetime())
                        : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(shortData.getUpdatetime()));
                //查询当天数据 时间从load_city_fc_short中取
                List<BigDecimal> shortLine = ColumnUtil
                    .toList(shortData, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> result = getBigDecimals(timeSpan, cityId, caliberId, date, startTimePoint);
                result.addAll(shortLine);
                //拼接多天的超短期预测数据。来自fc_basic和fc_short表
                if (result.size() > totalColums) {
                    finalResult.addAll(result.subList(0, totalColums));
                } else {
                    finalResult.addAll(result);
                }
            }else {
                List<BigDecimal> result = getBigDecimals(timeSpan, cityId, caliberId, date, startTimePoint);
                if (result.size() > totalColums) {
                    finalResult.addAll(result.subList(0, totalColums));
                } else {
                    finalResult.addAll(result);
                }
            }
        } else {
            //查询历史数据，不涉及超短期预测表的查询
            String time = findInFcBasic(cityId, timeSpan, caliberId, finalResult, dayList);
            response.setTime(time);
        }
        if (response.getFc() == null && response.getReal() == null && response.getShortLine() == null) {
            return new BaseResp("T706");
        }
        response.setShortLine(finalResult);
        List<String> timeStrings = fixTimeList(dayList, totalColums);
        response.setTimeList(timeStrings);
        baseResp.setData(response);
        return baseResp;
    }

    private String findInFcBasic(String cityId, Integer timeSpan, String caliberId, List<BigDecimal> finalResult,
        List<Date> midDateList) throws Exception {
        //多天时循环覆盖 只取最后一天的最新时间
        String time = null;
        for (Date midDate : midDateList) {
            List<BigDecimal> reportLoadCityFcDO = new ArrayList<>();
            if (ShortConstants.MINUTE.equals(timeSpan)) {
                LoadCityFc288DO doByDate = loadCityFcService
                    .find288DOByDate(midDate, cityId, caliberId, AlgorithmEnum.SHORT_FORECAST.getId());
                if (doByDate != null) {
                    reportLoadCityFcDO = BasePeriodUtils.toList(doByDate, 288,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                    if (doByDate.getUpdatetime() != null) {
                        time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(doByDate.getUpdatetime());
                    } else {
                        time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(doByDate.getCreatetime());
                    }
                }
            } else {
                LoadCityFcDO reportCityFcDO = loadCityFcService
                    .findReportCityFcDO(midDate, cityId, caliberId, AlgorithmEnum.SHORT_FORECAST.getId(), null);
                if (reportCityFcDO != null) {
                    reportLoadCityFcDO = BasePeriodUtils.toList(reportCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                    if (reportCityFcDO.getUpdatetime() != null) {
                        time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportCityFcDO.getUpdatetime());
                    } else {
                        time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportCityFcDO.getCreatetime());
                    }
                }
            }
            finalResult.addAll(checkNull(reportLoadCityFcDO, timeSpan));
        }
        return time;
    }

    /**
     * 判断数据null，如果是 则填充null
     *
     * @param timeSpan 5or15
     */
    private List<BigDecimal> checkNull(List<BigDecimal> dataList, Integer timeSpan) {
        if (!CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            dataList.addAll(ColumnUtil.getZeroOrNullList(288, null));
        } else {
            dataList.addAll(ColumnUtil.getZeroOrNullList(96, null));
        }
        return dataList;
    }


}