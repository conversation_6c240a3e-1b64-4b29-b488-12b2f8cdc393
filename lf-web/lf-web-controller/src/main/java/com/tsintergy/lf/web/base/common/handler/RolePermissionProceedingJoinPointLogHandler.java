/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieRoleVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.web.base.security.request.PermissionRequest;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * Description: 添加删除权限记录 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/29 9:10
 * @Version: 1.0.0
 */
@Slf4j
public class RolePermissionProceedingJoinPointLogHandler extends BaseProceedingJoinPointLogHandler {

    private SecurityService securityService;

    public RolePermissionProceedingJoinPointLogHandler(
        SecurityService securityService) {
        this.securityService = securityService;
    }

    @Override
    List<String> initOperateTitle() {
        return new ArrayList<String>() {
            {
                add("新增权限");
                add("删除权限");
            }
        };
    }

    @Override
    public void proceedingJoinPointLog(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog) {
        if (isMaching(proceedingJoinPoint, operateLog)) {
            PermissionRequest permissionRequest = (PermissionRequest) proceedingJoinPoint.getArgs()[0];
            DBQueryParam param = DBQueryParamBuilder.create()
                .where(QueryOp.StringEqualTo, "id", permissionRequest.getUserid()).queryDataOnly().build();
            TsieUserVO tsieUserVO = null;
            try {
                tsieUserVO = securityService.queryTsieUserVo(param);
                TsieRoleVO tsieRoleVO = securityService.queryTsieRoleVO(permissionRequest.getRoleid());
                StringBuilder description = new StringBuilder(operateLog.getTitle())
                    .append(",")
                    .append("用户名为")
                    .append(tsieUserVO.getUsername())
                    .append(",")
                    .append("角色名为")
                    .append(tsieRoleVO.getRole());
                operateLog.setDescription(description.toString());
            } catch (Exception e) {
                log.error("记录角色权限增删异常", e);
            }
        }
    }

    public void setSecurityService(SecurityService securityService) {
        this.securityService = securityService;
    }


}