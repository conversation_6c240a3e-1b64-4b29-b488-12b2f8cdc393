/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/1/2 11:08 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/2
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportRequest implements Serializable {

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "年份")
    private String year;


    @ApiModelProperty(value = "周")
    private String week;

    @ApiModelProperty(value = "quarter")
    private String quarter;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "code标志位")
    private Integer code;

    /**
     * 需求最大电力
     */
    @ApiModelProperty(value = "需求最大电力")
    private BigDecimal reqMaxLoad;

    /**
     * 需求最小电力
     */
    @ApiModelProperty(value = "需求最小电力")
    private BigDecimal reqMinLoad;

    /**
     * 需求电量
     */
    @ApiModelProperty(value = "需求电量")
    private BigDecimal reqEnergy;

    /**
     * 错峰电力
     */
    @ApiModelProperty(value = "错峰电力")
    private BigDecimal peakLoad;

    /**
     * 错峰电量
     */
    @ApiModelProperty(value = "错峰电量")
    private BigDecimal peakEnergy;

    /**
     * 可供电力
     */
    @ApiModelProperty(value = "可供电力")
    private BigDecimal proLoad;

    /**
     * 可供电量
     */
    @ApiModelProperty(value = "可供电量")
    private BigDecimal proEnergy;

    @ApiModelProperty(value = "假日code")
    private Integer holidayCode;

    public Integer getHolidayCode() {
        return holidayCode;
    }

    public void setHolidayCode(Integer holidayCode) {
        this.holidayCode = holidayCode;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public BigDecimal getReqMaxLoad() {
        return reqMaxLoad;
    }

    public void setReqMaxLoad(BigDecimal reqMaxLoad) {
        this.reqMaxLoad = reqMaxLoad;
    }

    public BigDecimal getReqMinLoad() {
        return reqMinLoad;
    }

    public void setReqMinLoad(BigDecimal reqMinLoad) {
        this.reqMinLoad = reqMinLoad;
    }

    public BigDecimal getReqEnergy() {
        return reqEnergy;
    }

    public void setReqEnergy(BigDecimal reqEnergy) {
        this.reqEnergy = reqEnergy;
    }

    public BigDecimal getPeakLoad() {
        return peakLoad;
    }

    public void setPeakLoad(BigDecimal peakLoad) {
        this.peakLoad = peakLoad;
    }

    public BigDecimal getPeakEnergy() {
        return peakEnergy;
    }

    public void setPeakEnergy(BigDecimal peakEnergy) {
        this.peakEnergy = peakEnergy;
    }

    public BigDecimal getProLoad() {
        return proLoad;
    }

    public void setProLoad(BigDecimal proLoad) {
        this.proLoad = proLoad;
    }

    public BigDecimal getProEnergy() {
        return proEnergy;
    }

    public void setProEnergy(BigDecimal proEnergy) {
        this.proEnergy = proEnergy;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week = week;
    }

    public String getQuarter() {
        return quarter;
    }

    public void setQuarter(String quarter) {
        this.quarter = quarter;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }


}