package com.tsintergy.lf.web.base.forecast.request;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 曲线修正
 * <AUTHOR>
 * @date 2018/05/25 15:21
 */
public class RecorrectRequest {

    /**
     * 负荷数据
     */
    @ApiModelProperty(value = "负荷数据")
    @Size(min = 1,message = "数据不能为空")
    private List<BigDecimal> forecast;

    /**
     * 最大负荷
     */
    @ApiModelProperty(value = "最大负荷")
    @NotNull(message = "最大负荷不能为空")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @ApiModelProperty(value = "最小负荷")
    @NotNull(message = "最小负荷不能为空")
    private BigDecimal minLoad;

    /**
     * 早高峰
     */
    private BigDecimal earlyPeak;

    /**
     * 午高峰
     */
    private BigDecimal noonPeak;

    /**
     * 晚高峰
     */
    private BigDecimal eveningPeak;

    /**
     * 腰荷
     */
    private BigDecimal waistLoad;

    /**
     * 低谷
     */
    private BigDecimal trough;

    public List<BigDecimal> getForecast() {
        return forecast;
    }

    public void setForecast(String forecast) {
        this.forecast = JSONArray.parseArray(forecast, BigDecimal.class);
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public void setForecast(List<BigDecimal> forecast) {
        this.forecast = forecast;
    }

    public BigDecimal getEarlyPeak() {
        return earlyPeak;
    }

    public void setEarlyPeak(BigDecimal earlyPeak) {
        this.earlyPeak = earlyPeak;
    }

    public BigDecimal getNoonPeak() {
        return noonPeak;
    }

    public void setNoonPeak(BigDecimal noonPeak) {
        this.noonPeak = noonPeak;
    }

    public BigDecimal getEveningPeak() {
        return eveningPeak;
    }

    public void setEveningPeak(BigDecimal eveningPeak) {
        this.eveningPeak = eveningPeak;
    }

    public BigDecimal getWaistLoad() {
        return waistLoad;
    }

    public void setWaistLoad(BigDecimal waistLoad) {
        this.waistLoad = waistLoad;
    }

    public BigDecimal getTrough() {
        return trough;
    }

    public void setTrough(BigDecimal trough) {
        this.trough = trough;
    }
}
