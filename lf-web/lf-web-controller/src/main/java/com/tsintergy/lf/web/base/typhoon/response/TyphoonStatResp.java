/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2018/11/23 10:23
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.typhoon.response;

import java.io.Serializable;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2018/11/23
 * @since 1.0.0
 */
public class TyphoonStatResp implements Serializable {
    /**
     * 年份
     */
    private String year;
    /**
     * 台风
     */
    private int typhoon;

    /**
     * 高温
     */
    private int highTemperature;

    /**
     * 降雨
     */
    private int rainfall;

    /**
     * 大风
     */
    private int strongWind;


    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public int getTyphoon() {
        return typhoon;
    }

    public void setTyphoon(int typhoon) {
        this.typhoon = typhoon;
    }

    public int getHighTemperature() {
        return highTemperature;
    }

    public void setHighTemperature(int highTemperature) {
        this.highTemperature = highTemperature;
    }

    public int getRainfall() {
        return rainfall;
    }

    public void setRainfall(int rainfall) {
        this.rainfall = rainfall;
    }

    public int getStrongWind() {
        return strongWind;
    }

    public void setStrongWind(int strongWind) {
        this.strongWind = strongWind;
    }
}