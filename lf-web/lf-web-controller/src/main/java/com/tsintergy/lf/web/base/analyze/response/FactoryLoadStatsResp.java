/**
 * Copyright(C),2015‐2024,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.analyze.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 厂用电分析-特性表格
 * <AUTHOR>
 * @date 2024/05/10 18:29
 * @version: 1.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(description = "厂用电分析-特性表格")
public class FactoryLoadStatsResp {

    @ApiModelProperty(name = "名称")
    private String name;

    @ApiModelProperty(name = "最大值")
    @BigdecimalJsonFormat(scale=4)
    private BigDecimal maxLoad;

    @ApiModelProperty(name = "最小值")
    @BigdecimalJsonFormat(scale=4)
    private BigDecimal minLoad;

    @ApiModelProperty(name = "平均值")
    @BigdecimalJsonFormat(scale=4)
    private BigDecimal aveLoad;

    @ApiModelProperty(name = "早峰最大")
    @BigdecimalJsonFormat(scale=4)
    private BigDecimal earlyMaxLoad;

    @ApiModelProperty(name = "早峰最大")
    @BigdecimalJsonFormat(scale=4)
    private BigDecimal nightMaxLoad;

    @ApiModelProperty(name = "曲线")
    private List<BigDecimal> loadList;



}