package com.tsintergy.lf.web.base.annotation;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ConditionalOnProperty(name = "tsie.cas.open", havingValue = "true")
public @interface DiaokongCasEnabledConfigured {
}
