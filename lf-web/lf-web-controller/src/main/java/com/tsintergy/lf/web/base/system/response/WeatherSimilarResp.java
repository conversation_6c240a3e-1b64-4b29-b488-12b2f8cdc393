/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  ThinkPad
 * Date:  2019/4/15 7:02
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.system.response;


import com.tsintergy.lf.web.base.weather.response.WeatherSimilar;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/4/15
 * @since 1.0.0
 */
public class WeatherSimilarResp {

    private List<BigDecimal> fcWeatherLoad;


    private List<WeatherSimilar> weatherSimilars;


    public List<BigDecimal> getFcWeatherLoad() {
        return fcWeatherLoad;
    }

    public void setFcWeatherLoad(List<BigDecimal> fcWeatherLoad) {
        this.fcWeatherLoad = fcWeatherLoad;
    }

    public List<WeatherSimilar> getWeatherSimilars() {
        return weatherSimilars;
    }

    public void setWeatherSimilars(List<WeatherSimilar> weatherSimilars) {
        this.weatherSimilars = weatherSimilars;
    }
}