/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/1/6 3:30 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeCumulativeService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyWeekService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAllAccuracyService;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeInfoDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportDay96AndDateDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.SynthesizeAccuracyDTO;
import com.tsintergy.lf.core.enums.ReportTypeEnum;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.report.request.AccuracyLoadRequest;
import com.tsintergy.lf.web.base.report.request.ListRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.Date;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:  <br> 考核准确率查询页面
 *
 * <AUTHOR>
 * @create 2020/1/6
 * @since 1.0.0
 */
@Api(tags = "准确率上报控制器")
@RequestMapping(value = "/accuracy")
@RestController
public class AccuracyReportController extends CommonBaseController {

    @Autowired
    private CityService cityService;

    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;

    @Autowired
    private ReportAccuracyWeekService reportAccuracyWeekService;

    @Autowired
    private ReportAccuracyMonthService reportAccuracyMonthService;

    @Autowired
    private ReportAccuracySynthesizeMonthService reportAccuracySynthesizeMonthService;

    @Autowired
    private ReportAccuracySynthesizeCumulativeService reportAccuracySynthesizeCumulativeService;
    @Autowired
    private ReportAllAccuracyService reportAllAccuracyService;
    /**
     * 日负荷填报准确率
     */
    @ApiOperation("日负荷填报准确率")
    @GetMapping(value = "/day")
    public BaseResp<List<ReportAccuracyDTO>> getDayAccuracy(@ApiParam("城市ID") String cityId,
        @ApiParam("开始时间") String start, @ApiParam("结束时间") String end, @ApiParam("类型") Integer type,@ApiParam("口径")String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = getDefaultCityId(cityId);
        List<ReportAccuracyDTO> reportAccuracyDTOS = null;
        //多日查询
        if (ReportTypeEnum.DAY.getType().equals(type)) {
            reportAccuracyDTOS = reportAccuracyDayService.findReportAccuracy(cityId, start, end,caliberId);
        } else {
            //多月查询
            reportAccuracyDTOS = reportAccuracyMonthService
                .findMonthAvg(cityId, start, end, ReportTypeEnum.DAY.getType());
        }
        if (reportAccuracyDTOS == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(reportAccuracyDTOS);
        return baseResp;
    }
    /**
     * 累计综合准确率重新加载
     */
    @PostMapping(value = "/accuracyLoad")
    public BaseResp updateLoadeAccuracy(@RequestBody AccuracyLoadRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date startDate = DateUtil.getDate(request.getStart(), null);
        Date endDate = DateUtil.getDate(request.getStart(), null);
        try {
            reportAllAccuracyService.doReportAccuracy(startDate,endDate,null);
        } catch (Exception e) {
            return BaseResp.failResp(e.getMessage());
        }
        return baseResp;
    }
    /**
     * 周负荷填报准确率
     */
    @ApiOperation("周负荷填报准确率")
    @GetMapping(value = "/week")
    public BaseResp<List<ReportAccuracyDTO>> getWeekAccuracy(@ApiParam("城市ID") String cityId,
        @ApiParam("开始时间") String start, @ApiParam("结束时间") String end, @ApiParam("类型") Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportAccuracyDTO> reportAccuracyDTOS = null;
        cityId = getDefaultCityId(cityId);
        //多周
        if (type.equals(ReportTypeEnum.WEEK.getType())) {
            reportAccuracyDTOS = reportAccuracyWeekService.findWeekAccuracy(cityId, start, end);
        } else {
            //多月查询
            reportAccuracyDTOS = reportAccuracyMonthService
                .findMonthAvg(cityId, start, end, ReportTypeEnum.WEEK.getType());
        }
        if (CollectionUtils.isEmpty(reportAccuracyDTOS) || reportAccuracyDTOS == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(reportAccuracyDTOS);
        return baseResp;
    }

    /**
     * 月负荷填报准确率
     */
    @ApiOperation("月负荷填报准确率")
    @GetMapping(value = "/month")
    public BaseResp<List<ReportAccuracyDTO>> getMonthAccuracy(@ApiParam("城市ID") String cityId,
        @ApiParam("开始时间") String start, @ApiParam("结束时间") String end, @ApiParam("类型") Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = getDefaultCityId(cityId);
        List<ReportAccuracyDTO> reportAccuracyDTOS = null;
        if (type == ReportTypeEnum.MONTH.getType()) {
            reportAccuracyDTOS = reportAccuracyMonthService.findMonthAccuracy(cityId, start, end);
        } else {
            reportAccuracyDTOS = reportAccuracyMonthService.findMonthYearAvg(cityId, start, end);
        }
        if (CollectionUtils.isEmpty(reportAccuracyDTOS) || reportAccuracyDTOS == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(reportAccuracyDTOS);
        return baseResp;
    }

    /**
     * 综合负荷填报准确率
     */
    @ApiOperation("综合负荷填报准确率")
    @GetMapping(value = "/synthesize")
    public BaseResp<List<SynthesizeAccuracyDTO>> getSynthesizeAccuracy(@ApiParam("城市ID") String cityId,
        @ApiParam("开始时间") String start, @ApiParam("结束时间") String end, @ApiParam("类型") Integer type) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = getDefaultCityId(cityId);
        List<SynthesizeAccuracyDTO> synthesizeAccuracyDTOS = null;
        if (ReportTypeEnum.MONTH.getType().equals(type)) {
            synthesizeAccuracyDTOS = reportAccuracySynthesizeMonthService.findReportSynthesize(cityId, start, end);
        } else {
            synthesizeAccuracyDTOS = reportAccuracySynthesizeMonthService.findYearSynthesize(cityId, start, end);
        }
        if (synthesizeAccuracyDTOS == null || CollectionUtils.isEmpty(synthesizeAccuracyDTOS)) {
            return new BaseResp("T706");
        }

//        for (SynthesizeAccuracyDTO synthesizeAccuracyDTO : synthesizeAccuracyDTOS) {
//
//            synthesizeAccuracyDTO.setStandardAccuracy(synthesizeAccuracyDTO.getStandardAccuracy().divide(new BigDecimal(100), 4));
//
//        }

        baseResp.setData(synthesizeAccuracyDTOS);
        return baseResp;
    }
    /**
     * 累计综合准确率指标查询
     */
    @ApiOperation("累计综合准确率指标查询")
    @RequestMapping(value = "/comprehensiveAccuracyInfo", method = RequestMethod.GET)
    public BaseResp<List<ReportAccuracySynthesizeInfoDTO>> getComprehensiveAccuracy(java.util.Date date, String cityId, Integer type) throws Exception {
        BaseResp baseResp = BaseResp.succResp();

        List<ReportAccuracySynthesizeInfoDTO> synthesizeCumulative = reportAccuracySynthesizeCumulativeService
            .findSynthesizeCumulative(date, type,
                cityId);
        baseResp.setData(synthesizeCumulative);
        return baseResp;
    }
    /**
     * 累计综合准确率查询
     */
    @RequestMapping(value = "/comprehensiveAccuracy", method = RequestMethod.GET)
    @ApiOperation("累计综合准确率查询")
    public BaseResp getSynthesizeAccuracy(String start, String end, Integer type,String cityId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        java.sql.Date date = null;
        java.sql.Date date1 = null;
        try {
            if (start!=null&&end!=null)
            {
                start = start + "-01";
                end = end + "-01";
                Date startDate = DateUtil.getDate(start, null);
                Date endDate = DateUtil.getDate(end, null);
                date = new java.sql.Date(startDate.getTime());
                date1 = new java.sql.Date(endDate.getTime());
            }
            List<ReportAccuracySynthesizeDTO> byDate = reportAccuracySynthesizeCumulativeService
                .findByDate(date, date1,
                    type);
            baseResp.setData(byDate);

        } catch (Exception e) {
            return BaseResp.failResp(e.getMessage());
        }
        return baseResp;
    }
    /**
     * 累计综合准确率修改
     */
    @ApiOperation("累计综合准确率修改")
    @RequestMapping(value = "/comprehensiveAccuracy", method = RequestMethod.PUT)
    public BaseResp updateSynthesizeAccuracy(@RequestBody ListRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        try {
            request.getRequest().forEach(e -> {
                BigDecimal divide=null;
                if (e.getComprehensiveAccuracyRight()!=null) {
                    divide = e.getComprehensiveAccuracyRight().divide(new BigDecimal(100));
                }
                String dateStr=e.getDate()+"-01";
                Date date = DateUtil.getDate(dateStr, null);
                try {
                    reportAccuracySynthesizeCumulativeService
                        .updateAccuracyCorrect(e.getCityId(),
                            divide,date,e.getType());
                } catch (Exception exception) {
                    exception.printStackTrace();
                }

            });
        } catch (Exception exception) {
            return BaseResp.failResp(exception.getMessage());
        }
        return baseResp;
    }

    /**
     * 各地区日96点负荷预测准确率
     */
    @ApiOperation("各地区日96点负荷预测准确率")
    @GetMapping(value = "/reportDayAccuracy")
    public BaseResp<ReportDay96AndDateDTO> findReportDayAccuracy(Date startDate,Date endDate,String cityId,String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        try {
            ReportDay96AndDateDTO accuracyDayFor96DTO = reportAccuracyDayService
                .findAccuracyDayFor96DTO(cityId, startDate, endDate,getLoginUserId(), caliberId);
            baseResp.setData(accuracyDayFor96DTO);
        } catch (Exception e) {
            return BaseResp.failResp(e.getMessage());
        }
        return baseResp;
    }
    private String getDefaultCityId(String cityId) throws Exception {
        CityDO cityVO = cityService.findCityById(cityId);
        if (cityVO.getType() == 1) {
            return null;
        }
        return cityId;
    }
}