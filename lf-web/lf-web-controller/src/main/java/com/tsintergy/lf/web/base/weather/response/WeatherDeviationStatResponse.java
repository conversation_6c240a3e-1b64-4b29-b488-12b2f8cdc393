/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.weather.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/11/16 17:11
 * @Version: 1.0.0
 */
@ApiModel
@Data
public class WeatherDeviationStatResponse {
    @ApiModelProperty(value = "最大温度偏差")
private BigDecimal maxTemDeviation;
    @ApiModelProperty(value = "最小温度偏差")
    private BigDecimal minTemDeviation;
    @ApiModelProperty(value = "最大雨量偏差")
    private BigDecimal maxRainDeviation;
    @ApiModelProperty(value = "最小雨量偏差")
    private BigDecimal minRainDeviation;
    @ApiModelProperty(value = "温度精度")
    private BigDecimal temAccuracy;
    @ApiModelProperty(value = "雨量精度")
    private BigDecimal rainAccuracy;
}