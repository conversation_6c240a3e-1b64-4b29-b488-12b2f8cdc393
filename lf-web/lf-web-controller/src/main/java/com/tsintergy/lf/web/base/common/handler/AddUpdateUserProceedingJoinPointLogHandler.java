/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import com.tsintergy.lf.web.base.security.controller.UserRequest;
import java.util.ArrayList;
import java.util.List;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/28 20:21
 *@Version: 1.0.0
 */
public class AddUpdateUserProceedingJoinPointLogHandler extends BaseProceedingJoinPointLogHandler {

    @Override
    List<String> initOperateTitle() {
        return new ArrayList<String>(){
            {
                add("新增用户");
                add("修改用户");
            }
        };
    }

    @Override
    public void proceedingJoinPointLog(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog) {
        if (isMaching(proceedingJoinPoint,operateLog)){
            UserRequest userRequest = (UserRequest) proceedingJoinPoint.getArgs()[0];
            String addUserName = userRequest.getUsername();
            StringBuilder description = new StringBuilder(operateLog.getTitle())
                .append(",")
                .append("用户名为")
                .append(addUserName);
            operateLog.setDescription(description.toString());
        }
    }


}