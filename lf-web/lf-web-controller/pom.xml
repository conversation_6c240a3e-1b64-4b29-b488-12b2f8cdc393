<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>lf-web</artifactId>
    <groupId>com.tsintergy.lf</groupId>
    <version>${lf.version}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>lf-web-controller</artifactId>

  <dependencies>

    <dependency>
      <groupId>com.tsieframework.boot</groupId>
      <artifactId>tsie-boot-starter</artifactId>
      <!--      <exclusions>-->
      <!--        <exclusion>-->
      <!--          <artifactId>poi</artifactId>-->
      <!--          <groupId>org.apache.poi</groupId>-->
      <!--        </exclusion>-->
      <!--        <exclusion>-->
      <!--          <artifactId>poi-ooxml</artifactId>-->
      <!--          <groupId>org.apache.poi</groupId>-->
      <!--        </exclusion>-->
      <!--      </exclusions>-->
    </dependency>

    <dependency>
      <groupId>com.tsieframework.boot</groupId>
      <artifactId>tsie-boot-starter-web</artifactId>
      <exclusions>
<!--        <exclusion>-->
<!--          <artifactId>spring-plugin-core</artifactId>-->
<!--          <groupId>org.springframework.plugin</groupId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <artifactId>springfox-swagger2</artifactId>-->
<!--          <groupId>io.springfox</groupId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <artifactId>spring-plugin-metadata</artifactId>-->
<!--          <groupId>org.springframework.plugin</groupId>-->
<!--        </exclusion>-->
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-i18n</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-service-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-config</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>javax.persistence</groupId>
      <artifactId>javax.persistence-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tsieframework.cloud.security</groupId>
      <artifactId>tsie-cloud-security-service-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsieframework.cloud.security</groupId>
      <artifactId>tsie-cloud-security-web-controller</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.jasig.cas.client</groupId>
          <artifactId>cas-client-core</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>cas-client-autoconfig-support</artifactId>
          <groupId>net.unicon.cas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>cas-client-support-saml</artifactId>
          <groupId>org.jasig.cas.client</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- junrar文件解压 -->
    <dependency>
      <groupId>com.github.junrar</groupId>
      <artifactId>junrar</artifactId>
    </dependency>

    <!--cas的客户端 -->
    <dependency>
      <groupId>org.jasig.cas.client</groupId>
      <artifactId>cas-client-core</artifactId>
    </dependency>

    <!--webservice发布调用-->
    <dependency>
      <groupId>org.apache.axis</groupId>
      <artifactId>axis</artifactId>
    </dependency>
    <dependency>
      <groupId>axis</groupId>
      <artifactId>axis-jaxrpc</artifactId>
    </dependency>
    <dependency>
      <groupId>axis</groupId>
      <artifactId>axis-wsdl4j</artifactId>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>com.sgcc.epri.auth</groupId>-->
<!--      <artifactId>sso-client-base</artifactId>-->
<!--    </dependency>-->

    <!--poi 解析excel-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--    <dependency>-->
    <!--      <groupId>org.apache.poi</groupId>-->
    <!--      <artifactId>poi</artifactId>-->
    <!--    </dependency>-->
    <!--    <dependency>-->
    <!--      <groupId>org.apache.poi</groupId>-->
    <!--      <artifactId>poi-ooxml-schemas</artifactId>-->
    <!--    </dependency>-->
    <!--    <dependency>-->
    <!--      <groupId>org.apache.poi</groupId>-->
    <!--      <artifactId>poi-ooxml</artifactId>-->
    <!--    </dependency>-->


    <!-- dom4j 解析xml -->
    <dependency>
      <groupId>org.dom4j</groupId>
      <artifactId>dom4j</artifactId>
      <version>2.1.1</version>
    </dependency>
    <dependency>
      <groupId>com.tsieframework.cloud.security</groupId>
      <artifactId>tsie-cloud-security-service-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <exclusions>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tsintergy</groupId>
      <artifactId>tsie-operation-exporter-starter</artifactId>
      <version>1.0.0</version>
    </dependency>
  </dependencies>
</project>