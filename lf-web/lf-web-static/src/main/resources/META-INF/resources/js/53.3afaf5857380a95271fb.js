"use strict";(self.webpackChunkload_forecast_fe=self.webpackChunkload_forecast_fe||[]).push([[53],{56053:function(e,t,s){s.r(t);var r=s(75947),n=s(16550),a=s(67294),c=s(85893);function i(e,t){return d(e)||f(e,t)||u(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return o(e,t);var s=Object.prototype.toString.call(e).slice(8,-1);return"Object"===s&&e.constructor&&(s=e.constructor.name),"Map"===s||"Set"===s?Array.from(e):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var s=0,r=new Array(t);s<t;s++)r[s]=e[s];return r}function f(e,t){var s=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=s){var r=[],n=!0,a=!1,c,i;try{for(s=s.call(e);!(n=(c=s.next()).done)&&(r.push(c.value),!t||r.length!==t);n=!0);}catch(e){a=!0,i=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw i}}return r}}function d(e){if(Array.isArray(e))return e}var b={height:"100%",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"};t.default=function(){var e=(0,n.k6)(),t=(0,n.TH)(),s,l,u=i((0,r.aC)(),1)[0],o=u.isLogin,f=u.refreshAuth;return a.useEffect((function(){if(f)window.location.reload();else{var s=t.search.split("?defaultRoute=");o&&e.push(s[1]),window.location.reload()}}),[e,o,f,t]),(0,c.jsxs)("div",{style:b,children:[(0,c.jsxs)("div",{className:"init-sk-cube-grid",children:[(0,c.jsx)("div",{className:"sk-cube sk-cube-1"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-2"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-3"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-4"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-5"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-6"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-7"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-8"}),(0,c.jsx)("div",{className:"sk-cube sk-cube-9"})]}),(0,c.jsx)("div",{className:"loading-text",children:"\u52a0\u8f7d\u4e2d..."})]})}}}]);