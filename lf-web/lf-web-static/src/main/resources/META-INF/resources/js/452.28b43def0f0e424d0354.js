"use strict";(self.webpackChunkload_forecast_fe=self.webpackChunkload_forecast_fe||[]).push([[452],{66613:function(t,e,r){r.d(e,{Z:function(){return h}});var n=r(91894),o=r(71577),a=r(67294),i=r(83675),c=r(84666),u=r(67923),l={loading:"NewModuleCard--loading--Vz0rm_q","empty-box":"NewModuleCard--empty-box--INyoet7","title-sign":"NewModuleCard--title-sign--o6u2UTY",wrap:"NewModuleCard--wrap--_REi7u4",body:"NewModuleCard--body--OplioED",title:"NewModuleCard--title--Kxv793_"},s=r(85893),d,f,h=function t(e){var r=e.children,c=e.title,d=e.extra,f=e.bordered,h=void 0!==f&&f,m=e.loading,p=e.isWrap,y=void 0!==p&&p,v=e.retCode,g=void 0===v?"T200":v,x=e.refreshAction,b=e.compact,w=void 0!==b&&b,j=e.theme,S=void 0===j?"light":j,L=e.noBg,k=void 0!==L&&L,E=(0,a.useMemo)((function(){var t="0px 12px 12px 12px";return y?t=0:w&&(t="0px 24px 6px 24px"),{padding:t,opacity:m?.5:1,height:c?"calc(100% - 40px)":"100%"}}),[w,y,m,c]),A=(0,a.useMemo)((function(){return c?(0,s.jsx)("span",{className:l["title-sign"],children:c}):null}),[c]);return(0,s.jsxs)("div",{style:{height:"100%",position:"relative"},children:[m&&(0,s.jsx)("div",{className:l.loading,children:(0,s.jsx)(u.Z,{theme:S})}),(0,s.jsxs)(n.Z,{title:A,extra:d,bordered:h,bodyStyle:E,style:{height:"100%",border:"1px solid #326C90",background:"rgba(165, 175, 183, 0)"},className:l.card,children:[!m&&"T200"===g&&r,!m&&"T200"!==g&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:l["empty-box"],children:[(0,s.jsx)("img",{src:i}),x&&(0,s.jsx)(o.default,{type:"primary",onClick:x,children:"\u5237\u65b0"})]})})]})]})}},42081:function(t,e,r){var n=r(54377),o=r(32586),a=r(8690),i=r(17813),c=r(31281),u=r(93450),l=r(68023),s=r(81615),d=r(70012),f=r(67294),h=["autoResize","debounceDelay"];function m(t,e){if(null==t)return{};var r=p(t,e),n,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function p(t,e){if(null==t)return{};var r={},n=Object.keys(t),o,a;for(a=0;a<n.length;a++)o=n[a],e.indexOf(o)>=0||(r[o]=t[o]);return r}function y(t,e){return w(t)||b(t,e)||g(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return x(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?x(t,e):void 0}}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n=[],o=!0,a=!1,i,c;try{for(r=r.call(t);!(o=(i=r.next()).done)&&(n.push(i.value),!e||n.length!==e);o=!0);}catch(t){a=!0,c=t}finally{try{o||null==r.return||r.return()}finally{if(a)throw c}}return n}}function w(t){if(Array.isArray(t))return t}function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function S(t,e){if(t===e)return!0;if(null==t||null==e)return!1;if(j(t)!==j(e))return!1;if("object"!==j(t))return t===e;var r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(var o=0,a=r;o<a.length;o++){var i=a[o];if(!n.includes(i)||!S(t[i],e[i]))return!1}return!0}l.D([a.N,i.N,c.N,u.N,n.N,o.N,d.N]);var L=function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(0,f.useRef)(null),o=(0,f.useRef)(null),a=(0,f.useRef)(null),i=(0,f.useRef)(null),c=(0,f.useRef)(!0),u=(0,f.useRef)(null),l=(0,f.useRef)(!1),d=(0,f.useState)(!1),p=y(d,2),v=p[0],g=p[1],x=(0,f.useState)(null),b=y(x,2),w=b[0],j=b[1],L=r.autoResize,k=void 0===L||L,E=r.debounceDelay,A=void 0===E?100:E,C=m(r,h),D=(0,f.useCallback)((function(t){return!u.current||!S(t,u.current)}),[]),M=(0,f.useCallback)((function(){a.current&&clearTimeout(a.current),a.current=setTimeout((function(){o.current&&!o.current.isDisposed()&&c.current&&o.current.resize()}),100)}),[]),O=(0,f.useCallback)((function(){a.current&&(clearTimeout(a.current),a.current=null),i.current&&(clearTimeout(i.current),i.current=null),k&&window.removeEventListener("resize",M),o.current&&!o.current.isDisposed()&&(o.current.dispose(),o.current=null)}),[k,M]),N=(0,f.useCallback)((function(t,e){D(t)&&(i.current&&clearTimeout(i.current),i.current=setTimeout((function(){if(o.current&&!o.current.isDisposed()&&c.current)try{o.current.setOption(t,e),u.current=JSON.parse(JSON.stringify(t)),j(null)}catch(t){j(t instanceof Error?t.message:"\u56fe\u8868\u66f4\u65b0\u5931\u8d25")}}),A))}),[D,A]),I=(0,f.useCallback)((function(){if(n.current&&c.current&&!l.current)try{l.current=!0,g(!0),j(null),o.current&&!o.current.isDisposed()&&(o.current.dispose(),o.current=null);var t=s.S1(n.current);o.current=t,t.setOption(e,!0),u.current=JSON.parse(JSON.stringify(e)),C.onClick&&t.on("click",C.onClick),C.onDoubleClick&&t.on("dblclick",C.onDoubleClick),C.onMouseOver&&t.on("mouseover",C.onMouseOver),C.onMouseOut&&t.on("mouseout",C.onMouseOut),C.onLegendSelectChanged&&t.on("legendselectchanged",C.onLegendSelectChanged),C.onDataZoom&&t.on("datazoom",C.onDataZoom),C.onRendered&&t.on("rendered",C.onRendered),C.onFinished&&t.on("finished",C.onFinished),k&&window.addEventListener("resize",M),g(!1)}catch(t){j(t instanceof Error?t.message:"\u56fe\u8868\u521d\u59cb\u5316\u5931\u8d25"),g(!1)}finally{l.current=!1}}),[e,C,k,M]),P=(0,f.useCallback)((function(t,e){if(o.current&&!o.current.isDisposed()&&c.current)try{o.current.setOption(t,e),u.current=JSON.parse(JSON.stringify(t)),j(null)}catch(t){j(t instanceof Error?t.message:"\u56fe\u8868\u66f4\u65b0\u5931\u8d25")}}),[]),T=(0,f.useCallback)((function(){o.current&&!o.current.isDisposed()&&c.current&&o.current.resize()}),[]),Z=(0,f.useCallback)((function(t){o.current&&!o.current.isDisposed()&&c.current&&o.current.showLoading("default",{text:t||"\u52a0\u8f7d\u4e2d...",color:"#4285f4",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",fontSize:12})}),[]),R=(0,f.useCallback)((function(){o.current&&!o.current.isDisposed()&&c.current&&o.current.hideLoading()}),[]);return(0,f.useEffect)((function(){return c.current=!0,I(),function(){c.current=!1,O()}}),[]),(0,f.useEffect)((function(){var t=null;if(n.current){o.current&&!o.current.isDisposed()||I();var e=n.current.parentElement;if(e){var r=null;(t=new MutationObserver((function(t){r&&clearTimeout(r),r=setTimeout((function(){t.forEach((function(t){"childList"===t.type&&n.current&&document.contains(n.current)&&(o.current&&!o.current.isDisposed()||setTimeout((function(){c.current&&I()}),0))}))}),200)}))).observe(e,{childList:!0,subtree:!0})}}return function(){t&&t.disconnect()}}),[I]),(0,f.useEffect)((function(){o.current&&!o.current.isDisposed()&&c.current&&N(e,!0)}),[e,N]),{chartRef:n,chartInstance:o.current,isLoading:v,error:w,updateChart:P,resizeChart:T,showLoading:Z,hideLoading:R}};e.Z=L},14994:function(t,e,r){r.d(e,{Z:function(){return F}});var n=r(68628),o=r(22245),a=r(28406),i=r(89456),c=r(29730),u=r(71230),l=r(15746),s=r(71577),d=r(89395),f=r(67294),h=r(66613),m=r(42081),p="AlgorithmDetail--detailLayoutLeft--l_CO_gO",y="AlgorithmDetail--detailCardTop--xmLXqMV",v="AlgorithmDetail--detailCardBottom--eycJidk",g="AlgorithmDetail--detailLayoutRight--vias3T_",x="AlgorithmDetail--detailContainer--ugdimeo",b="AlgorithmDetail--detailTopRow--XjvjdJa",w="AlgorithmDetail--detailBottomRow--ItQ1gRR",j="AlgorithmDetail--ant-card-body--yHXpNuX",S="AlgorithmDetail--detailSection--R1qG4x1",L="AlgorithmDetail--advantagesBox--asD0veL",k="AlgorithmDetail--advantagesText--qHml6PH",E="AlgorithmDetail--shine--ePWsGGy",A=r(85893);function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach((function(e){M(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function M(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(t,e){return Z(t)||T(t,e)||I(t,e)||N()}function N(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(t,e){if(t){if("string"==typeof t)return P(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(t,e):void 0}}function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function T(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n=[],o=!0,a=!1,i,c;try{for(r=r.call(t);!(o=(i=r.next()).done)&&(n.push(i.value),!e||n.length!==e);o=!0);}catch(t){a=!0,c=t}finally{try{o||null==r.return||r.return()}finally{if(a)throw c}}return n}}function Z(t){if(Array.isArray(t))return t}var R,F=function t(e){var r=e.algorithmAccuracyData,j=e.algorithmParameterData,L=e.algorithmDetail,k=e.onBack,E=e.runSaveAlgorithmParameter,C,M,N=O(o.default.useForm(),1)[0],I,P=O((0,f.useState)({}),2),T=P[0],Z=P[1],R,F=O((0,f.useState)(!1),2),z=F[0],G=F[1];(0,f.useEffect)((function(){if(j&&j.length>0){var t={};j.filter((function(t){return"1"===t.belongType})).forEach((function(e){t[e.paramName]=e.paramValue})),N.setFieldsValue(t),Z(t),G(!1)}}),[j,N]);var _=j.filter((function(t){return"0"===t.belongType}))||[],B=j.filter((function(t){return"1"===t.belongType}))||[],Y=function t(e){var r={},n=!1;Object.entries(e).forEach((function(t){var e=O(t,2),o=e[0],a=e[1],i=j.find((function(t){return t.paramName===o}));i&&"1"===i.belongType&&(r[o]=a,n=!0)})),n&&(G(!0),Z((function(t){return D(D({},t),r)})))},V=function t(){a.default.confirm({title:"\u786e\u8ba4\u4fdd\u5b58",content:"\u786e\u5b9a\u8981\u4fdd\u5b58\u5f53\u524d\u7684\u53c2\u6570\u914d\u7f6e\u5417\uff1f",onOk:function t(){E(B.map((function(t){return D(D({},t),{},{paramValue:T[t.paramName]})}))),G(!1)}})},W=(0,f.useMemo)((function(){return r?{tooltip:{trigger:"axis",axisPointer:{type:"line"},borderColor:"rgba(64, 169, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.7)",textStyle:{color:"#fff"},formatter:function t(e){var r=e[0];return"".concat(r.name,"<br/>\u51c6\u786e\u7387\uff1a").concat(r.value,"%")}},xAxis:{type:"category",data:null==r?void 0:r.map((function(t){return t.date})),axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},axisLabel:{color:"rgba(221, 234, 243, 1)",interval:0,rotate:35,fontSize:12}},yAxis:{type:"value",name:"\u51c6\u786e\u7387\uff08%\uff09",nameTextStyle:{color:"rgba(221, 234, 243, 1)",fontSize:12},axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},axisLabel:{color:"rgba(221, 234, 243, 1)",formatter:"{value}%",fontSize:12},splitLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)",type:"dashed"}},min:90,max:100},series:[{name:"\u51c6\u786e\u7387",type:"bar",data:null==r?void 0:r.map((function(t){return t.accuracy})),itemStyle:{color:new d.Q.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(24, 144, 255, 1)"},{offset:1,color:"rgba(64, 169, 255, 1)"}])},barWidth:"50%",label:{show:!0,position:"top",formatter:"{c}%",color:"rgba(221, 234, 243, 1)",fontSize:11}}],grid:{left:"8%",right:"5%",bottom:"25%",top:"15%",containLabel:!1}}:{}}),[r]),J,X=(0,m.Z)(W).chartRef,H=function t(e){return(0,A.jsx)("div",{style:{marginBottom:16},children:(0,A.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:8,fontSize:14,fontWeight:500,color:"#fff"},children:[(0,A.jsxs)("div",{children:[(0,A.jsx)("span",{style:{marginRight:8},children:"\u25cf"}),e.paramName,"\uff1a",(0,A.jsx)("span",{style:{color:"#1890ff"},children:e.paramValue})]}),(0,A.jsx)("div",{children:e.type})]})},e.id)},q=function t(e){return(0,A.jsx)(o.default.Item,{label:(0,A.jsxs)("span",{children:[e.details,(0,A.jsx)(i.default,{title:e.details,children:(0,A.jsx)(n.Z,{style:{marginLeft:4,color:"#1890ff"}})})]}),name:e.paramName,style:{marginBottom:16},children:(0,A.jsx)(c.default,{})},e.id)};return(0,A.jsxs)("div",{className:x,children:[(0,A.jsxs)(u.default,{gutter:[16,0],className:b,children:[(0,A.jsx)(l.default,{span:9,children:(0,A.jsxs)("div",{className:p,children:[(0,A.jsx)("div",{className:y,children:(0,A.jsx)(h.Z,{title:"\u6a21\u578b\u8be6\u60c5\u4ecb\u7ecd",children:(0,A.jsx)("div",{className:S,children:(0,A.jsx)("p",{children:null==L?void 0:L.details})})})}),(0,A.jsx)("div",{className:v,children:(0,A.jsx)(h.Z,{title:"\u5e94\u7528\u4f18\u52bf",children:(0,A.jsx)("div",{className:S,children:(0,A.jsx)("p",{className:"scanning-text",children:null==L?void 0:L.advantage})})})})]})}),(0,A.jsx)(l.default,{span:15,children:(0,A.jsx)("div",{className:g,children:(0,A.jsx)(h.Z,{title:"\u6a21\u578b\u51c6\u786e\u7387 - \u8fd115\u5929",children:(0,A.jsx)("div",{ref:X,style:{height:"100%",width:"100%"}})})})})]}),(0,A.jsx)(u.default,{className:w,children:(0,A.jsx)(l.default,{span:24,children:(0,A.jsx)(h.Z,{title:"\u53c2\u6570\u8be6\u60c5",extra:z&&(0,A.jsx)(s.default,{type:"primary",onClick:V,children:"\u4fdd\u5b58\u914d\u7f6e"}),children:(0,A.jsx)("div",{className:S,children:(0,A.jsxs)(u.default,{gutter:[24,0],children:[(0,A.jsx)(l.default,{span:12,children:(0,A.jsxs)("div",{style:{paddingRight:16,minHeight:400},children:[(0,A.jsx)("h4",{style:{marginBottom:16,fontSize:16,fontWeight:600,color:"#fff"},children:"\u63a7\u5236\u53c2\u6570"}),(0,A.jsxs)("div",{children:[_.map(H),0===_.length&&(0,A.jsx)("div",{style:{color:"#fff",textAlign:"center",padding:"40px 0"},children:"\u6682\u65e0\u63a7\u5236\u53c2\u6570"})]})]})}),(0,A.jsx)(l.default,{span:12,children:(0,A.jsxs)("div",{style:{paddingLeft:16},children:[(0,A.jsx)("h4",{style:{marginBottom:16,fontSize:16,fontWeight:600,color:"#fff"},children:"\u7b97\u6cd5\u53c2\u6570"}),(0,A.jsxs)(o.default,{form:N,layout:"vertical",onValuesChange:Y,children:[B.map(q),0===B.length&&(0,A.jsx)("div",{style:{color:"#fff",textAlign:"center",padding:"40px 0"},children:"\u6682\u65e0\u7b97\u6cd5\u53c2\u6570"})]})]})})]})})})})})]})}},5452:function(t,e,r){r.r(e),r.d(e,{default:function(){return H}});var n=r(22245),o=r(71577),a=r(34041),i=r(11448),c=r(71230),u=r(15746),l=r(56204),s=r(89395),d=r(25754),f=r(4251),h=r(30381),m=r.n(h),p=r(67294),y=r(66613),v=r(42081),g=r(98733),x=r(14994),b=r(65826),w=r(74428),j=r(48086),S=r(7770);function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function k(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */k=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",i=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function u(t,e,r,n){var o=e&&e.prototype instanceof d?e:d,a=Object.create(o.prototype),i=new S(n||[]);return a._invoke=function(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return A()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var c=b(i,r);if(c){if(c===s)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=l(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===s)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(t,r,i),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var s={};function d(){}function f(){}function h(){}var m={};c(m,o,(function(){return this}));var p=Object.getPrototypeOf,y=p&&p(p(E([])));y&&y!==e&&r.call(y,o)&&(m=y);var v=h.prototype=d.prototype=Object.create(m);function g(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(o,a,i,c){var u=l(t[o],t,a);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==L(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,i,c)}),(function(t){n("throw",t,i,c)})):e.resolve(d).then((function(t){s.value=t,i(s)}),(function(t){return n("throw",t,i,c)}))}c(u.arg)}var o;this._invoke=function(t,r){function a(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(a,a):a()}}function b(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,b(t,e),"throw"===e.method))return s;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return s}var n=l(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,s;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,s):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,s)}function w(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(w,this),this.reset(!0)}function E(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return a.next=a}}return{next:A}}function A(){return{value:void 0,done:!0}}return f.prototype=h,c(v,"constructor",h),c(h,"constructor",f),f.displayName=c(h,i,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,i,"GeneratorFunction")),t.prototype=Object.create(v),t},t.awrap=function(t){return{__await:t}},g(x.prototype),c(x.prototype,a,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new x(u(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},g(v),c(v,i,"Generator"),c(v,o,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=E,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return c.type="throw",c.arg=e,n.next=t,r&&(n.method="next",n.arg=void 0),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),l=r.call(i,"finallyLoc");if(u&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=n&&n<=i.finallyLoc&&(i=null);var c=i?i.completion:{};return c.type=e,c.arg=n,i?(this.method="next",this.next=i.finallyLoc,s):this.complete(c)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),s},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),s}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;j(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:E(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=void 0),s}},t}function E(t,e,r,n,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function A(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){E(a,n,o,i,c,"next",t)}function c(t){E(a,n,o,i,c,"throw",t)}i(void 0)}))}}var C=function t(e){var r=n.default.useWatch("cityId",e),o=(0,w.Z)(A(k().mark((function t(){var r,n,o,a;return k().wrap((function t(i){for(;;)switch(i.prev=i.next){case 0:return r=e.getFieldsValue(),n=r.cityId,o=r.ym,i.next=3,D({cityId:n,ym:o.format("YYYY-MM")});case 3:return a=i.sent,i.abrupt("return",a.data);case 5:case"end":return i.stop()}}),t)}))),{manual:!1,ready:!!r,refreshDeps:[r]}),a=o.run,i=o.data,c=o.loading,u=(0,w.Z)(A(k().mark((function t(){var r,n,o,a;return k().wrap((function t(i){for(;;)switch(i.prev=i.next){case 0:return r=e.getFieldsValue(),n=r.cityId,o=r.ym,i.next=3,M({cityId:n,ym:o.format("YYYY-MM")});case 3:return a=i.sent,i.abrupt("return",a.data);case 5:case"end":return i.stop()}}),t)}))),{manual:!1,ready:!!r,refreshDeps:[r]}),l=u.run,s=u.data,d=u.loading,f=(0,w.Z)(A(k().mark((function t(){var r,n,o;return k().wrap((function t(a){for(;;)switch(a.prev=a.next){case 0:return r=e.getFieldsValue(),n=r.cityId,a.next=3,T({cityId:n});case 3:return o=a.sent,a.abrupt("return",o.data);case 5:case"end":return a.stop()}}),t)}))),{manual:!1,ready:!!r,refreshDeps:[r]}),h=f.run,m=f.data,p=(0,w.Z)(function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,O(e);case 2:return r=n.sent,n.abrupt("return",r.data);case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),{manual:!0}),y=p.data,v=p.run,g=p.loading,x=(0,w.Z)(function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,N(e);case 2:return r=n.sent,n.abrupt("return",r.data);case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),{manual:!0}),b=x.data,S=x.run,L=(0,w.Z)(function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,I(e);case 2:return r=n.sent,n.abrupt("return",r.data);case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),{manual:!0}),E=L.data,C=L.run,Z=(0,w.Z)(function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,P(e);case 2:"T200"===(r=n.sent).retCode&&j.default.success("\u4fdd\u5b58\u6210\u529f");case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),{manual:!0}),R;return{monthForecastData:i,algorithmDetailData:y,algorithmDetailLoading:g,algorithmParameterData:b,monthForecastLoading:c,weatherForecastLoading:d,algorithmAccuracyData:m,runGetAlgorithmDetail:v,runGetAlgorithmParameter:S,runGetAlgorithmAccuracy:h,runSaveAlgorithmParameter:Z.run,weatherForecastData:s,runGetWeatherForecast:l,runGetMonthForecast:a,modelAccuracyData:E,runGetModelAccuracy:C}},D=function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,S.Z)({url:"".concat("web","/month/forecast/weatherInfo"),method:"GET",params:e});case 2:return r=n.sent,n.abrupt("return",r);case 4:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),M=function(){var t=A(k().mark((function t(e){var r;return k().wrap((function t(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,S.Z)({url:"".concat("web","/month/forecast/weatherFc"),method:"GET",params:e});case 2:return r=n.sent,n.abrupt("return",r);case 4:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),O=function(){var t=A(k().mark((function t(e){return k().wrap((function t(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,S.Z.get("".concat("web","/middle/algorithmManagement/getAlgorithmDetails?algorithmName=").concat(e));case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),N=function(){var t=A(k().mark((function t(e){return k().wrap((function t(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,S.Z.get("".concat("web","/middle/algorithmManagement/getParamDetails?algorithmName=").concat(e));case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),I=function(){var t=A(k().mark((function t(e){return k().wrap((function t(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,S.Z.get("".concat("web","/month/forecast/getModelAccuracy"),{params:e});case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),P=function(){var t=A(k().mark((function t(e){return k().wrap((function t(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,S.Z.post("".concat("web","/middle/algorithmManagement/editParamDetails"),e);case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),T=function(){var t=A(k().mark((function t(e){return k().wrap((function t(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,S.Z.get("".concat("web","/month/forecast/getHistoryAccuracy"),{params:e});case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}(),Z=r(85893);function R(t){return G(t)||z(t)||Y(t)||F()}function F(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function z(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function G(t){if(Array.isArray(t))return V(t)}function _(t,e){return J(t)||W(t,e)||Y(t,e)||B()}function B(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Y(t,e){if(t){if("string"==typeof t)return V(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?V(t,e):void 0}}function V(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function W(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n=[],o=!0,a=!1,i,c;try{for(r=r.call(t);!(o=(i=r.next()).done)&&(n.push(i.value),!e||n.length!==e);o=!0);}catch(t){a=!0,c=t}finally{try{o||null==r.return||r.return()}finally{if(a)throw c}}return n}}function J(t){if(Array.isArray(t))return t}var X=d.E.div;function H(){var t,e,r,d=_(n.default.useForm(),1)[0],h,v,w=_((0,g.Z)(["cityList"]),1)[0],j,S=_((0,p.useState)("list"),2),L=S[0],k=S[1],E=C(d),A=E.monthForecastData,D=E.algorithmDetailData,M=E.runGetAlgorithmDetail,O=E.algorithmParameterData,N=E.runGetAlgorithmParameter,I=E.algorithmAccuracyData,P=E.runGetAlgorithmAccuracy,T=E.runSaveAlgorithmParameter,F=E.runGetMonthForecast,z=E.weatherForecastData,G=E.modelAccuracyData,B=E.runGetModelAccuracy,Y=E.runGetWeatherForecast;(0,p.useEffect)((function(){var t="progress-bar-animation-style";if(!document.getElementById(t)){var e=document.createElement("style");e.id=t,e.innerHTML="\n        @keyframes shimmer {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n        .progress-bar-animated::after {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);\n          animation: shimmer 1.5s infinite linear;\n        }\n      ",document.head.appendChild(e)}return function(){var e=document.getElementById(t),r;e&&(null===(r=e.parentNode)||void 0===r||r.removeChild(e))}}),[]);var V=(0,p.useMemo)((function(){var t,e,r,n;return A?{tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(0, 240, 255, 0.3)",textStyle:{color:"#fff"},axisPointer:{type:"cross",lineStyle:{color:"#1fdaf6"},crossStyle:{color:"#1fdaf6"},label:{backgroundColor:"rgba(27, 212, 123, 0.9)"}}},legend:{data:["\u540c\u671f\u5b9e\u9645\u6700\u5927\u8d1f\u8377","\u4e0a\u6708\u5b9e\u9645\u6700\u5927\u8d1f\u8377"],textStyle:{color:"rgba(221, 234, 243, 1)"},top:"2%",icon:"path://M4 2C2.89551 2 2 2.89551 2 4C2 5.10449 2.89551 6 4 6C5.10449 6 6 5.10449 6 4C6 2.89551 5.10449 2 4 2Z,M0 4C0 6.20923 1.79102 8 4 8C6.20898 8 8 6.20923 8 4C8 1.79077 6.20898 0 4 0C1.79102 0 0 1.79077 0 4ZM4 7C2.34277 7 1 5.65674 1 4C1 2.34326 2.34277 1 4 1C5.65723 1 7 2.34326 7 4C7 5.65674 5.65723 7 4 7Z"},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",data:null==A?void 0:A.map((function(t){return t.type})),axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},axisLabel:{color:"##DDEAF3"}},yAxis:{axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},min:(.9*Math.min.apply(Math,R(null!==(t=null==A?void 0:A.map((function(t){var e;return null!==(e=t.yoyMaxLoad)&&void 0!==e?e:0})))&&void 0!==t?t:[]).concat(R(null!==(e=null==A?void 0:A.map((function(t){var e;return null!==(e=t.dodMaxLoad)&&void 0!==e?e:0})))&&void 0!==e?e:[])))).toFixed(0),max:(1.1*Math.max.apply(Math,R(null!==(r=null==A?void 0:A.map((function(t){var e;return null!==(e=t.yoyMaxLoad)&&void 0!==e?e:0})))&&void 0!==r?r:[]).concat(R(null!==(n=null==A?void 0:A.map((function(t){var e;return null!==(e=t.dodMaxLoad)&&void 0!==e?e:0})))&&void 0!==n?n:[])))).toFixed(0),splitLine:{lineStyle:{dashStyle:"dashed",color:"rgba(0, 240, 255, 0.3)"}},axisLabel:{color:"rgba(221, 234, 243, 1)"},type:"value"},series:[{name:"\u540c\u671f\u5b9e\u9645\u6700\u5927\u8d1f\u8377",type:"line",smooth:!0,lineStyle:{width:2,color:"#1890ff"},symbol:"none",itemStyle:{color:"#1890ff"},data:null==A?void 0:A.map((function(t){return t.yoyMaxLoad}))},{name:"\u4e0a\u6708\u5b9e\u9645\u6700\u5927\u8d1f\u8377",type:"line",smooth:!0,lineStyle:{width:2,color:"#52c41a"},symbol:"none",itemStyle:{color:"#52c41a"},data:null==A?void 0:A.map((function(t){return t.dodMaxLoad}))}]}:{}}),[A]),W=(0,p.useMemo)((function(){var t,e;if(!z)return{};var r=Math.min.apply(Math,R(null!==(t=null==z?void 0:z.map((function(t){var e;return null!==(e=t.highestTemperature)&&void 0!==e?e:0})))&&void 0!==t?t:[])),n=Math.max.apply(Math,R(null!==(e=null==z?void 0:z.map((function(t){var e;return null!==(e=t.highestTemperature)&&void 0!==e?e:0})))&&void 0!==e?e:[]));return{tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(0, 240, 255, 0.3)",textStyle:{color:"#fff"},axisPointer:{type:"cross",lineStyle:{color:"#1fdaf6"},crossStyle:{color:"#1fdaf6"},label:{backgroundColor:"rgba(27, 212, 123, 0.9)"}}},xAxis:[{type:"category",data:null==z?void 0:z.map((function(t){return t.date})),axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},axisLabel:{color:"##DDEAF3"}}],yAxis:[{type:"value",name:"\u6c14\u6e29\uff08\u2103\uff09",nameTextStyle:{color:"rgba(221, 234, 243, 1)",fontSize:12},min:(.9*r).toFixed(0),max:(1.1*n).toFixed(0),scale:!0,position:"left",axisLine:{lineStyle:{color:"rgba(0, 240, 255, 0.3)"}},splitLine:{show:!1},axisLabel:{color:"rgba(221, 234, 243, 1)"}}],series:[{name:"\u6c14\u6e29",type:"line",yAxisIndex:0,data:null==z?void 0:z.map((function(t){return t.highestTemperature})),smooth:!0,connectNulls:!0,symbol:"none",itemStyle:{color:"rgba(31, 226, 255, 1)"},areaStyle:{color:new s.Q.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(31, 226, 255, 0.5)"},{offset:1,color:"rgba(31, 226, 255, 0)"}])},emphasis:{focus:"series",lineStyle:{width:2}},animationDelay:function t(e){return 100*e},animationDuration:2e3,animationEasing:"cubicOut",lineStyle:{width:2,color:"rgba(31, 226, 255, 1)"}}],grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0}}}),[z]),J=(0,p.useMemo)((function(){return(null==A?void 0:A.reduce((function(t,e){var r,n,o,a,i,c,u,l,s=e.type;return t.push({key:"".concat(e.type,"-1"),period:s,item:"\u6700\u5927\u8d1f\u8377",forecast:e.maxLoad,actualSamePeriod:e.yoyMaxLoad,yoy:"".concat(null!==(r=e.maxLoadYoy)&&void 0!==r?r:"--","%"),actualLastMonth:e.dodMaxLoad,mom:"".concat(null!==(n=e.maxLoadDod)&&void 0!==n?n:"--","%")},{key:"".concat(e.type,"-2"),period:s,item:"\u6700\u5c0f\u8d1f\u8377",forecast:e.minLoad,actualSamePeriod:e.yoyMinLoad,yoy:"".concat(null!==(o=e.minLoadYoy)&&void 0!==o?o:"--","%"),actualLastMonth:e.dodMinLoad,mom:"".concat(null!==(a=e.minLoadDod)&&void 0!==a?a:"--","%")},{key:"".concat(e.type,"-3"),period:s,item:"\u6700\u5c0f\u8170\u8377",forecast:e.noontimeMinLoad,actualSamePeriod:e.yoyNoontimeMinLoad,yoy:"".concat(null!==(i=e.noontimeMinLoadYoy)&&void 0!==i?i:"--","%"),actualLastMonth:e.dodNoontimeMinLoad,mom:"".concat(null!==(c=e.noontimeMinLoadDod)&&void 0!==c?c:"--","%")},{key:"".concat(e.type,"-4"),period:s,item:"\u65ec\u7535\u91cf",forecast:e.tenEnergy,actualSamePeriod:e.yoyTenEnergy,yoy:"".concat(null!==(u=e.tenEnergyYoy)&&void 0!==u?u:"--","%"),actualLastMonth:e.dodTenEnergy,mom:"".concat(null!==(l=e.tenEnergyDod)&&void 0!==l?l:"--","%")}),t}),[]))||[]}),[A]),H,Q,$=[{title:"",dataIndex:"period",render:function t(e,r,n){return n<4?"\u4e0a\u65ec":n<8?"\u4e2d\u65ec":"\u4e0b\u65ec"},onCell:function t(e,r){return void 0===r?{}:r%4!=0?{rowSpan:0}:{rowSpan:4}}},{title:"\u6570\u636e\u9879",dataIndex:"item",onCell:function t(){return{colSpan:1}}},{title:"\u9884\u6d4b\u503c",dataIndex:"forecast"},{title:"\u540c\u671f\u5b9e\u9645",dataIndex:"actualSamePeriod"},{title:"\u540c\u6bd4",dataIndex:"yoy"},{title:"\u4e0a\u6708\u5b9e\u9645",dataIndex:"actualLastMonth"},{title:"\u73af\u6bd4",dataIndex:"mom"}],K=[{title:"\u6392\u540d",dataIndex:"rank",width:60,align:"center",render:function t(e,r,n){return n+1}},{title:"\u7b97\u6cd5",dataIndex:"algorithmName",width:120},{title:"\u51c6\u786e\u7387",dataIndex:"maxLoad",render:function t(e){return(0,Z.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,Z.jsx)("div",{style:{flex:1,paddingRight:8},children:(0,Z.jsx)("div",{className:"progress-bar-animated",style:{width:"".concat(e,"%"),height:10,background:"linear-gradient(90deg, #91c7ff, #1890ff)",borderRadius:5,transition:"width 0.5s ease-in-out",position:"relative",overflow:"hidden"}})}),(0,Z.jsxs)("span",{style:{width:50},children:[e,"%"]})]})}},{title:"\u64cd\u4f5c",key:"action",width:80,align:"center",render:function t(e,r){return(0,Z.jsx)(o.default,{type:"link",onClick:function t(){k("detail"),M(r.algorithmName),N(r.algorithmName);var e=d.getFieldValue("cityId");B({cityId:e,algorithmName:r.algorithmName})},children:"\u8be6\u60c5"})}}];return(0,Z.jsx)("div",{children:"list"===L?(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(y.Z,{children:(0,Z.jsxs)(n.default,{layout:"inline",form:d,initialValues:{cityId:null===(t=w[0])||void 0===t?void 0:t.value,ym:m()((0,b.Dt)()).add(1,"m")},children:[(0,Z.jsx)(n.default.Item,{label:"\u5730\u533a",name:"cityId",children:(0,Z.jsx)(a.default,{options:w,style:{width:200}})}),(0,Z.jsx)(n.default.Item,{label:"\u65e5\u671f",name:"ym",children:(0,Z.jsx)(i.default,{picker:"month"})}),(0,Z.jsx)(n.default.Item,{children:(0,Z.jsx)(o.default,{type:"primary",onClick:function t(){F(),P(),Y()},children:"\u67e5\u8be2"})})]})}),(0,Z.jsxs)(c.default,{gutter:[16,16],style:{marginTop:"16px"},children:[(0,Z.jsx)(u.default,{span:12,children:(0,Z.jsx)("div",{style:{height:"100%"},children:(0,Z.jsxs)(y.Z,{title:"\u6708\u9884\u6d4b\u6982\u89c8",children:[(0,Z.jsx)(q,{option:V}),(0,Z.jsx)(l.default,{columns:$,dataSource:J,bordered:!0,size:"small",pagination:!1,style:{marginTop:"16px"}})]})})}),(0,Z.jsxs)(u.default,{span:12,style:{display:"flex",flexDirection:"column"},children:[(0,Z.jsx)("div",{style:{marginBottom:16},children:(0,Z.jsx)(y.Z,{title:"\u6c14\u8c61\u9884\u62a5",children:(0,Z.jsx)(U,{option:W})})}),(0,Z.jsx)("div",{style:{flex:1},children:(0,Z.jsx)(y.Z,{title:"\u591a\u7b97\u6cd5\u51c6\u786e\u7387",children:(0,Z.jsx)(l.default,{columns:K,dataSource:I||[],pagination:!1,rowKey:"rank",size:"small"})})})]})]})]}):(0,Z.jsx)(f.M,{exitBeforeEnter:!0,children:(0,Z.jsx)(X,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:50},transition:{duration:.3,ease:"easeInOut"},children:(0,Z.jsx)(x.Z,{algorithmAccuracyData:(null==G?void 0:G.map((function(t){return{accuracy:t.maxLoadAccuracy,date:"".concat(t.year,"-").concat(t.month,"-").concat(t.type),pass:0}})))||[],algorithmParameterData:O||[],algorithmDetail:D,onBack:function t(){return k("list")},runSaveAlgorithmParameter:T})},"detail")})})}var q=p.memo((function(t){var e=t.option,r,n=(0,v.Z)(e,{autoResize:!0}).chartRef;return(0,Z.jsx)("div",{ref:n,style:{height:280,width:"100%"}})})),U=p.memo((function(t){var e=t.option,r,n=(0,v.Z)(e,{autoResize:!0}).chartRef;return(0,Z.jsx)("div",{ref:n,style:{height:280,width:"100%"}})}))},84666:function(t,e,r){t.exports=r.p+"assets/dad28a43608e5bd18089.png"},83675:function(t,e,r){t.exports=r.p+"assets/52c56b333b5cd576bdfd.png"},4251:function(t,e,r){r.d(e,{M:function(){return b}});var n=r(97582),o=r(67294),a=r(49304),i=r(42396),c=r(58868);function u(){var t=(0,o.useRef)(!1);return(0,c.L)((function(){return t.current=!0,function(){t.current=!1}}),[]),t}function l(){var t=u(),e=(0,n.CR)((0,o.useState)(0),2),r=e[0],a=e[1],c=(0,o.useCallback)((function(){t.current&&a(r+1)}),[r]),l;return[(0,o.useCallback)((function(){return i.ZP.postRender(c)}),[c]),r]}var s=r(240),d=r(96681),f=r(76316),h=function(t){var e=t.children,r=t.initial,a=t.isPresent,i=t.onExitComplete,c=t.custom,u=t.presenceAffectsLayout,l=(0,d.h)(m),h=(0,f.M)(),p=(0,o.useMemo)((function(){return{id:h,initial:r,isPresent:a,custom:c,onExitComplete:function(t){var e,r;l.set(t,!0);try{for(var o=(0,n.XA)(l.values()),a=o.next();!a.done;a=o.next()){var c;if(!a.value)return}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}null==i||i()},register:function(t){return l.set(t,!1),function(){return l.delete(t)}}}}),u?void 0:[a]);return(0,o.useMemo)((function(){l.forEach((function(t,e){return l.set(e,!1)}))}),[a]),o.useEffect((function(){!a&&!l.size&&(null==i||i())}),[a]),o.createElement(s.O.Provider,{value:p},e)};function m(){return new Map}var p=r(25364),y=r(73226),v=function(t){return t.key||""};function g(t,e){t.forEach((function(t){var r=v(t);e.set(r,t)}))}function x(t){var e=[];return o.Children.forEach(t,(function(t){(0,o.isValidElement)(t)&&e.push(t)})),e}var b=function(t){var e=t.children,r=t.custom,i=t.initial,s=void 0===i||i,d=t.onExitComplete,f=t.exitBeforeEnter,m=t.presenceAffectsLayout,b=void 0===m||m,w,j=(0,n.CR)(l(),1)[0],S=(0,o.useContext)(p.p).forceRender;S&&(j=S);var L=u(),k=x(e),E=k,A=new Set,C=(0,o.useRef)(E),D=(0,o.useRef)(new Map).current,M=(0,o.useRef)(!0);if((0,c.L)((function(){M.current=!1,g(k,D),C.current=E})),(0,y.z)((function(){M.current=!0,D.clear(),A.clear()})),M.current)return o.createElement(o.Fragment,null,E.map((function(t){return o.createElement(h,{key:v(t),isPresent:!0,initial:!!s&&void 0,presenceAffectsLayout:b},t)})));E=(0,n.ev)([],(0,n.CR)(E),!1);for(var O=C.current.map(v),N=k.map(v),I=O.length,P=0;P<I;P++){var T=O[P];-1===N.indexOf(T)&&A.add(T)}return f&&A.size&&(E=[]),A.forEach((function(t){if(-1===N.indexOf(t)){var e=D.get(t);if(e){var n=O.indexOf(t),a=function(){D.delete(t),A.delete(t);var e=C.current.findIndex((function(e){return e.key===t}));if(C.current.splice(e,1),!A.size){if(C.current=k,!1===L.current)return;j(),d&&d()}};E.splice(n,0,o.createElement(h,{key:v(e),isPresent:!1,onExitComplete:a,custom:r,presenceAffectsLayout:b},e))}}})),E=E.map((function(t){var e=t.key;return A.has(e)?t:o.createElement(h,{key:v(t),isPresent:!0,presenceAffectsLayout:b},t)})),"production"!==a.O&&f&&E.length>1&&console.warn("You're attempting to animate multiple children within AnimatePresence, but its exitBeforeEnter prop is set to true. This will lead to odd visual behaviour."),o.createElement(o.Fragment,null,A.size?E:E.map((function(t){return(0,o.cloneElement)(t)})))}}}]);