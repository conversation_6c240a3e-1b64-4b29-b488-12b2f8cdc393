#配置job-admin数据库、端口等
tsie:
  multiple-datasource:
    configs:
      - name: dsCommon
        driverClass: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
        url: jdbc:dm://************:5240/LF_HENAN?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&zeroDateTimeBehavior=convertToNull&useSSL=false
        username: LF_HENAN
        password: Tsintergy@123
  #  启动web后打开的浏览器地址
  open-browser-url: http://localhost:7080/xxl-job-admin/jobhome/toLogin
server:
  port: 7080
  servlet:
    #   web应用的上线文，不是spring dispatcherServlet的mapping路径
    context-path: ${JOB_ADMIN_CONTEXT_PATH:/xxl-job-admin}

#dubbo:
#  registries:
#    zk1:
#      register: true
#      address: zookeeper://**********:2181
#      timeout: 60000
#    zk2:
#      register: true
#      address: zookeeper://**********:2181
#      timeout: 60000
#    zk3:
#      register: true
#      address: zookeeper://**********:2181
#      timeout: 60000

#jobAlarm:
#  phone-num-list: 18372652531
#  template: "【负荷预测定时任务告警】%s 时刻， %s定时任务执行失败，请及时处理。"
#  system-name: "福建短期负荷预测"
#  ip: "127.0.0.1"
#  job-names: "annTdForecastHandler,annTdTrainHandler,gtTenDaysFcHandler,gtTenDaysTrainHandler,forecastGruFactorsHandler,gTransformerTrainHandler,gTransformerForecastHandler,transForecastHandler,transTrainHandler,loadCityHisClctJob"
