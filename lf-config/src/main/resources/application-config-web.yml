server:
  port: 8300
  tomcat:
    # 等待队列长度,默认100
    accept-count: 100
    # 最大连接数，默认10000
    max-connections: 10000
    # 最大工作线程数，默认200
    max-threads: 200
    # 初始化分配线程数，默认10
    min-spare-threads: 10
  servlet:
    #   web应用的上线文，不是spring dispatcherServlet的mapping路径
    context-path: /lf_hn
    # 设置session过期时间
    session:
      timeout: 28800
spring:
  mvc:
    servlet:
      #      spring dispatcherServlet的mapping路径, 必须配置为此值，因为前端统一添加 /web 前缀
      path: /web
  servlet:
    multipart:
      enabled: true
      max-file-size: 30MB
      max-request-size: 100MB
tsie:
  #  启动web后打开的浏览器地址
  open-browser-url: http://localhost:8300/lf_hn/index.html#/home

#security:
#  #自定义拦截器开关
#  token-interceptor-open: false
#  password:
#    #30必须重置密码
#    reset-interval: 30000
#    #提前10天发出警告
#    warning-interval: 10
#  tokenCacheKeyPrefix: lf_v2
evalution:
  pass-threshold: 0.8


# 误差分析配置
qualified: 0.97
#实际最高温度和预测最高温度偏差4
weatherDiffer: 4
dataId: 123456789
load:
  holiday: true
  prevDay: 2
  aftDay: 2
  points: 24
  interval: -7
acload:
  summer:
    startdate: 5
    enddate: 9

#置信度95
confidence: 0.95
#误差允许的范围
deviation.range: 0.001
#定时任务的预测类型，默认为1（算法内部做滚动预测，如果预测三天，则只调用算法一次）
task:
  forecast:
    type: 1
    #回溯预测定时任务默认新息点数
    point: 43

#通过实施页面补测的类型，实施一般是补测，如果补的在三天之内，设值为1，如果补测太多天，则设为2（算法内部做滚动预测，如果预测三天，则只调用算法一次）
implement:
  forecast:
    type: 2


#====================预测后评估========================
1: 自动预测准确率达标，无人工修正
2: 准确率达标，自动预测准确率高于人工修正
3: 准确率达标，人工修正准确率高于自动预测
4: 人工修正准确率未达标，自动预测准确率达标，请检查
5: 准确率未达标，系统推荐算法非最优
6: 准确率未达标，气象预测偏差较大
7: 准确率未达标，自动预测算法欠佳


cas:
  #cas服务端URL前缀
  server-url-prefix: http://***********/cas
  server-login-url: http://***********/cas/login
  client-host-url: http://127.0.0.1:8301/
  validation-type: CAS
  #解决ajax不能重定向请求的问题，前端转http请求后，后端重定向
  #  redirect-url: http://localhost:3333/ufls/#/system-init?param=1
  redirect-url: http://***********/cas/login?service=http://***********/loadforecast_new/web/logincontroller/forward
sso:
  whiteList: /*.js|/*.css|/*.png|/system/baseInfo|/data/fc




