tsie:
  multiple-datasource:
    configs:
      - name: ds<PERSON><PERSON><PERSON>
        driver-class-name: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
        #        mysql-host为主机名，非docker环境需要配置hosts的主机映射
        url: jdbc:dm://************:5240/LF_HENAN?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&zeroDateTimeBehavior=convertToNull&useSSL=false
        username: LF_HENAN
        password: Tsintergy@123
        primary: true
        properties:
          connectionTimeout: 300000
          idleTimeout: 300000
          maxLifetime: 1800000
          connectionTestQuery: SELECT 1 FROM DUAL
#      - name: buslf
#        driver-class-name: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
#        url: jdbc:dm://************:5240/QNHL_BUSLF_FJ?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&zeroDateTimeBehavior=convertToNull&useSSL=false
#        username: QNHL_BUSLF_FJ
#        password: qinghua123
      # 福建网格气象站数据采集数据源配置
      #      - name: wgWeather
      #        driver-class-name: oracle.jdbc.driver.OracleDriver
      #        url: *********************************************
      #        username: dd_onlyread
      #        password: Qywsjzx_3948
#      - name: wgWeather
#        driver-class-name: oracle.jdbc.driver.OracleDriver
#        url: *******************************************
#        username: '"c##gdprs"'
#        password: orcl


# 数据库密码秘钥
#java -cp jasypt-1.9.2.jar org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI input="qinghua123@" password=qnhl@2021 algorithm=PBEWithMD5AndDES
jasypt:
  encryptor:
    password: qnhl@2021

xxl:
  job:
    db:
      driverClass: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
      url: jdbc:dm://************:5240/QNHL_LF_FJ?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&zeroDateTimeBehavior=convertToNull&useSSL=false
      user: QNHL_LF_FJ
      password: qinghua123
    taskId: 9
    id: 49
TaskReSendUrl: http://127.0.0.1:8080/tasks/jobinfo/trigger
