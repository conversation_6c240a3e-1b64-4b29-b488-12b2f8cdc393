/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 17:47 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.constants;

import com.tsintergy.lf.core.util.ColumnUtil;

import java.io.File;
import java.util.List;

/**
 * Description:  算法常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class Constants {


    //算法模块回传参数标识 是否为回溯预测
    public final static String IS_RECALL = "isRecall";

    public final static String SEPARATOR = File.separator;

    public static final String PROVINCE_DEFAULT_INDEX = "province_default_index";

    public static final String CITY_DEFAULT_INDEX = "city_default_index";

    /**
     * 文件编码格式 稳定度算法用到  其他算法用的是GB2312
     */
    public final static String ENCODING = "UTF-8";

    /**
     * 授权文件的路径（所有的都用同一个授权文件）
     */
    public final static String LICENCE_PATH = "LICENCE.PATH";


    /**
     * 稳定度算法名称
     */
    public final static String PRECISION_NAME = "precision";

    /**
     * 稳定度分析控制参数写入文件
     */
    public final static String PRECISION_IN_FILE_NAME = "precision.ctr";

    /**
     * 存放历史数据的文件
     */
    public final static String PRECISION_IN_DATA_NAME = "load.txt";

    /**
     * 算法输出结果（分量）存放文件
     */
    public final static String PRECISION_OUT_LOAD_DATA_NAME = "loadDivision.out";

    /**
     * 算法输出结果(上下限) 存放文件名称
     */
    public final static String PRECISION_OUT_DATA_NAME = "precision.out";

    /**
     * 调试模式
     */
    public final static String IsDebug = "ALGORITHM.DEBUG";

    public final static String EMPTY = " ";

    /**
     * 系统通用分隔符
     */
    public static final String SEPARATOR_PUNCTUATION = ",";

    /**
     * 分隔符英文折线
     */
    public static final String SEPARATOR_BROKEN_LINE = "-";

    /**
     * 时间范围分隔符
     */
    public static final String DATE_SEPARATOR_PUNCTUATION = "~";
    //查询条件为全部的常量
    public static final String ALL = "all";
    /*
     *
     * 省会城市Type
     * */
    public static final Integer PROVINCE_TYPE = 1;
    /*
     * 地市城市Type
     * */
    public static final Integer CITY_TYPE = 2;

    /**
     * 默认城市
     */
    public static final String DEFAULT_CITY = "410000";
    /**
     * 计算中Bigdecimal保留的小数位
     */
    public static final int SCALE = 4;


    /**
     * 负荷曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final boolean LOAD_CURVE_START_WITH_ZERO = false;

    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM = 96;

    /**
     * 气象曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final boolean WEATHER_CURVE_START_WITH_ZERO = false;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM = 96;

    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM_24 = 24;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM_24 = 24;

    /**
     * 负荷曲线时段列表
     */
    public static final List<String> DAY_LOAD_CURVE_COLUMNS = ColumnUtil
            .getColumns(LOAD_CURVE_POINT_NUM, LOAD_CURVE_START_WITH_ZERO, true);

    /*
     *
     * 省会城市ID
     * */
    public static final String PROVINCE_ID = "410000";

    public static final String PROVINCE_NAME = "河南";

    public static final String DEVIATION_TOTAL = "全省偏差";


    public static final String PLANT_NET = "厂用电+网损";

    public static final String OTHER_BUS = "其它母线累计";

    /**
     * 全口径
     */
    public static final String CALIBER_QUAN = "1";

    /**
     * 网供口径
     */
    public static final String CALIBER_WANGGONG = "2";
    /**
     * 全行业用电分类
     */
    public static final String TRADE_ID_All = "AAAA";

    /**
     * 秒间隔类型
     */
    public final static Integer SECOND = 1;

    /**
     * 分钟间隔类型
     */
    public final static Integer MINUTE = 2;

    /**
     * 手动预测
     */
    public static final Integer FORECAST_TYPE = 1;
    public static final String CALIBER_CITY_ID = "1";

    public static final String STATION_CITY_ID = "58847";
}