/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/10/12 9:03 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.enums;

/**
 * 综合相似 日期出力模板枚举
 *
 * <AUTHOR>
 * @create 2020/4/18
 * @since 1.0.0
 */
public enum CompositeDayEnum {

    //当日
    TODAY(0, "Today"),
    //前一日
    BEFORE_ONE(1, "BeforeOne"),
    //前两日
    BEFORE_TWO(2, "BeforeTwo");

    /**
     * 对应前端的值
     */
    private Integer type;

    /**
     * 模板字段名
     */
    private String name;

    CompositeDayEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getTypeName(Integer i) {
        String typeName = null;
        CompositeDayEnum[] values = CompositeDayEnum.values();
        for (CompositeDayEnum weatherTypeEnum : values) {
            if (weatherTypeEnum.type.equals(i)) {
                typeName = weatherTypeEnum.name;
            }
        }
        return typeName;

    }


    public Integer getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setName(String name) {
        this.name = name;
    }
}