package com.tsintergy.lf.core.enums;

public enum WeatherSourceEnum {

    /**
     * 气象局预测气象
     */
    FC("1", "预测气象","fc"),

    /**
     * 气象局实际气象
     */
    HIS("2", "实际气象","his"),

    /**
     * METEO气象
     */
    METEO("3", "METEO气象","ec");

    private final String code;
    private final String description;

    private final String name;

    WeatherSourceEnum(String code, String description,String name) {
        this.code = code;
        this.description = description;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getName() {
        return name;
    }
}
