/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/6/2619:37
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.enums;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2019/6/26
 *@since 1.0.0
 */
public enum FeatureRowEnum {


    MAXLOAD(0,"最大负荷","maxLoad"),
    MINLOAD(1,"最小负荷","minLoad"),
    AVELOAD(2,"平均负荷","aveLoad"),
    DIFFERENT(3,"峰谷差","different"),
    GRADIENT(4,"峰谷差率","gradient");

    private Integer id;
    private String name;
    private String value;


    FeatureRowEnum(Integer id, String name, String value) {
        this.id = id;
        this.name = name;
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueById(Integer id){
        for (FeatureRowEnum w :FeatureRowEnum.values()){
            if(w.getId().equals(id)){
                return w.getValue();
            }
        }
        return null;
    }

}