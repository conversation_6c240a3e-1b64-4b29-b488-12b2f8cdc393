/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.core.constants;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

/**
 * Description: 城市常量 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/8 20:56
 * @Version: 1.0.0
 */
public class CityConstants {

    /**
     * 省份id为1
     */
    public static final String PROVINCE_ID = "410000";

    /**
     * 福建行业负荷id
     */
    public static final String INDUSTRY_PROVINCE_ID = "35101";

    /**
     * 省会id
     */
    public static final String CAPITAL_ID = "2";

    /*
     *
     * 省会城市Type
     * */
    public static final Integer PROVINCE_TYPE = 1;

    /*
     * 地市城市Type
     * */
    public static final Integer CITY_TYPE = 2;


    public static final String TONGDIAO = "1";

    public static final String ZHONGDIAO = "2";

    public static final String PROVINCE_STAION = "58847";


    /**
     * 每个地市对应的标准站点,福建标准站点为福州标准站点
     */
    public static final Map<String, String> standardWgStationMap = ImmutableMap.<String, String>builder()
            .put("1", "58847")
            .put("2", "58847")
            .put("3", "59134")
            .put("4", "58946")
            .put("5", "58828")
            .put("6", "59132")
            .put("7", "59126")
            .put("8", "58834")
            .put("9", "58927")
            .put("10", "58846")
            .build();
}