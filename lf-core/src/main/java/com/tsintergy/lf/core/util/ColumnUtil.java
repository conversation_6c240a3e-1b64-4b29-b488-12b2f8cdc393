package com.tsintergy.lf.core.util;

import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.BasePeriod24VO;
import com.tsieframework.core.base.vo.BasePeriod288VO;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsintergy.lf.core.constants.Constants;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

/**
 * @date:17-12-3 下午8:36
 * @author:angel
 **/
public class ColumnUtil {


    private final static Map<Integer, Set<String>> ENTITY_TYPE = new HashMap<Integer, Set<String>>() {{
        put(new Integer(24), new HashSet<String>() {
            {
                Class cla = BasePeriod24VO.class;
                Field[] pkFields = cla.getDeclaredFields();

                for (Field field : pkFields) {
                    add(field.getName());
                }
            }
        });
        put(new Integer(96), new HashSet<String>() {
            {
                Class cla = BasePeriod96VO.class;

                while (cla != null) {
                    Field[] pkFields = cla.getDeclaredFields();

                    for (Field field : pkFields) {
                        add(field.getName());
                    }
                    cla = cla.getSuperclass();
                }
            }
        });
        put(new Integer(288), new HashSet<String>() {
            {
                Class cla = BasePeriod288VO.class;

                while (cla != null) {
                    Field[] pkFields = cla.getDeclaredFields();

                    for (Field field : pkFields) {
                        add(field.getName());
                    }
                    cla = cla.getSuperclass();
                }
            }
        });
    }};



    /**
     * 获取时段列表
     * @param columnCount 时段数：24，96，288
     * @param startWithZero 是否从0点开始
     * @param startWithT 是否添加t前缀
     * @return
     */
    public static List<String> getColumns(int columnCount, boolean startWithZero, boolean startWithT) {

        if (columnCount != 24 && columnCount != 96 && columnCount != 288) {
            throw TsieExceptionUtils.newBusinessException("获取时段列表有误：时段数只能是24/96/288");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("HHmm");
        Calendar calendar = Calendar.getInstance();
        calendar.set(2000, 0, 1, 0, 0, 0);

        int step = 0;
        if (columnCount == 24) {
            step = 60;
        } else if (columnCount == 96) {
            step = 15;
        } else if (columnCount == 288) {
            step = 5;
        }

        if (!startWithZero) {
            calendar.add(Calendar.MINUTE, step);
        }

        List<String> colums = new ArrayList<String>();

        for (int i = 0; i < columnCount; i++) {
            String time = sdf.format(calendar.getTime());
            if (!startWithZero && "0000".equals(time)) {
                time = "2400";
            }
            if (startWithT) {
                time = "t" + time;
            }
            colums.add(time);
            calendar.add(Calendar.MINUTE, step);
        }

        return colums;
    }

    /**
     * 根据需要，将某类型的实体对象转换指定的时刻点LIST
     *
     * <AUTHOR>
     */
    public static List<BigDecimal> toList(BaseVO vo, int type, Boolean index) {
        if (null == vo || !ColumnUtil.ENTITY_TYPE.containsKey(type)) {
            return null;
        }
        List<BigDecimal> result = new ArrayList<BigDecimal>();
        BeanMap map = new BeanMap(vo);
        //Map<String, Object> map = new org.apache.commons.beanutils.BeanMap(vo);
        for (int i = 1; i < 97; i++) {
            String name = "t" + i;
            if (map.containsKey(name)) {
                result.add((BigDecimal) map.get(name));
            }
        }
        return result;
    }

    /**
     * 获取超短期时段列表
     */
    public static List<String> getShortColumns() {
        List<String> colums = new ArrayList<String>();
        for (int i = 1; i <= 96; i++) {
            colums.add("t" + i);
        }
        return colums;
    }

    /**
     * 获取指定范围内的所有字段
     *
     * @param beginColumn 开始时段
     * @param endColumn 结束时段
     * @param columnCount 点数：24,96,288
     * @param startWithZero 是否从0点开始
     * @param startWithT 是否添加t前缀
     */
    public static List<String> getColumnsBetween(String beginColumn, String endColumn, int columnCount,
        boolean startWithZero, boolean startWithT) throws Exception {

        List<String> allColumns = ColumnUtil.getColumns(columnCount, startWithZero, startWithT);

        List<String> columns = new ArrayList<String>();

        if (startWithT) {
            if (!beginColumn.startsWith("t")) {
                beginColumn = "t" + beginColumn;
            }
            if (!endColumn.startsWith("t")) {
                endColumn = "t" + endColumn;
            }
        } else {
            if (beginColumn.startsWith("t")) {
                beginColumn = beginColumn.replace("t", "");
            }
            if (endColumn.startsWith("t")) {
                endColumn = endColumn.replace("t", "");
            }
        }

        for (String col : allColumns) {
            if (col.compareTo(beginColumn) >= 0 && col.compareTo(endColumn) <= 0) {
                columns.add(col);
            }
        }

        return columns;

    }


    /**
     * 判断是否全时段
     */
    public static boolean isAllColumn(String beginColumn, String endColumn) throws Exception {
        List<String> colums = ColumnUtil
            .getColumns(Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        if (colums.get(0).equals(beginColumn) && colums.get(colums.size() - 1).equals(endColumn)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 负荷曲线转Map
     */
    public static Map<String, BigDecimal> listToMap(List<BigDecimal> list, Boolean startWithZero) throws Exception {
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        List<String> columns = ColumnUtil.getColumns(list.size(), startWithZero, true);
        for (int i = 0; i < columns.size(); i++) {
            map.put(columns.get(i), list.get(i));
        }
        return map;
    }


    /**
     * 空缺天的数据补齐
     *
     * @param point
     * @param type  获取96个0 或者96个null
     * @return
     */
    public static List<BigDecimal> getZeroOrNullList(int point, BigDecimal type) {
        List<BigDecimal> list = Lists.newArrayList();
        if (BigDecimal.ZERO.equals(type)) {
            for (int i = 0; i < point; i++) {
                list.add(BigDecimal.ZERO);
            }
        } else {
            for (int i = 0; i < point; i++) {
                list.add(null);
            }
        }
        return list;
    }

    /**
     * 超短期负荷曲线转Map
     *
     * <AUTHOR>
     */
    public static Map<String, BigDecimal> shortListToMap(List<BigDecimal> list) throws Exception {
        List<String> columns = ColumnUtil.getShortColumns();
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        for (int i = 0; i < list.size(); i++) {
            map.put(columns.get(i), list.get(i));
        }
        return map;
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    /**
     * 获取一天的时刻点
     */
    public static List<String> getDayTimes(int count, boolean startWithZero) {
        List<String> list = new ArrayList<String>();
        for (String column : ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false)) {
            list.add(new StringBuffer(column).insert(2, ":").toString());
        }
        return list;
    }

    /**
     * 多列求平均值，如果某个点为null,则不计算该值
     * @param srcList
     * @return
     */
    public static List<BigDecimal> avgList(List<List<BigDecimal>> srcList) {
        List<BigDecimal> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(srcList)) {
            for (int i = 0; i < srcList.get(0).size(); i++) {
                BigDecimal sum = BigDecimal.ZERO;
                Integer number = 0;
                for (List<BigDecimal> list : srcList) {
                    if (list.get(i) != null) {
                        sum = sum.add(list.get(i));
                        number++;
                    }
                }
                if (number != 0) {
                    result.add(sum.divide(new BigDecimal(number), RoundingMode.HALF_UP));
                } else {
                    result.add(null);
                }
            }
        }
        return result;
    }

    /**
     * 根据时刻获取第几个点,从第0个点开始
     * @param timeString HH:mm
     * @return
     */
    public static int getPointIndex(String timeString) {
        if ("24:00".equals(timeString)) {
            return 95;
        }
        LocalTime time = LocalTime.parse(timeString);
        // 将时间转换为分钟表示
        int totalMinutes = time.getHour() * 60 + time.getMinute();
        // 计算是第几个15分钟点
        int pointIndex = totalMinutes / 15;
        if (!Constants.LOAD_CURVE_START_WITH_ZERO) {
            pointIndex = totalMinutes / 15 - 1;
        }
        return pointIndex;
    }

    /**
     * 根据集合角标获取当前时刻，返回值HH:mm
     * @param index
     * @return
     */
    public static String getTimeByIndex(int index) {
        // 每个点表示的分钟数
        int minutesPerPoint = 15;

        // 计算给定角标对应的分钟数
        int minutes = (index) * minutesPerPoint;

        if (!Constants.LOAD_CURVE_START_WITH_ZERO) {
            minutes = (index + 1) * minutesPerPoint;
        }

        // 计算时和分
        int hours = minutes / 60;
        int mins = minutes % 60;

        // 格式化输出时刻
        String time = String.format("%02d:%02d", hours, mins);

        return time;
    }

    /**
     * 补全96点数据
     * @param decimalList
     */
    public static void supplimentPoit(List<BigDecimal> decimalList) {
        for (int i = 0; i < decimalList.size(); i++) {
            if (decimalList.get(i) == null) {
                if (i == 0) {
                    continue;
                }
                BigDecimal front = decimalList.get(i - 1);
                if (front == null) {
                    continue;
                }
                int endIndex = i;
                int firstIndex = i - 1;
                BigDecimal end = null;
                for (; ; ) {
                    i++;
                    if (i >= decimalList.size()) {
                        break;
                    }
                    end = decimalList.get(i);
                    if (end != null) {
                        endIndex = i;
                        break;
                    }
                }

                if (end == null) {
                    continue;
                } else {
                    int countIndex = endIndex - firstIndex;
                    BigDecimal countDevation = end.subtract(front);
                    BigDecimal avgDevation = countDevation
                            .divide(new BigDecimal(countIndex), 2, BigDecimal.ROUND_HALF_UP);
                    for (int b = 1; b < countIndex; b++) {
                        BigDecimal value = avgDevation.multiply(new BigDecimal(b))
                                .add(decimalList.get(firstIndex));
                        decimalList.set(firstIndex + b, value);
                    }
                }
            }
        }
    }

}
