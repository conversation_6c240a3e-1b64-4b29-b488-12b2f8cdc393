package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @date: 2/27/18 5:33 PM
 * @author: angel
 **/
@ApiModel
public class ForecastDTO implements Serializable {

    @ApiModelProperty(value = "算法id",example = "2")
    private String algorithmId;

    @ApiModelProperty(value = "id",example = "3")
    public String id;
    /**
     * 口径id
     */
    @ApiModelProperty(value = "口径id",example = "3")
    public  String caliberId;

    /**
     * 96点预测数据
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "96点预测数据")
    List<BigDecimal> list;

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<BigDecimal> getList() {
        return list;
    }

    public void setList(List<BigDecimal> list) {
        this.list = list;
    }
}
