package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import lombok.Data;

import java.util.Date;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 14:00
 **/
@Data
public class MultiDayWeatherFeatureQueryDTO implements DTO {

    private Date date;

    private WeatherFeatureDTO feature;
}
