/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/5/14 16:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 灵敏度分析算法产出对象实体
 * <AUTHOR>
 * @create 2022/3/9
 * @since 1.0.0
 */
@Data
public class SensitivityResult extends Result implements Serializable {

    @ApiModelProperty(value = "拟合精度")
    private BigDecimal fittingAccuracy;

    @ApiModelProperty(value = "灵敏度列表")
    private List<SensitivityBean> beanList;

    @ApiModelProperty(value = "散点图")
    private List<List<BigDecimal>> featuresPoint;

    @ApiModelProperty(value = "折线图")
    private List<List<BigDecimal>> featuresLine;

}
