package com.tsintergy.lf.serviceapi.base.common.enumeration;

/**
 * 训练模式类型
 * <AUTHOR>
 * @date 2018/02/05
 */
public enum TrainingModeType {

    STATIC(1,"静态训练"),

    DYNAMIC(2,"动态训练");

    private Integer type;

    private String typeName;

    private TrainingModeType(Integer type , String typeName){
        this.type = type;
        this.typeName = typeName;
    }

    public Integer value(){
        return this.type;
    }

    public String typeName(){
        return this.typeName;
    }

}



