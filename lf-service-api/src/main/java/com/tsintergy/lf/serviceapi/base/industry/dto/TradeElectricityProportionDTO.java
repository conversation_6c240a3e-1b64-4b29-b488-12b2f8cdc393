/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.industry.dto;

import com.tsieframework.core.base.domain.dto.DTO;

import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 18:10
 * @Version: 1.0.0
 */

public class TradeElectricityProportionDTO implements DTO {


    /**
     * 0第二类全行业  1第三类 细分行业电量占比
     */
    private Integer level;

    private List<ElectricityProportionDTO> list;


    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<ElectricityProportionDTO> getList() {
        return list;
    }

    public void setList(List<ElectricityProportionDTO> list) {
        this.list = list;
    }
}