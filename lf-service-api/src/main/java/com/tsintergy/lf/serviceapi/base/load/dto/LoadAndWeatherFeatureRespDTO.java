/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/6/9 17:30 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: 修正上报相似日历史特性 <br>
 *
 * <AUTHOR>
 * @create 2022/6/9
 * @since 1.0.0
 */
@Data
public class LoadAndWeatherFeatureRespDTO implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private Date date;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    private String minTime;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(scale = 4, percentConvert = 100)
    private BigDecimal gradient;

    /**
     * 积分电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal integralLoad;

    /**
     * 峰段电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal peakSectionLoad;

    /**
     * 最高温度
     */
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    private BigDecimal aveTemperature;

    /**
     * 最大风速
     */
    private BigDecimal maxWinds;

    /**
     * 降雨量
     */
    private BigDecimal rainfall;

}
