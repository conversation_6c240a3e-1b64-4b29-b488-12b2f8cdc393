package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 13:59
 **/
@Data
public class MultiDayLoadQueryDTO implements DTO {

    @ApiModelProperty(value = "日期")
    private Date date;

    @ApiModelProperty(value = "实际")
    private List<BigDecimal> real;

    @ApiModelProperty(value = "预测")
    private List<BigDecimal> fc;

    @ApiModelProperty(value = "人工修正")
    private List<BigDecimal> modify;
}
