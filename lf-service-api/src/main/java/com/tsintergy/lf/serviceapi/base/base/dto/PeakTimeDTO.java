package com.tsintergy.lf.serviceapi.base.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

/**
 * User:taojingui
 * Date:18-3-1
 * Time:下午4:20
 */
@ApiModel
public class PeakTimeDTO implements Serializable {

    /**
     * 早高峰
     */
    @ApiModelProperty(value = "早高峰",example = "11,22")
    private List<String> earlyPeak;

    /**
     * 午高峰
     */
    @ApiModelProperty(value = "午高峰",example = "11,22")
    private List<String> noonPeak;

    /**
     * 晚高峰
     */
    @ApiModelProperty(value = "午高峰",example = "11,22")
    private List<String> nightPeak;

    public List<String> getEarlyPeak() {
        return earlyPeak;
    }

    public void setEarlyPeak(List<String> earlyPeak) {
        this.earlyPeak = earlyPeak;
    }

    public List<String> getNoonPeak() {
        return noonPeak;
    }

    public void setNoonPeak(List<String> noonPeak) {
        this.noonPeak = noonPeak;
    }

    public List<String> getNightPeak() {
        return nightPeak;
    }

    public void setNightPeak(List<String> nightPeak) {
        this.nightPeak = nightPeak;
    }
}
