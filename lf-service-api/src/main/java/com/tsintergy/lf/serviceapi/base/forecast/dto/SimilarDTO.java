package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @date: 3/2/18 2:47 PM
 * @author: angel
 **/
public class SimilarDTO implements Serializable{

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date targetDay;

    private List<SimilarDay> similarDay = new ArrayList<SimilarDay>(5);

    private boolean holiday;

    private class SimilarDay implements Serializable{
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date date;
        private BigDecimal similar;
        SimilarDay(Date date,BigDecimal similar){
            this.date = date;
            this.similar = similar;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public BigDecimal getSimilar() {
            return similar;
        }

        public void setSimilar(BigDecimal similar) {
            this.similar = similar;
        }
    }

    public Date getTargetDay() {
        return targetDay;
    }

    public void setTargetDay(Date targetDay) {
        this.targetDay = targetDay;
    }

    public List<SimilarDay> getSimilarDay() {
        return similarDay;
    }

    public void setSimilarDay(List<SimilarDay> similarDay) {
        this.similarDay = similarDay;
    }

    public boolean isHoliday() {
        return holiday;
    }

    public void setHoliday(boolean holiday) {
        this.holiday = holiday;
    }

    public SimilarDay setSimilarDay(Date date, BigDecimal similar){
        SimilarDay similarDay = new SimilarDay(date,similar);
        return similarDay;
    }
}
