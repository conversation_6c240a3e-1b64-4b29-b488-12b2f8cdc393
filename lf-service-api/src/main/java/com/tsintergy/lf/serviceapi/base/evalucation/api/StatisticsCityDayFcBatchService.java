package com.tsintergy.lf.serviceapi.base.evalucation.api;


import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayBatchDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: StatisticsCityDayFcService.java, v 0.1 2018-01-31 10:20:07 tao Exp $$
 */
public interface StatisticsCityDayFcBatchService {

    List<StatisticsCityDayFcBatchDO> doSaveOrUpdateStatisticsCityDayFcDOs(
            List<StatisticsCityDayFcBatchDO> statisticsCityDayFcVOS) throws Exception;

    List<StatisticsDayBatchDTO> getDayAccuracy(String cityId, String caliberId, List<String> algorithmId, List<Integer> batchIds, Date startDate,
                                               Date endDate) throws Exception;

    List<StatisticsCityDayFcBatchDO> getDayAccuracyInBatch(String cityId, String caliberId, String algorithmId, String batchId, Date startDate,
                                                           Date endDate) throws Exception;

}