package com.tsintergy.lf.serviceapi.base.check.pojo;


import com.tsieframework.core.base.dao.BaseVO;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 上报设置
 */@Data
@Entity
@Table(name = "setting_report_init")
public class SettingReportDO extends BaseVO {
    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市ID
     */@Column(name = "city_id")
    private String cityId;

    /**
     * 考核标准准确率
     */@Column(name = "standard_accuracy")
    private BigDecimal standardAccuracy;

//    /**
//     * 上报时间
//     */@Column(name = "report_time")
//    private String reportTime;



}
