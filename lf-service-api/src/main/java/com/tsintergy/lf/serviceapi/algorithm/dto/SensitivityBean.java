/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/14 3:03
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 灵敏度分析 灵敏度单元对象
 *
 * <AUTHOR>
 * @create 2022/3/9
 * @since 1.0.0
 */
@Data
public class SensitivityBean implements Serializable {

    @ApiModelProperty(value = "气象指标")
    private BigDecimal weatherNorm;

    @ApiModelProperty(value = "灵敏度")
    private BigDecimal value;

    private BigDecimal load;

    private BigDecimal fittingValue;

    private BigDecimal fittingValueCompare;

    private BigDecimal accuracy;

    private String year;

    private List<BigDecimal> featuresPoint;

    public SensitivityBean() {}

    // 拷贝构造函数
    public SensitivityBean(SensitivityBean other) {
        this.value = other.value;
        this.load = other.load;
        this.fittingValue = other.fittingValue;
        this.accuracy = other.accuracy;
        this.weatherNorm = other.weatherNorm;
        this.year = other.year;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    // Getter 和 Setter 方法
    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BigDecimal getLoad() {
        return load;
    }

    public void setLoad(BigDecimal load) {
        this.load = load;
    }

    public BigDecimal getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }
}