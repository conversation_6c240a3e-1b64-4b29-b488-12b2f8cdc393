/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/8/28 14:36
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2018/8/28
 * @since 1.0.0
 */
public class WeatherFeature {
    /**
     * 日期
     */
    private Date date;
    /**
     * 最高温度
     */
    private BigDecimal highestTemperature;

}