/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/4/19 19:22
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19 
 * @since 1.0.0
 */
@ApiModel
public class AreaLoadFeatureDTO implements Serializable {

    private static final long serialVersionUID = 8479751142997462925L;
    @ApiModelProperty(value = "地区名称",example = "抚顺")
    private String areaName;
    @ApiModelProperty(value = "地区负荷",example = "123121")
    private BigDecimal areaFeatureLoad;
    @ApiModelProperty(value = "负荷",example = "123121")
    private BigDecimal proportion;

    public AreaLoadFeatureDTO(String areaName, BigDecimal areaFeatureLoad, BigDecimal proportion) {
        this.areaName = areaName;
        this.areaFeatureLoad = areaFeatureLoad;
        this.proportion = proportion;
    }

    public AreaLoadFeatureDTO() {
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public BigDecimal getAreaFeatureLoad() {
        return areaFeatureLoad;
    }

    public void setAreaFeatureLoad(BigDecimal areaFeatureLoad) {
        this.areaFeatureLoad = areaFeatureLoad;
    }

    public BigDecimal getProportion() {
        return proportion;
    }

    public void setProportion(BigDecimal proportion) {
        this.proportion = proportion;
    }
}
