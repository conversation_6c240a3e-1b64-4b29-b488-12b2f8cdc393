package com.tsintergy.lf.serviceapi.base.base.dto;

import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 一段时间内的正常日和休息日
 *
 * @date: 6/13/18 11:15 AM
 * @author: angel
 **/
public class SkipHolidayDTO implements Serializable {

    /**
     * 正常日
     */
    private List<Date> normalDays;

    /**
     * 休息日
     */
    private List<Date> holidays;

    public List<Date> getNormalDays() {
        return normalDays;
    }

    public void setNormalDays(List<Date> normalDays) {
        this.normalDays = normalDays;
    }

    public List<Date> getHolidays() {
        return holidays;
    }

    public void setHolidays(List<Date> holidays) {
        this.holidays = holidays;
    }

    public List<Date> addNormalDay(Date normalDate) {
        if (CollectionUtils.isEmpty(this.getNormalDays())) {
            this.normalDays = new ArrayList<Date>(10);
        }
        this.normalDays.add(normalDate);
        return this.normalDays;
    }

    public List<Date> addholiday(Date normalDate) {
        if (CollectionUtils.isEmpty(this.getNormalDays())) {
            this.holidays = new ArrayList<Date>(10);
        }
        this.holidays.add(normalDate);
        return this.holidays;
    }
}
