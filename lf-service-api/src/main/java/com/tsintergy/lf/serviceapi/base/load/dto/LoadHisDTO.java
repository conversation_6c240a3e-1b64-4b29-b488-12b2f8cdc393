package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @date: 3/12/18 5:33 PM
 * @author: angel
 **/
public class LoadHisDTO implements Serializable {

    @NotBlank(message = "唯一标识不能为空")
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "日期")
    @NotNull(message = "日期不能为空")
    private Date date;
    @ApiModelProperty(value = "城市ID")
    @NotNull(message = "城市不能为空")
    private String cityId;
    @ApiModelProperty(value = "96点数据")
    @Size(min = 96,message = "请传递完整的96点数据")
    private List<BigDecimal> data;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public List<BigDecimal> getData() {
        return data;
    }

    public void setData(List<BigDecimal> data) {
        this.data = data;
    }
}
