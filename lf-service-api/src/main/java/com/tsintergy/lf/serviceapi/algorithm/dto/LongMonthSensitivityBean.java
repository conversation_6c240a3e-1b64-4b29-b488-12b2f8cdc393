/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/6/27 1:34 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 中长期综合模型
 *
 * <AUTHOR>
 * @create 2024/05/24
 * @since 1.0.0
 */
@Data
public class LongMonthSensitivityBean implements Serializable {

    private String date;

    private BigDecimal minMonthLoad;

    private BigDecimal maxMonthLoad;

    private BigDecimal electricity;


}