/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/6/11 17:04
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/6/11 
 * @since 1.0.0
 */
@ApiModel
public class ManualForecastCurveDTO implements Serializable {

    @ApiModelProperty(value = "码值",example = "1")
    private String code;

    @ApiModelProperty(value = "算法id",example = "1")
    private String algorithmId;

    @ApiModelProperty(value = "算法名称",example = "人工修正")
    private String algorithmName;

    /**
     * 峰谷差
     */
    @ApiModelProperty(value = "峰谷差",example = "213")
    private BigDecimal gradient;

    /**
     * 积分电量
     */
    @ApiModelProperty(value = "积分电量",example = "12312")
    private BigDecimal energy;

    /**
     * 最大负荷
     */
    @ApiModelProperty(value = "最大负荷",example = "3123121")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @ApiModelProperty(value = "最小负荷",example = "123")
    private BigDecimal minLoad;

    /**
     * 曲线值
     */
    @ApiModelProperty(value = "曲线值",example = "[0.99,0.99,0.99]")
    private List<BigDecimal> curve;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getEnergy() {
        return energy;
    }

    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public List<BigDecimal> getCurve() {
        return curve;
    }

    public void setCurve(List<BigDecimal> curve) {
        this.curve = curve;
    }
}
