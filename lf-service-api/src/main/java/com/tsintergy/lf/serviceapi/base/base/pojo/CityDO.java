package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.vo.CacheVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 城市
 */

@Data
@Entity
@Table(name = "city_base_init")
@ApiModel
public class CityDO extends CacheVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "5")
    private String id;

    /**
     * 城市
     */
    @Column(name = "city")
    @ApiModelProperty(value = "城市",example = "抚顺")
    private String city;

    /**
     * 城市类型，地区类型（1:省级 2:地市 3：区级）
     */
    @Column(name = "type")
    @ApiModelProperty(value = "城市类型",example = "2")
    private Integer type;

    /**
     * 上级
     */
    @Column(name = "belong_id")
    @ApiModelProperty(value = "上级",example = "2312321")
    private String belongId;

    /**
     * 排序号
     */
    @Column(name = "order_no")
    @ApiModelProperty(value = "排序号",example = "2")
    private Integer orderNo;

    /**
     * 对应城市气象
     */
    @Column(name = "weather_city_id")
    @ApiModelProperty(value = "对应城市气象",example = "1")
    private String weatherCityId;


    /**
     * 区域
     */
    @Column(name = "area")
    @ApiModelProperty(value = "区域",example = "辽宁")
    private String area;

    /**
     * 是否有效
     */
    @Column(name = "valid")
    @ApiModelProperty(value = "是否有效",example = "true")
    private Boolean valid;

    @Column(name = "ORG_NO")
    @ApiModelProperty(value = "编号")
    private String orgNo;

    @Override
    public String getKey() {
        return this.id;
    }

    @Override
    public String getLabel() {
        return this.city;
    }
}
