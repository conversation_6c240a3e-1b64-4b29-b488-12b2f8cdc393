package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 17:03
 **/
@Data
public class WeatherFeatureDTO implements DTO {

    /**
     * 最高温度
     */
    @ApiModelProperty("最高温度")
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    @ApiModelProperty("最低温度")
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    @ApiModelProperty("平均温度")
    private BigDecimal aveTemperature;



    /**
     * 相对湿度
     */
    @ApiModelProperty("相对湿度")
    private BigDecimal aveHumidity;

    /**
     * 最大风速
     */
    @ApiModelProperty("最大风速")
    private BigDecimal maxWinds;

    /**
     * 最高实感温度
     */
    @ApiModelProperty("最高实感温度")
    private BigDecimal highestEffectiveTemperature;

    /**
     * 最低实感温度
     */
    @ApiModelProperty("最低实感温度")
    private BigDecimal lowestEffectiveTemperature;

    /**
     * 平均实感温度
     */
    @ApiModelProperty("平均实感温度")
    private BigDecimal aveEffectiveTemperature;
}
