package com.tsintergy.lf.serviceapi.base.report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel("上报累计准确率信息")
public class ReportAccuracySynthesizeInfoDTO {

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("日期")
    private String date;

    /**
     * 累计综合准确率
     */
    @ApiModelProperty("累计综合准确率")
    private BigDecimal accuracy;

    /**
     * 96时刻点准确率
     */
    @ApiModelProperty("96时刻点准确率")
    private BigDecimal pointAccuracy;

    /**
     * 最大负荷综合预测准确率
     */
    @ApiModelProperty("最大负荷综合预测准确率")
    private BigDecimal maxSynthesizeAccuracy;

    /**
     * 最小负荷综合准确率
     */
    @ApiModelProperty("最小负荷综合准确率")
    private BigDecimal minSynthesizeAccuracy;

    /**
     * 电量综合准确率
     */
    @ApiModelProperty("电量综合准确率")
    private BigDecimal energySynthesizeAccuracy;

}