package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsintergy.lf.serviceapi.base.load.dto.DayValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 多批次准确率曲线结果
 */
@Data
public class MultiDayQueryWeatherDTO implements Serializable {

    @ApiModelProperty("多日气象数据")
    private List<MultiDayLoadQueryDTO> multiDayLoad;

    @ApiModelProperty("多日实际气象特性数据")
    private List<MultiDayWeatherFeatureQueryDTO> multiDayHisFeature;

    @ApiModelProperty("多日预测气象特性数据")
    private List<MultiDayWeatherFeatureQueryDTO> multiDayFcFeature;

    @ApiModelProperty("多日最大负荷及其发生日期")
    private DayValueDTO multiDayMaxLoad;

}
