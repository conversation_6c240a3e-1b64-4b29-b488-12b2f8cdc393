package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/4/15
 * @since 1.0.0
 */
@Data
public class HisDateDTO implements Serializable {

    @ApiModelProperty(value = "月份",example = "1月 2月 3月... ...")
    private String hisMonth;

    @ApiModelProperty(value = "每月对应的数据", example = "99.00")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal hisDate;
}
