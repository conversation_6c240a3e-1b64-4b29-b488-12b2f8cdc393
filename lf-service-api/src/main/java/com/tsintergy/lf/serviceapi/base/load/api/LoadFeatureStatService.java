package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.dto.IndexLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureCityMonthDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityQuarterHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityWeekHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityYearHisDO;
import java.util.Date;
import java.util.List;

/**
 * 负荷特性统计接口 User:taojingui Date:18-2-23 Time:上午11:53
 */
public interface LoadFeatureStatService {


    /**
     * 计算负荷预测特性
     *
     * @param fcVO 预测96点数据
     * <AUTHOR>
     */
    LoadFeatureFcDTO findStatisticsDayFeature(BasePeriod96VO fcVO) throws Exception;

    /**
     * 统计地区日负荷特性并入库
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    List<LoadFeatureCityDayHisDO> doStatLoadFeatureCityDay(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;

    /**
     * 统计地区日负荷特性并入库-从1分钟间隔的负荷数据中读取历史数据计算负荷特性；
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    void doStatLoadFeatureCityDayClctHisSecond(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;

    /**
     * 计-日-预测-负荷特性
     */
    void doStatLoadFeatureCityDayFc(List<String> cityIds, Date startDate, Date endDate, String caliberId);

    /**
     * 统计-周-预测-负荷特性
     */
    void doStatLoadFeatureCityWeekFc(List<String> cityIds, Date startDate, Date endDate, String caliberId);

    /**
     * 统计-月-预测-负荷特性
     */
    void doStatLoadFeatureCityMonthFc(List<String> cityIds, Date startDate, Date endDate, String caliberId);


    /**
     * 统计负荷特性
     */
    List<? extends BaseLoadFeatureCityDayDO> doStatLoadFeatureCityDay(List<? extends BaseLoadCityDO> loadCityVOS)
        throws Exception;

    /**
     * 统计负荷特性
     */
    BaseLoadFeatureCityDayDO doStatLoadFeatureCityDay(BasePeriod96VO loadCityVO);


    /**
     * 统计地区周负荷特性并入库
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    List<LoadFeatureCityWeekHisDO> doStatLoadFeatureCityWeek(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;


    /**
     * 统计地区月负荷特性并入库
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    List<LoadFeatureCityMonthHisDO> doStatLoadFeatureCityMonth(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;


    /**
     * 统计地区季负荷特性并入库
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    List<LoadFeatureCityQuarterHisDO> doStatLoadFeatureCityQuarter(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;


    /**
     * 统计全部时间纬度的负荷特性
     */
    void doStatLoadFeatureCityAll(List<String> cityIds, Date startDate, Date endDate, String caliberId)
        throws Exception;

    /**
     * 统计地区年负荷特性并入库
     */
    List<LoadFeatureCityYearHisDO> doStatLoadFeatureCityYear(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception;


    /**
     * 功能描述:查询月负荷特性 <br> 〈〉
     *
     * @param cityId 城市ID
     * @param startYM 开始日期
     * @param endYM 结束日期
     * @param caliberId 口径ID
     * @return:
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/22 10:41
     */

    IndexLoadFeatureDTO findStatLoadFeatureCityMonth(String cityId, String startYM, String endYM, String caliberId)
        throws Exception;


    /**
     * 功能描述: 根据要查看的数据类型返回每个月的该数据类型的值<br> 〈〉
     *
     * @return:java.util.List<com.load.load.persistent.LoadFeatureCityMonthHisDO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/22 13:54
     */
    List<LoadFeatureCityMonthDTO> findTypePowerFeatureDate(String dataType, Date start, Date end, String cityId,
        String caliberId) throws Exception;



    /**
     * 统计超短期预测日负荷特性并入库
     *
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    void doShortFcLoadFeatureCityDay(String cityId, Date startDate, Date endDate,
                                                           String caliberId) throws Exception;

}
