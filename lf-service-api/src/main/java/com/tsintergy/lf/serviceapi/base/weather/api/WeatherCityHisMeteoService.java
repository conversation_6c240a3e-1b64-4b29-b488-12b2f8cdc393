package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisMeteoDO;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-03
 * @since 1.0.0
 */
public interface WeatherCityHisMeteoService {

    void doSaveOrUpdate(WeatherCityHisMeteoDO weatherCityHisMeteoDO);

    void insertYesterday24HourData(WeatherCityHisMeteoDO weatherCityHisMeteoDO);

    List<WeatherCityHisMeteoDO> getListByCondition(String cityId, Integer type, Date startDate, Date endDate);

    List<WeatherCityHisMeteoDO> getWeatherCityHisDOS(List<String> cityIds, Integer type, Date startDate, Date endDate);
}
