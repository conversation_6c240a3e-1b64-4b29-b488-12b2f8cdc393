/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/1/2 10:15
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.report.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * Description:  <br>
 * 季负荷预测填报
 *
 * <AUTHOR>
 * @create 2020/1/2
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "report_load_holiday")
@ApiModel
public class ReportLoadHolidayDO extends BaseVO {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "5")
    private String id;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    @ApiModelProperty(value = "城市id",example = "5")
    private String cityId;

    /**
     * 节假日id
     */
    @Column(name = "holiday_id")
    @ApiModelProperty(value = "节假日id",example = "1")
    private String holidayId;

    /**
     * 日期
     */
    @Column(name = "date")
    @ApiModelProperty(value = "日期",example = "2020-03-01")
    private Date date;

    /**
     * 需求最大电力
     */
    @Column(name = "req_max_load")
    @ApiModelProperty(value = "需求最大电力",example = "521321")
    private BigDecimal reqMaxLoad;

    /**
     * 需求最小电力
     */
    @Column(name = "req_min_load")
    @ApiModelProperty(value = "需求最小电力",example = "521321")
    private BigDecimal reqMinLoad;

    /**
     * 需求电量
     */
    @Column(name = "req_energy")
    @ApiModelProperty(value = "需求电量",example = "521321")
    private BigDecimal reqEnergy;

    /**
     * 错峰电力
     */
    @Column(name = "peak_load")
    @ApiModelProperty(value = "错峰电力",example = "521321")
    private BigDecimal peakLoad;
    /**
     * 错峰电量
     */
    @Column(name = "peak_energy")
    @ApiModelProperty(value = "错峰电量",example = "521321")
    private BigDecimal peakEnergy;

    /**
     * 可供电力
     */
    @Column(name = "pro_load")
    @ApiModelProperty(value = "可供电力",example = "521321")
    private BigDecimal proLoad;

    /**
     * 可供电量
     */
    @Column(name = "pro_energy")
    @ApiModelProperty(value = "可供电量",example = "521321")
    private BigDecimal proEnergy;

    /**
     * 上报
     */
    @Column(name = "report")
    @ApiModelProperty(value = "上报",example = "true")
    private Boolean report;


    @Column(name = "createtime")
    @CreationTimestamp
    @ApiModelProperty(value = "创建时间",example = "2020-03-01")
    private Timestamp createTime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    @ApiModelProperty(value = "修改时间",example = "2020-03-01")
    private Timestamp updateTime;

    @Column(name = "report_time")
    @ApiModelProperty(value = "上报时间",example = "2020-03-01")
    private java.util.Date reportTime;
}