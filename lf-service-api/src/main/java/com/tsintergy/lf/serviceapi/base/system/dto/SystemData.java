/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  gss Date:  2019/7/8 16:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 系统设置DTO参数
 *
 * <AUTHOR>
 * @create 2020/7/16
 * @since 1.0.0
 */
@Data
@ApiModel
public class SystemData implements Serializable {

    private static final long serialVersionUID = 8076632146733869931L;

    /**
     * 预测天数
     */
    @ApiModelProperty(value = "预测天数")
    private String forecastDay;

    /**
     * 正常日推荐算法 全网
     */
    @ApiModelProperty(value = "正常日推荐算法全网")
    private String provinceNormalAlgorithm;

    /**
     * 正常日推荐算法 地市
     */
    @ApiModelProperty(value = "正常日推荐算法地市")
    private String cityNormalAlgorithm;

    /**
     * 节假日推荐算法 全网
     */
    @ApiModelProperty(value = "节假日推荐算法全网")
    private String provinceHolidayAlgorithm;

    /**
     * 节假日推荐算法 地市
     */
    @ApiModelProperty(value = "节假日推荐算法地市")
    private String cityHolidayAlgorithm;

    /**
     * 全网自动上报 开关 0 关闭 1开启
     */
    @ApiModelProperty(value = "全网自动上报")
    private String provinceAutoReportSwitch;

    /**
     * 地市自动上报 开关 0 关闭 1开启
     */
    @ApiModelProperty(value = "地市自动上报")
    private String cityAutoReportSwitch;

    /**
     * 子网上报截止时间开关
     */
    @ApiModelProperty(value = "子网上报截止时间开关")
    private String endReportSwitch;

    /**
     * 子网上报截止时间
     */
    @ApiModelProperty(value = "子网上报截止时间")
    private String endReportTime;

    /**
     * 全网超短期5分钟开关
     */
    @ApiModelProperty(value = "全网超短期5分钟开关")
    private String provinceShortFiveSwitch;

    /**
     * 全网超短期5分钟预测时长
     */
    @ApiModelProperty(value = "全网超短期5分钟预测时长")
    private String provinceShortFiveTime;

    /**
     * 全网超短期15分钟开关
     */
    @ApiModelProperty(value = "全网超短期15分钟开关")
    private String provinceShortFifteenSwitch;

    /**
     * 全网超短期15分钟预测时长
     */
    @ApiModelProperty(value = "全网超短期15分钟预测时长")
    private String provinceShortFifteenTime;

    /**
     * 地市超短期5分钟预测开关
     */
    @ApiModelProperty(value = "地市超短期5分钟预测开关")
    private String cityShortFiveSwitch;

    /**
     * 地市超短期5分钟预测时长
     */
    @ApiModelProperty(value = "地市超短期5分钟预测时长")
    private String cityShortFiveTime;

    /**
     * 地市超短期15分钟预测开关
     */
    @ApiModelProperty(value = "地市超短期15分钟预测开关")
    private String cityShortFifteenSwitch;

    /**
     * 地市超短期15分钟预测时长
     */
    @ApiModelProperty(value = "地市超短期15分钟预测时长")
    private String cityShortFifteenTime;

    /**
     * 目标准确率
     */
    @ApiModelProperty(value = "目标准确率")
    private String targetAccuracy;


    /**
     * 全网上报截止时间开关
     */
    @ApiModelProperty(value = "全网上报截止时间开关")
    private String provinceEndReportSwitch;

    /**
     * 全网上报截止时间
     */
    @ApiModelProperty(value = "全网上报截止时间")
    private String provinceEndReportTime;


    @ApiModelProperty(value = "数据发送d5000开关")
    private String acLoadSendD5000;


    @ApiModelProperty(value = "基础负荷增长率设置")
    private String basicLoadIncreaseRate;


    @ApiModelProperty(value = "空调负荷导入数据展示并发送d5000")
    private String importDataShowAndSendD5000;

    @ApiModelProperty(value = "预测上传国调-算法")
    private String guodiaoUploadFileAlgorithm;

    @ApiModelProperty(value = "预测上传国调-口径")
    private String guodiaoUploadFileCaliber;

    @ApiModelProperty(value = "多算法准确率-展示算法")
    private List<String> cmAccuracyAlgorithmIds;

    @ApiModelProperty(value = "预测上传国调-算法列表")
    private Map<String, String> guodiaoUploadFileAlgorithmList;
}
