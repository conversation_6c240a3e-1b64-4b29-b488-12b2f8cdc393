package com.tsintergy.lf.serviceapi.base.weather.pojo;


import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base24DO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 气象站网格数据24点
 */
@Data
@Entity
@Table(name ="weather_station_fc_clct_wg")
@EntityUniqueIndex({"date","type","stationWgId"})
public class WeatherStationFcClctWgDO extends Base24DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;


    /**
     * 气象类型
     */
    @Column(name = "type")
    private Integer type;


    /**
     * 城市ID
     */
    @Column(name = "station_wg_id")
    private String stationWgId;


    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;
}
