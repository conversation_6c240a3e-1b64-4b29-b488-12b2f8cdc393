package com.tsintergy.lf.serviceapi.base.base.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;

import com.tsieframework.cloud.security.serviceapi.system.enums.LoginMode;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * @date: 6/14/18 11:39 AM
 * @author: angel
 **/
@Data
@Entity
@Table(name = "load_user_detail")
@ApiModel
public class LoadUserDO extends BaseVO {

    @Id
    @Column(name = "tsie_uid")
    @ApiModelProperty(value = "用户id",example = "5")
    private String tsieUId;

    @Column(name = "city_id")
    @ApiModelProperty(value = "城市id",example = "5")
    private String cityId;

    @Column(name = "createtime")
    @CreationTimestamp
    @ApiModelProperty(value = "创建时间",example = "2021-03-27")
    private Timestamp createTime;

    @Column(name = "last_update_password_time")
    @UpdateTimestamp
    @ApiModelProperty(value = "最后一次修改时间",example = "2021-03-27")
    private Timestamp lastUpdatePasswordTime;

    @Column(name = "cloud_user_id")
    private String cloudUserId;

    @Transient
    private String city;

    @Transient
    private String id;
    @Transient
    private String username;
    @Transient
    private Integer enable;
    @Transient
    private String passwd;
    @Transient
    private String nickname;
    @Transient
    private String email;
    @Transient
    private String phoneno;
    @Transient
    private LoginMode loginMode;
    @Transient
    private Integer passwdChangePrompt;
    @Transient
    private Date expiredDate;


}
