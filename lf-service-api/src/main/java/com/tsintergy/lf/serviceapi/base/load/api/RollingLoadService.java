/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/6/2214:48
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsieframework.core.base.service.BaseService;
import com.tsintergy.lf.serviceapi.base.load.pojo.RollingLoadFeatureDO;
import java.util.Date;
import java.util.List;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/6/22
 *@since 1.0.0
 */
public interface RollingLoadService extends BaseService {

    List<RollingLoadFeatureDO> findByDate(Date startDate, Date endDate) throws Exception;

}