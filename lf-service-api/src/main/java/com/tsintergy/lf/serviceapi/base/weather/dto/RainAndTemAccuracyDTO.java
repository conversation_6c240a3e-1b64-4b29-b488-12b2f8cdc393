/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/10/918:51
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.weather.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2019/10/9
 *@since 1.0.0
 */
@ApiModel
public class RainAndTemAccuracyDTO implements Serializable {

    @ApiModelProperty(value = "城市名称",example = "抚顺")
    String cityName;

    @ApiModelProperty(value = "日期",example = "2021-03-01")
    String date;

    @ApiModelProperty(value = "温度准确率",example = "0.98")
    BigDecimal temAccuracy;

    @ApiModelProperty(value = "降雨准确率",example = "0.97")
    BigDecimal rainAccuracy;

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public BigDecimal getTemAccuracy() {
        return temAccuracy;
    }

    public void setTemAccuracy(BigDecimal temAccuracy) {
        this.temAccuracy = temAccuracy;
    }

    public BigDecimal getRainAccuracy() {
        return rainAccuracy;
    }

    public void setRainAccuracy(BigDecimal rainAccuracy) {
        this.rainAccuracy = rainAccuracy;
    }
}