package com.tsintergy.lf.serviceapi.base.forecast.pojo;


import com.tsieframework.core.base.dao.BaseVO;

import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 参数方案
 */
@Data
@Entity
@Table(name = "plan_basic")
public class PlanDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 方案名称
     */
    @Column(name = "plan")
    private String plan;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 是否默认方案
     */
    @Column(name = "is_default")
    private Boolean isDefault;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    private Timestamp updatetime;

    /**
     * 创建者ID
     */
    @Column(name = "create_user_id")
    private String createUserId;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 训练模式
     */
    @Column(name = "training_mode")
    private Integer trainingMode;

    /**
     * 参数自适应
     */
    @Column(name = "param_adaptation")
    private Boolean paramAdaptation;

    /**
     * 静态训练开始日期
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 静态训练结束日期
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 动态训练样本天数
     */
    @Column(name = "sample_days")
    private Integer sampleDays;


}
