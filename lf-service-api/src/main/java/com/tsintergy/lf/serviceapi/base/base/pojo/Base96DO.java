package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.vo.BasePeriod96VO;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;


/**
 * <AUTHOR>
 */
@MappedSuperclass
public class Base96DO extends BasePeriod96VO {

    @Column(name = "T0000")
    private BigDecimal t0000;

    @Column(name = "T0015")
    private BigDecimal t0015;

    @Column(name = "T0030")
    private BigDecimal t0030;

    @Column(name = "T0045")
    private BigDecimal t0045;

    @Column(name = "T0100")
    private BigDecimal t0100;

    @Column(name = "T0115")
    private BigDecimal t0115;

    @Column(name = "T0130")
    private BigDecimal t0130;

    @Column(name = "T0145")
    private BigDecimal t0145;

    @Column(name = "T0200")
    private BigDecimal t0200;

    @Column(name = "T0215")
    private BigDecimal t0215;

    @Column(name = "T0230")
    private BigDecimal t0230;

    @Column(name = "T0245")
    private BigDecimal t0245;

    @Column(name = "T0300")
    private BigDecimal t0300;

    @Column(name = "T0315")
    private BigDecimal t0315;

    @Column(name = "T0330")
    private BigDecimal t0330;

    @Column(name = "T0345")
    private BigDecimal t0345;

    @Column(name = "T0400")
    private BigDecimal t0400;

    @Column(name = "T0415")
    private BigDecimal t0415;

    @Column(name = "T0430")
    private BigDecimal t0430;

    @Column(name = "T0445")
    private BigDecimal t0445;

    @Column(name = "T0500")
    private BigDecimal t0500;

    @Column(name = "T0515")
    private BigDecimal t0515;

    @Column(name = "T0530")
    private BigDecimal t0530;

    @Column(name = "T0545")
    private BigDecimal t0545;

    @Column(name = "T0600")
    private BigDecimal t0600;

    @Column(name = "T0615")
    private BigDecimal t0615;

    @Column(name = "T0630")
    private BigDecimal t0630;

    @Column(name = "T0645")
    private BigDecimal t0645;

    @Column(name = "T0700")
    private BigDecimal t0700;

    @Column(name = "T0715")
    private BigDecimal t0715;

    @Column(name = "T0730")
    private BigDecimal t0730;

    @Column(name = "T0745")
    private BigDecimal t0745;

    @Column(name = "T0800")
    private BigDecimal t0800;

    @Column(name = "T0815")
    private BigDecimal t0815;

    @Column(name = "T0830")
    private BigDecimal t0830;

    @Column(name = "T0845")
    private BigDecimal t0845;

    @Column(name = "T0900")
    private BigDecimal t0900;

    @Column(name = "T0915")
    private BigDecimal t0915;

    @Column(name = "T0930")
    private BigDecimal t0930;

    @Column(name = "T0945")
    private BigDecimal t0945;

    @Column(name = "T1000")
    private BigDecimal t1000;

    @Column(name = "T1015")
    private BigDecimal t1015;

    @Column(name = "T1030")
    private BigDecimal t1030;

    @Column(name = "T1045")
    private BigDecimal t1045;

    @Column(name = "T1100")
    private BigDecimal t1100;

    @Column(name = "T1115")
    private BigDecimal t1115;

    @Column(name = "T1130")
    private BigDecimal t1130;

    @Column(name = "T1145")
    private BigDecimal t1145;

    @Column(name = "T1200")
    private BigDecimal t1200;

    @Column(name = "T1215")
    private BigDecimal t1215;

    @Column(name = "T1230")
    private BigDecimal t1230;

    @Column(name = "T1245")
    private BigDecimal t1245;

    @Column(name = "T1300")
    private BigDecimal t1300;

    @Column(name = "T1315")
    private BigDecimal t1315;

    @Column(name = "T1330")
    private BigDecimal t1330;

    @Column(name = "T1345")
    private BigDecimal t1345;

    @Column(name = "T1400")
    private BigDecimal t1400;

    @Column(name = "T1415")
    private BigDecimal t1415;

    @Column(name = "T1430")
    private BigDecimal t1430;

    @Column(name = "T1445")
    private BigDecimal t1445;

    @Column(name = "T1500")
    private BigDecimal t1500;

    @Column(name = "T1515")
    private BigDecimal t1515;

    @Column(name = "T1530")
    private BigDecimal t1530;

    @Column(name = "T1545")
    private BigDecimal t1545;

    @Column(name = "T1600")
    private BigDecimal t1600;

    @Column(name = "T1615")
    private BigDecimal t1615;

    @Column(name = "T1630")
    private BigDecimal t1630;

    @Column(name = "T1645")
    private BigDecimal t1645;

    @Column(name = "T1700")
    private BigDecimal t1700;

    @Column(name = "T1715")
    private BigDecimal t1715;

    @Column(name = "T1730")
    private BigDecimal t1730;

    @Column(name = "T1745")
    private BigDecimal t1745;

    @Column(name = "T1800")
    private BigDecimal t1800;

    @Column(name = "T1815")
    private BigDecimal t1815;

    @Column(name = "T1830")
    private BigDecimal t1830;

    @Column(name = "T1845")
    private BigDecimal t1845;

    @Column(name = "T1900")
    private BigDecimal t1900;

    @Column(name = "T1915")
    private BigDecimal t1915;

    @Column(name = "T1930")
    private BigDecimal t1930;

    @Column(name = "T1945")
    private BigDecimal t1945;

    @Column(name = "T2000")
    private BigDecimal t2000;

    @Column(name = "T2015")
    private BigDecimal t2015;

    @Column(name = "T2030")
    private BigDecimal t2030;

    @Column(name = "T2045")
    private BigDecimal t2045;

    @Column(name = "T2100")
    private BigDecimal t2100;

    @Column(name = "T2115")
    private BigDecimal t2115;

    @Column(name = "T2130")
    private BigDecimal t2130;

    @Column(name = "T2145")
    private BigDecimal t2145;

    @Column(name = "T2200")
    private BigDecimal t2200;

    @Column(name = "T2215")
    private BigDecimal t2215;

    @Column(name = "T2230")
    private BigDecimal t2230;

    @Column(name = "T2245")
    private BigDecimal t2245;

    @Column(name = "T2300")
    private BigDecimal t2300;

    @Column(name = "T2315")
    private BigDecimal t2315;

    @Column(name = "T2330")
    private BigDecimal t2330;

    @Column(name = "T2345")
    private BigDecimal t2345;

    @Column(name = "T2400")
    private BigDecimal t2400;


    @Override
    public BigDecimal getT0015() {
        return t0015;
    }

    @Override
    public void setT0015(BigDecimal t0015) {
        this.t0015 = t0015;
    }

    @Override
    public BigDecimal getT0030() {
        return t0030;
    }

    @Override
    public void setT0030(BigDecimal t0030) {
        this.t0030 = t0030;
    }

    @Override
    public BigDecimal getT0045() {
        return t0045;
    }

    @Override
    public void setT0045(BigDecimal t0045) {
        this.t0045 = t0045;
    }

    @Override
    public BigDecimal getT0115() {
        return t0115;
    }

    @Override
    public void setT0115(BigDecimal t0115) {
        this.t0115 = t0115;
    }

    @Override
    public BigDecimal getT0130() {
        return t0130;
    }

    @Override
    public void setT0130(BigDecimal t0130) {
        this.t0130 = t0130;
    }

    @Override
    public BigDecimal getT0145() {
        return t0145;
    }

    @Override
    public void setT0145(BigDecimal t0145) {
        this.t0145 = t0145;
    }

    @Override
    public BigDecimal getT0215() {
        return t0215;
    }

    @Override
    public void setT0215(BigDecimal t0215) {
        this.t0215 = t0215;
    }

    @Override
    public BigDecimal getT0230() {
        return t0230;
    }

    @Override
    public void setT0230(BigDecimal t0230) {
        this.t0230 = t0230;
    }

    @Override
    public BigDecimal getT0245() {
        return t0245;
    }

    @Override
    public void setT0245(BigDecimal t0245) {
        this.t0245 = t0245;
    }

    @Override
    public BigDecimal getT0315() {
        return t0315;
    }

    @Override
    public void setT0315(BigDecimal t0315) {
        this.t0315 = t0315;
    }

    @Override
    public BigDecimal getT0330() {
        return t0330;
    }

    @Override
    public void setT0330(BigDecimal t0330) {
        this.t0330 = t0330;
    }

    @Override
    public BigDecimal getT0345() {
        return t0345;
    }

    @Override
    public void setT0345(BigDecimal t0345) {
        this.t0345 = t0345;
    }

    @Override
    public BigDecimal getT0415() {
        return t0415;
    }

    @Override
    public void setT0415(BigDecimal t0415) {
        this.t0415 = t0415;
    }

    @Override
    public BigDecimal getT0430() {
        return t0430;
    }

    @Override
    public void setT0430(BigDecimal t0430) {
        this.t0430 = t0430;
    }

    @Override
    public BigDecimal getT0445() {
        return t0445;
    }

    @Override
    public void setT0445(BigDecimal t0445) {
        this.t0445 = t0445;
    }

    @Override
    public BigDecimal getT0515() {
        return t0515;
    }

    @Override
    public void setT0515(BigDecimal t0515) {
        this.t0515 = t0515;
    }

    @Override
    public BigDecimal getT0530() {
        return t0530;
    }

    @Override
    public void setT0530(BigDecimal t0530) {
        this.t0530 = t0530;
    }

    @Override
    public BigDecimal getT0545() {
        return t0545;
    }

    @Override
    public void setT0545(BigDecimal t0545) {
        this.t0545 = t0545;
    }

    @Override
    public BigDecimal getT0615() {
        return t0615;
    }

    @Override
    public void setT0615(BigDecimal t0615) {
        this.t0615 = t0615;
    }

    @Override
    public BigDecimal getT0630() {
        return t0630;
    }

    @Override
    public void setT0630(BigDecimal t0630) {
        this.t0630 = t0630;
    }

    @Override
    public BigDecimal getT0645() {
        return t0645;
    }

    @Override
    public void setT0645(BigDecimal t0645) {
        this.t0645 = t0645;
    }

    @Override
    public BigDecimal getT0715() {
        return t0715;
    }

    @Override
    public void setT0715(BigDecimal t0715) {
        this.t0715 = t0715;
    }

    @Override
    public BigDecimal getT0730() {
        return t0730;
    }

    @Override
    public void setT0730(BigDecimal t0730) {
        this.t0730 = t0730;
    }

    @Override
    public BigDecimal getT0745() {
        return t0745;
    }

    @Override
    public void setT0745(BigDecimal t0745) {
        this.t0745 = t0745;
    }

    @Override
    public BigDecimal getT0815() {
        return t0815;
    }

    @Override
    public void setT0815(BigDecimal t0815) {
        this.t0815 = t0815;
    }

    @Override
    public BigDecimal getT0830() {
        return t0830;
    }

    @Override
    public void setT0830(BigDecimal t0830) {
        this.t0830 = t0830;
    }

    @Override
    public BigDecimal getT0845() {
        return t0845;
    }

    @Override
    public void setT0845(BigDecimal t0845) {
        this.t0845 = t0845;
    }

    @Override
    public BigDecimal getT0915() {
        return t0915;
    }

    @Override
    public void setT0915(BigDecimal t0915) {
        this.t0915 = t0915;
    }

    @Override
    public BigDecimal getT0930() {
        return t0930;
    }

    @Override
    public void setT0930(BigDecimal t0930) {
        this.t0930 = t0930;
    }

    @Override
    public BigDecimal getT0945() {
        return t0945;
    }

    @Override
    public void setT0945(BigDecimal t0945) {
        this.t0945 = t0945;
    }

    @Override
    public BigDecimal getT1015() {
        return t1015;
    }

    @Override
    public void setT1015(BigDecimal t1015) {
        this.t1015 = t1015;
    }

    @Override
    public BigDecimal getT1030() {
        return t1030;
    }

    @Override
    public void setT1030(BigDecimal t1030) {
        this.t1030 = t1030;
    }

    @Override
    public BigDecimal getT1045() {
        return t1045;
    }

    @Override
    public void setT1045(BigDecimal t1045) {
        this.t1045 = t1045;
    }

    @Override
    public BigDecimal getT1115() {
        return t1115;
    }

    @Override
    public void setT1115(BigDecimal t1115) {
        this.t1115 = t1115;
    }

    @Override
    public BigDecimal getT1130() {
        return t1130;
    }

    @Override
    public void setT1130(BigDecimal t1130) {
        this.t1130 = t1130;
    }

    @Override
    public BigDecimal getT1145() {
        return t1145;
    }

    @Override
    public void setT1145(BigDecimal t1145) {
        this.t1145 = t1145;
    }

    @Override
    public BigDecimal getT1215() {
        return t1215;
    }

    @Override
    public void setT1215(BigDecimal t1215) {
        this.t1215 = t1215;
    }

    @Override
    public BigDecimal getT1230() {
        return t1230;
    }

    @Override
    public void setT1230(BigDecimal t1230) {
        this.t1230 = t1230;
    }

    @Override
    public BigDecimal getT1245() {
        return t1245;
    }

    @Override
    public void setT1245(BigDecimal t1245) {
        this.t1245 = t1245;
    }

    @Override
    public BigDecimal getT1315() {
        return t1315;
    }

    @Override
    public void setT1315(BigDecimal t1315) {
        this.t1315 = t1315;
    }

    @Override
    public BigDecimal getT1330() {
        return t1330;
    }

    @Override
    public void setT1330(BigDecimal t1330) {
        this.t1330 = t1330;
    }

    @Override
    public BigDecimal getT1345() {
        return t1345;
    }

    @Override
    public void setT1345(BigDecimal t1345) {
        this.t1345 = t1345;
    }

    @Override
    public BigDecimal getT1415() {
        return t1415;
    }

    @Override
    public void setT1415(BigDecimal t1415) {
        this.t1415 = t1415;
    }

    @Override
    public BigDecimal getT1430() {
        return t1430;
    }

    @Override
    public void setT1430(BigDecimal t1430) {
        this.t1430 = t1430;
    }

    @Override
    public BigDecimal getT1445() {
        return t1445;
    }

    @Override
    public void setT1445(BigDecimal t1445) {
        this.t1445 = t1445;
    }

    @Override
    public BigDecimal getT1515() {
        return t1515;
    }

    @Override
    public void setT1515(BigDecimal t1515) {
        this.t1515 = t1515;
    }

    @Override
    public BigDecimal getT1530() {
        return t1530;
    }

    @Override
    public void setT1530(BigDecimal t1530) {
        this.t1530 = t1530;
    }

    @Override
    public BigDecimal getT1545() {
        return t1545;
    }

    @Override
    public void setT1545(BigDecimal t1545) {
        this.t1545 = t1545;
    }

    @Override
    public BigDecimal getT1615() {
        return t1615;
    }

    @Override
    public void setT1615(BigDecimal t1615) {
        this.t1615 = t1615;
    }

    @Override
    public BigDecimal getT1630() {
        return t1630;
    }

    @Override
    public void setT1630(BigDecimal t1630) {
        this.t1630 = t1630;
    }

    @Override
    public BigDecimal getT1645() {
        return t1645;
    }

    @Override
    public void setT1645(BigDecimal t1645) {
        this.t1645 = t1645;
    }

    @Override
    public BigDecimal getT1715() {
        return t1715;
    }

    @Override
    public void setT1715(BigDecimal t1715) {
        this.t1715 = t1715;
    }

    @Override
    public BigDecimal getT1730() {
        return t1730;
    }

    @Override
    public void setT1730(BigDecimal t1730) {
        this.t1730 = t1730;
    }

    @Override
    public BigDecimal getT1745() {
        return t1745;
    }

    @Override
    public void setT1745(BigDecimal t1745) {
        this.t1745 = t1745;
    }

    @Override
    public BigDecimal getT1815() {
        return t1815;
    }

    @Override
    public void setT1815(BigDecimal t1815) {
        this.t1815 = t1815;
    }

    @Override
    public BigDecimal getT1830() {
        return t1830;
    }

    @Override
    public void setT1830(BigDecimal t1830) {
        this.t1830 = t1830;
    }

    @Override
    public BigDecimal getT1845() {
        return t1845;
    }

    @Override
    public void setT1845(BigDecimal t1845) {
        this.t1845 = t1845;
    }

    @Override
    public BigDecimal getT1915() {
        return t1915;
    }

    @Override
    public void setT1915(BigDecimal t1915) {
        this.t1915 = t1915;
    }

    @Override
    public BigDecimal getT1930() {
        return t1930;
    }

    @Override
    public void setT1930(BigDecimal t1930) {
        this.t1930 = t1930;
    }

    @Override
    public BigDecimal getT1945() {
        return t1945;
    }

    @Override
    public void setT1945(BigDecimal t1945) {
        this.t1945 = t1945;
    }

    @Override
    public BigDecimal getT2015() {
        return t2015;
    }

    @Override
    public void setT2015(BigDecimal t2015) {
        this.t2015 = t2015;
    }

    @Override
    public BigDecimal getT2030() {
        return t2030;
    }

    @Override
    public void setT2030(BigDecimal t2030) {
        this.t2030 = t2030;
    }

    @Override
    public BigDecimal getT2045() {
        return t2045;
    }

    @Override
    public void setT2045(BigDecimal t2045) {
        this.t2045 = t2045;
    }

    @Override
    public BigDecimal getT2115() {
        return t2115;
    }

    @Override
    public void setT2115(BigDecimal t2115) {
        this.t2115 = t2115;
    }

    @Override
    public BigDecimal getT2130() {
        return t2130;
    }

    @Override
    public void setT2130(BigDecimal t2130) {
        this.t2130 = t2130;
    }

    @Override
    public BigDecimal getT2145() {
        return t2145;
    }

    @Override
    public void setT2145(BigDecimal t2145) {
        this.t2145 = t2145;
    }

    @Override
    public BigDecimal getT2215() {
        return t2215;
    }

    @Override
    public void setT2215(BigDecimal t2215) {
        this.t2215 = t2215;
    }

    @Override
    public BigDecimal getT2230() {
        return t2230;
    }

    @Override
    public void setT2230(BigDecimal t2230) {
        this.t2230 = t2230;
    }

    @Override
    public BigDecimal getT2245() {
        return t2245;
    }

    @Override
    public void setT2245(BigDecimal t2245) {
        this.t2245 = t2245;
    }

    @Override
    public BigDecimal getT2315() {
        return t2315;
    }

    @Override
    public void setT2315(BigDecimal t2315) {
        this.t2315 = t2315;
    }

    @Override
    public BigDecimal getT2330() {
        return t2330;
    }

    @Override
    public void setT2330(BigDecimal t2330) {
        this.t2330 = t2330;
    }

    @Override
    public BigDecimal getT2345() {
        return t2345;
    }

    @Override
    public void setT2345(BigDecimal t2345) {
        this.t2345 = t2345;
    }

    @Override
    public BigDecimal getT0000() {
        return t0000;
    }

    @Override
    public void setT0000(BigDecimal t0000) {
        this.t0000 = t0000;
    }

    @Override
    public BigDecimal getT0100() {
        return t0100;
    }

    @Override
    public void setT0100(BigDecimal t0100) {
        this.t0100 = t0100;
    }

    @Override
    public BigDecimal getT0200() {
        return t0200;
    }

    @Override
    public void setT0200(BigDecimal t0200) {
        this.t0200 = t0200;
    }

    @Override
    public BigDecimal getT0300() {
        return t0300;
    }

    @Override
    public void setT0300(BigDecimal t0300) {
        this.t0300 = t0300;
    }

    @Override
    public BigDecimal getT0400() {
        return t0400;
    }

    @Override
    public void setT0400(BigDecimal t0400) {
        this.t0400 = t0400;
    }

    @Override
    public BigDecimal getT0500() {
        return t0500;
    }

    @Override
    public void setT0500(BigDecimal t0500) {
        this.t0500 = t0500;
    }

    @Override
    public BigDecimal getT0600() {
        return t0600;
    }

    @Override
    public void setT0600(BigDecimal t0600) {
        this.t0600 = t0600;
    }

    @Override
    public BigDecimal getT0700() {
        return t0700;
    }

    @Override
    public void setT0700(BigDecimal t0700) {
        this.t0700 = t0700;
    }

    @Override
    public BigDecimal getT0800() {
        return t0800;
    }

    @Override
    public void setT0800(BigDecimal t0800) {
        this.t0800 = t0800;
    }

    @Override
    public BigDecimal getT0900() {
        return t0900;
    }

    @Override
    public void setT0900(BigDecimal t0900) {
        this.t0900 = t0900;
    }

    @Override
    public BigDecimal getT1000() {
        return t1000;
    }

    @Override
    public void setT1000(BigDecimal t1000) {
        this.t1000 = t1000;
    }

    @Override
    public BigDecimal getT1100() {
        return t1100;
    }

    @Override
    public void setT1100(BigDecimal t1100) {
        this.t1100 = t1100;
    }

    @Override
    public BigDecimal getT1200() {
        return t1200;
    }

    @Override
    public void setT1200(BigDecimal t1200) {
        this.t1200 = t1200;
    }

    @Override
    public BigDecimal getT1300() {
        return t1300;
    }

    @Override
    public void setT1300(BigDecimal t1300) {
        this.t1300 = t1300;
    }

    @Override
    public BigDecimal getT1400() {
        return t1400;
    }

    @Override
    public void setT1400(BigDecimal t1400) {
        this.t1400 = t1400;
    }

    @Override
    public BigDecimal getT1500() {
        return t1500;
    }

    @Override
    public void setT1500(BigDecimal t1500) {
        this.t1500 = t1500;
    }

    @Override
    public BigDecimal getT1600() {
        return t1600;
    }

    @Override
    public void setT1600(BigDecimal t1600) {
        this.t1600 = t1600;
    }

    @Override
    public BigDecimal getT1700() {
        return t1700;
    }

    @Override
    public void setT1700(BigDecimal t1700) {
        this.t1700 = t1700;
    }

    @Override
    public BigDecimal getT1800() {
        return t1800;
    }

    @Override
    public void setT1800(BigDecimal t1800) {
        this.t1800 = t1800;
    }

    @Override
    public BigDecimal getT1900() {
        return t1900;
    }

    @Override
    public void setT1900(BigDecimal t1900) {
        this.t1900 = t1900;
    }

    @Override
    public BigDecimal getT2000() {
        return t2000;
    }

    @Override
    public void setT2000(BigDecimal t2000) {
        this.t2000 = t2000;
    }

    @Override
    public BigDecimal getT2100() {
        return t2100;
    }

    @Override
    public void setT2100(BigDecimal t2100) {
        this.t2100 = t2100;
    }

    @Override
    public BigDecimal getT2200() {
        return t2200;
    }

    @Override
    public void setT2200(BigDecimal t2200) {
        this.t2200 = t2200;
    }

    @Override
    public BigDecimal getT2300() {
        return t2300;
    }

    @Override
    public void setT2300(BigDecimal t2300) {
        this.t2300 = t2300;
    }

    @Override
    public BigDecimal getT2400() {
        return t2400;
    }

    @Override
    public void setT2400(BigDecimal t2400) {
        this.t2400 = t2400;
    }

    @Override
    public String toString() {
        return "Base96DO{" +
                "t0015=" + t0015 +
                ", t0030=" + t0030 +
                ", t0045=" + t0045 +
                ", t0115=" + t0115 +
                ", t0130=" + t0130 +
                ", t0145=" + t0145 +
                ", t0215=" + t0215 +
                ", t0230=" + t0230 +
                ", t0245=" + t0245 +
                ", t0315=" + t0315 +
                ", t0330=" + t0330 +
                ", t0345=" + t0345 +
                ", t0415=" + t0415 +
                ", t0430=" + t0430 +
                ", t0445=" + t0445 +
                ", t0515=" + t0515 +
                ", t0530=" + t0530 +
                ", t0545=" + t0545 +
                ", t0615=" + t0615 +
                ", t0630=" + t0630 +
                ", t0645=" + t0645 +
                ", t0715=" + t0715 +
                ", t0730=" + t0730 +
                ", t0745=" + t0745 +
                ", t0815=" + t0815 +
                ", t0830=" + t0830 +
                ", t0845=" + t0845 +
                ", t0915=" + t0915 +
                ", t0930=" + t0930 +
                ", t0945=" + t0945 +
                ", t1015=" + t1015 +
                ", t1030=" + t1030 +
                ", t1045=" + t1045 +
                ", t1115=" + t1115 +
                ", t1130=" + t1130 +
                ", t1145=" + t1145 +
                ", t1215=" + t1215 +
                ", t1230=" + t1230 +
                ", t1245=" + t1245 +
                ", t1315=" + t1315 +
                ", t1330=" + t1330 +
                ", t1345=" + t1345 +
                ", t1415=" + t1415 +
                ", t1430=" + t1430 +
                ", t1445=" + t1445 +
                ", t1515=" + t1515 +
                ", t1530=" + t1530 +
                ", t1545=" + t1545 +
                ", t1615=" + t1615 +
                ", t1630=" + t1630 +
                ", t1645=" + t1645 +
                ", t1715=" + t1715 +
                ", t1730=" + t1730 +
                ", t1745=" + t1745 +
                ", t1815=" + t1815 +
                ", t1830=" + t1830 +
                ", t1845=" + t1845 +
                ", t1915=" + t1915 +
                ", t1930=" + t1930 +
                ", t1945=" + t1945 +
                ", t2015=" + t2015 +
                ", t2030=" + t2030 +
                ", t2045=" + t2045 +
                ", t2115=" + t2115 +
                ", t2130=" + t2130 +
                ", t2145=" + t2145 +
                ", t2215=" + t2215 +
                ", t2230=" + t2230 +
                ", t2245=" + t2245 +
                ", t2315=" + t2315 +
                ", t2330=" + t2330 +
                ", t2345=" + t2345 +
                ", t0000=" + t0000 +
                ", t0100=" + t0100 +
                ", t0200=" + t0200 +
                ", t0300=" + t0300 +
                ", t0400=" + t0400 +
                ", t0500=" + t0500 +
                ", t0600=" + t0600 +
                ", t0700=" + t0700 +
                ", t0800=" + t0800 +
                ", t0900=" + t0900 +
                ", t1000=" + t1000 +
                ", t1100=" + t1100 +
                ", t1200=" + t1200 +
                ", t1300=" + t1300 +
                ", t1400=" + t1400 +
                ", t1500=" + t1500 +
                ", t1600=" + t1600 +
                ", t1700=" + t1700 +
                ", t1800=" + t1800 +
                ", t1900=" + t1900 +
                ", t2000=" + t2000 +
                ", t2100=" + t2100 +
                ", t2200=" + t2200 +
                ", t2300=" + t2300 +
                ", t2400=" + t2400 +
                '}';
    }
}
