/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/6/25 2:52
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.typhoon.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @create 2019/8/15
 * @since 1.0.0
 */
public class TyphoonAtomDTO implements Serializable {

    private static final long serialVersionUID = -5996030634406990869L;

    private Date date;

    private int leave;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public int getLeave() {
        return leave;
    }

    public void setLeave(int leave) {
        this.leave = leave;
    }
}