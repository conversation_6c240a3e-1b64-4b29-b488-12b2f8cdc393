package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: 月准确率的数据DTO <br>
 *
 * <AUTHOR>
 * @create 2021/4/14
 * @since 1.0.0
 */
@Data
public class MonthPrecisionDTO implements Serializable {

    @ApiModelProperty(value = "月份时间", example = "2020-01")
    private String monthDate;

    @ApiModelProperty(value = "月准确率的最大值", example = "99.99")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal monthMax;

    @ApiModelProperty(value = "月准确率的最小值", example = "97.77")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal monthMin;

    @ApiModelProperty(value = "月准确率的平均值", example = "98.88")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal monthAvg;
}
