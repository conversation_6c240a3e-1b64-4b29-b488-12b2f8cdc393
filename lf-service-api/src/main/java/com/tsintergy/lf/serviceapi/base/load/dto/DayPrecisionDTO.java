package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/4/14
 * @since 1.0.0
 */
@Data
public class DayPrecisionDTO implements Serializable {

    @ApiModelProperty(value = "逐日准确率的日期", example = "3-1")
    private String dayDate;

    @ApiModelProperty(value = "逐日准确率的值", example = "97.00")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal dayPrecision;

    @ApiModelProperty(value = "逐日准确率是否达到标准", example = "true")
    private Boolean standardFlag;
}
