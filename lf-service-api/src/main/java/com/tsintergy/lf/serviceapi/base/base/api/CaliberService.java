
package com.tsintergy.lf.serviceapi.base.base.api;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: CaliberService.java, v 0.1 2018-06-05 11:13:30 tao Exp $$
 */

public interface CaliberService {
   
    
     /**
     * create entity
     * @param vo
     * @return
     * @throws BusinessException
     */
     CaliberDO doCreate(CaliberDO vo) throws BusinessException;
    

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws BusinessException
     */
     CaliberDO findCaliberDOByPk(Serializable pk) throws BusinessException;

    /**
     * 获取全部口径
     * @return
     * @throws BusinessException
     */
     List<CaliberDO> findAllCalibers() throws BusinessException;

    /**
     * 根据名称查询口径
     * @param name
     * @return
     * @throws BusinessException
     */
     CaliberDO findCaliberDOByName(String name) throws BusinessException;

    /**
     * 查询系统默认口径
     * @return
     * @throws BusinessException
     */
     CaliberDO findSystemDefalutCaliberDO() throws BusinessException;


}