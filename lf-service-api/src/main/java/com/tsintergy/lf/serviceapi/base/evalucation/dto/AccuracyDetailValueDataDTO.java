package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-02-13
 * @since 1.0.0
 */
@Data
public class AccuracyDetailValueDataDTO implements DTO {

    private String algorithmName;

    private String dateStr;

    private BigDecimal dayAccuracy;

    private BigDecimal hisMaxLoad;

    private BigDecimal fcMaxLoad;

    private BigDecimal maxLoadAccuracy;

    private BigDecimal hisMinimumLoad;

    private BigDecimal fcMinimumLoad;

    private BigDecimal fcMinimumLoadAccuracy;

    private BigDecimal hisMinimumNightLoad;

    private BigDecimal fcMinimumNightLoad;

    private BigDecimal minimumNightLoadAccuracy;

    private List<AccuracyDetailDataDTO> accuracyDetailDataDTOList;

    private BigDecimal pointsAccuracy;

}
