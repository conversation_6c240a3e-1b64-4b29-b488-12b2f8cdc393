/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/6/27 2:08
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.lf.serviceapi.base.analyze.dto.WeatherElement;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 综合相似日 算法参数对象
 *
 * <AUTHOR>
 * @create 2020/8/21
 * @since 1.0.0
 */
@Data
public class SimilarParam extends Param {

    private Date date;

    private Date startDate;

    private Date endDate;

    /**
     * 展示结果的数量
     */
    private Integer pageSize;

    private List<WeatherElement> elements;

    /**
     * 丰枯期设置 平水期1 丰水期2、枯水期3
     */
    private List<Integer> phase;

    /**
     * 负荷天数选择  0当日 1前一日 2前两日
     */
    private List<Integer> loadElements;

    /**
     * 相似度计算方式 0数值相似 1趋势相似
     */
    private Integer compute;

    /**
     * 搜索日期最后一天数据  0 or null 历史数据   1 预测数据  （自动触发最新一天为预测数据，综合相似页面出发使用历史数据）
     */
    private String newestLoad;

}