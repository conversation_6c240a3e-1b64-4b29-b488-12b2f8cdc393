package com.tsintergy.lf.serviceapi.base.load.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.PercentSerialize2Digits;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @date: 3/21/18 4:41 PM
 * @author: angel
 **/
@ApiModel
@Data
public class LoadFeatureFcDTO implements Serializable {

    /**
     * 最大负荷
     */
    @ApiModelProperty(value = "最大负荷")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @ApiModelProperty(value = "最小负荷")
    private BigDecimal minLoad;

    @ApiModelProperty(value = "午间最大负荷")
    private BigDecimal noonMaxLoad;

    @ApiModelProperty(value = "午间最大负荷时刻")
    private String noonMaxTime;

    @ApiModelProperty(value = "夜间最大负荷")
    private BigDecimal eveningMaxLoad;

    @ApiModelProperty(value = "夜间最大负荷时刻")
    private String eveningMaxTime;
    /**
     * 平均负荷
     */
    @ApiModelProperty(value = "平均负荷")
    private BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    @ApiModelProperty(value = "最大负荷发生时刻")
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @ApiModelProperty(value = "最小负荷发生时刻")
    private String minTime;

    /**
     * 峰谷差
     */
    @ApiModelProperty(value = "峰谷差")
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @JsonSerialize(using = PercentSerialize2Digits.class)
    @ApiModelProperty(value = "峰谷差率")
    private BigDecimal gradient;


    /**
     * 积分电量
     */
    @ApiModelProperty(value = "积分电量",example = "2312")
    private BigDecimal integralLoad;

    /**
     * 峰段电量
     */
    @ApiModelProperty(value = "峰段电量",example = "12321")
    private BigDecimal peakSectionLoad;


}
