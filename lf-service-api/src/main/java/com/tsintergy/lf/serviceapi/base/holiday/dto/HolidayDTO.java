/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  gss Date:  2019/6/26 9:31 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.holiday.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 节假日分析设置年数据
 *
 * <AUTHOR>
 * @create 2019/6/26
 * @since 1.0.0
 */
@ApiModel
public class HolidayDTO implements Serializable {
    
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份",example = "2021")
    private String year;

    /**
     * 假日开始时间
     */
    @ApiModelProperty(value = "假日开始时间",example = "2021-03-17")
    private String startDate;

    /**
     * 假日结束时间
     */
    @ApiModelProperty(value = "假日结束时间",example = "2021-03-17")
    private String endDate;


    /**
     * 最大基准
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大基准",example = "20217")
    private BigDecimal max;

    /**
     * 最小基准
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小基准",example = "117")
    private BigDecimal min;


    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getMax() {
        return max;
    }

    public void setMax(BigDecimal max) {
        this.max = max;
    }

    public BigDecimal getMin() {
        return min;
    }

    public void setMin(BigDecimal min) {
        this.min = min;
    }
}
