package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import lombok.Data;

import java.util.Date;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 14:00
 **/
@Data
public class MultiDayFeatureQueryDTO implements DTO {

    private Date date;

    private LoadFeatureFcDTO feature;
}
