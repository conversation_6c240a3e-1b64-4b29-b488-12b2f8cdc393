/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wa<PERSON><PERSON>
 * Date:  2019/12/25 6:56
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/12/25
 * @since 1.0.0
 */

public class AutoForecastDTO implements Serializable {

    private boolean reportSwitch;

    private String selectAlgorithmId;

    private Date cuteDate;

    public boolean isReportSwitch() {
        return reportSwitch;
    }

    public void setReportSwitch(boolean reportSwitch) {
        this.reportSwitch = reportSwitch;
    }

    public String getSelectAlgorithmId() {
        return selectAlgorithmId;
    }

    public void setSelectAlgorithmId(String selectAlgorithmId) {
        this.selectAlgorithmId = selectAlgorithmId;
    }

    public Date getCuteDate() {
        return cuteDate;
    }

    public void setCuteDate(Date cuteDate) {
        this.cuteDate = cuteDate;
    }
}