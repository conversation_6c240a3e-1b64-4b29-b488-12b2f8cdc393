/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/3/5 10:23
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import java.io.Serializable;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/3/5
 * @since 1.0.0
 */
public class PeakDTO implements Serializable {

    private Integer type;

    private List<String> morning;

    private List<String> afternoon;

    private List<String> evening;

    private List<String> night;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<String> getMorning() {
        return morning;
    }

    public void setMorning(List<String> morning) {
        this.morning = morning;
    }

    public List<String> getAfternoon() {
        return afternoon;
    }

    public void setAfternoon(List<String> afternoon) {
        this.afternoon = afternoon;
    }

    public List<String> getEvening() {
        return evening;
    }

    public void setEvening(List<String> evening) {
        this.evening = evening;
    }

    public List<String> getNight() {
        return night;
    }

    public void setNight(List<String> night) {
        this.night = night;
    }
}