package com.tsintergy.lf.serviceapi.base.bus.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * base_bus
 * 站点基础信息表
 */
@Data
@Entity
@Table(name = "base_unit_clct_init")
@ApiModel(description = "机组&主变基础信息表")
public class BaseUnitDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "5")
    private String id;


    @ApiModelProperty(value = "站点名称")
    @Column(name = "NAME")
    private String name;


    @ApiModelProperty(value = "所属城市id")
    @Column(name = "CITY_ID")
    private String cityId;


    @ApiModelProperty(value = "站点容量")
    @Column(name = "VOLTAGE")
    private BigDecimal voltage;
    

    @ApiModelProperty(value = "UNIT_TYPE")
    @Column(name = "UNIT_TYPE")
    private Integer unitType;


    @Column(name = "STATION_ID")
    private String stationId;

    @Column(name = "TR_ID")
    private String trId;


    @ApiModelProperty(value = "创建时间")
    @Column(name = "CREATED_TIME")
    private Date createdtime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "UPDATED_TIME")
    private Date updatedtime;

}