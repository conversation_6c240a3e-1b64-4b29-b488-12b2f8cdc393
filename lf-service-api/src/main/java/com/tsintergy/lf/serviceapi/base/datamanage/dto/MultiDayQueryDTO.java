package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import com.tsintergy.lf.serviceapi.base.load.dto.DayValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 多批次准确率曲线结果
 */
@Data
public class MultiDayQueryDTO implements Serializable {

    @ApiModelProperty("多日负荷数据")
    private List<MultiDayLoadQueryDTO> multiDayLoad;

    @ApiModelProperty("多日实际负荷特性数据")
    private List<MultiDayFeatureQueryDTO> multiDayHisFeature;

    @ApiModelProperty("多日预测负荷特性数据")
    private List<MultiDayFeatureQueryDTO> multiDayFcFeature;

    @ApiModelProperty("多日最大负荷及其发生日期")
    private DayValueDTO multiDayMaxLoad;

    @ApiModelProperty("多日最大午峰负荷及其发生日期")
    private DayValueDTO multiDayMaxNoonLoad;

    @ApiModelProperty("多日最大晚峰负荷及其发生日期")
    private DayValueDTO multiDayMaxEveningLoad;

    @ApiModelProperty("多日最小负荷及其发生日期")
    private DayValueDTO multiDayMinLoad;

    @ApiModelProperty("单日最大峰谷差及其发生日期")
    private DayValueDTO multiDayDifMaxLoad;

    @ApiModelProperty("多日最小午峰负荷及其发生日期")
    private DayValueDTO multiDayMinNoonLoad;

    @ApiModelProperty("多日最小晚峰负荷及其发生日期")
    private DayValueDTO multiDayMinEveningLoad;

    @ApiModelProperty("单日最大峰谷差率及其发生日期")
    private DayValueDTO multiDayDifMaxRate;

}
