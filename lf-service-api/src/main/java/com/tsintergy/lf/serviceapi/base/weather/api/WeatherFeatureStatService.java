package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsintergy.lf.serviceapi.base.weather.pojo.*;

import java.util.Date;
import java.util.List;

/**
 * 负荷特性统计接口
 * User:taojingui
 * Date:18-2-23
 * Time:上午11:53
 */
public interface WeatherFeatureStatService {

    /**
     * 统计气象特性并入库（日）
     *
     * @param cityIds   城市ID列表 代码内部匹配对应气象城市
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    List<WeatherFeatureCityDayHisDO> doStatWeatherFeatureCityDay(List<String> cityIds, Date startDate, Date endDate) throws Exception;


    List<WeatherFeatureCityDayHisDO> statisticsDayFeature(List<WeatherCityHisDO> weatherCityHisVOs);

    /**
     * 统计气象特性并入库（月）
     *
     * @param cityIds   城市ID列表 代码内部匹配对应气象城市
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    List<WeatherFeatureCityMonthHisDO> doStatWeatherFeatureCityMonth(List<String> cityIds, Date startDate, Date endDate) throws Exception;

    /**
     * 统计气象特性并入库（季）
     * @param cityIds 城市ID列表 代码内部匹配对应气象城市
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
     List<WeatherFeatureCityQuarterHisDO> doStatWeatherFeatureCityQuarter(List<String> cityIds, Date startDate,
                                                                                Date endDate) throws Exception;


    /**
     * 统计气象特性并入库（日）预测
     * @param cityId 城市ID  代码内部匹配对应气象城市
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
     List<WeatherFeatureCityDayFcDO> doStatWeatherFeatureCityDayFc(String cityId, Date startDate, Date endDate) throws Exception;

    /**
     * 计算气象特性
     * @param weatherCityFcVO 气象城市预测数据list
     */
    WeatherFeatureCityDayFcDO doStatWeatherByCityAndType(List<?extends BaseWeatherDO> weatherCityFcVO) throws Exception;


}
