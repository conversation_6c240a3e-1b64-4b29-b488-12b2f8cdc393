package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 17:11
 **/
public interface WeatherCityFcBatchService {

    List<WeatherCityFcBatchDO> findByCondition(String cityId, String weatherSource, Date preDate,Integer type,String batchId) throws Exception;

    void doSaveOuUpdatePreLoadWeather(WeatherCityFcBatchDO weatherCityFcBatchDO);


}
