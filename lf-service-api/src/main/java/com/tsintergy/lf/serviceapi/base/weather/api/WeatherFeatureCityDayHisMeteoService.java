
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayHisService.java, v 0.1 2018-01-31 11:00:36 tao Exp $$
 */

public interface WeatherFeatureCityDayHisMeteoService {



     List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeatureCityDayHisMeteoDO(List<String> cityIds, Date startDate, Date endDate) throws Exception;


     List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeatureCityDayHisMeteoDO(String cityId, Date startDate, Date endDate) throws Exception;


}