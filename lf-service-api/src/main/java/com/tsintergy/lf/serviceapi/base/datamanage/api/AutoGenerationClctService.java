package com.tsintergy.lf.serviceapi.base.datamanage.api;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;

import java.util.Date;
import java.util.List;

/**
 *  Description: 采集数据 <br> 
 *  
 *  <AUTHOR>
 *  @create 2021/1/21
 *  @since 1.0.0 
 */
public interface AutoGenerationClctService {

    void doDealSingleLoadCity(Date date,  List<CityDO> cityDOS);

    void doDealSingleWeatherHis(Date date, List<CityDO> cityDOS) throws Exception;

    void doDealSingleWeatherFc(Date date, List<CityDO> cityDOS) throws Exception;
}
