package com.tsintergy.lf.serviceapi.base.common.jsonserialize;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * User:taojingui
 * Date:18-4-18
 * Time:上午10:46
 */
public class BigDecimalSerialize1Digits extends JsonSerializer<BigDecimal> {

    /**
     * 小数位
     */
    private int digits = 1;

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null != bigDecimal) {
            jsonGenerator.writeNumber(bigDecimal.setScale(digits,BigDecimal.ROUND_HALF_UP));
        }
    }

}
