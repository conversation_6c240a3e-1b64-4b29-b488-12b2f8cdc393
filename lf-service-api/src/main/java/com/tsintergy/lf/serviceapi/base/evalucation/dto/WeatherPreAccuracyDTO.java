/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/8/2614:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.evalucation.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2019/8/26
 *@since 1.0.0
 */
@ApiModel
public class WeatherPreAccuracyDTO implements Serializable {

    @ApiModelProperty(value = "城市id",example = "5")
    String cityId;

    @JsonIgnore
    @ApiModelProperty(value = "城市名称",example = "抚顺")
    String cityName;

    @JsonIgnore
    @ApiModelProperty(value = "日期",example = "2021-03-17")
    String date;

    @ApiModelProperty(value = "天气平均温度准确率")
    WeatherAvgTemAccuracyDTO avgTempeture;

    @ApiModelProperty(value = "天气高温准确率")
    WeatherHighTemAccuracyDTO highTempeture;

    @ApiModelProperty(value = "天气低温准确率")
    WeatherLowTemAccuracyDTO lowTempeture;

    public WeatherPreAccuracyDTO() {
    }

    public WeatherPreAccuracyDTO(String cityId, String cityName, String date, WeatherAvgTemAccuracyDTO avgTempeture, WeatherHighTemAccuracyDTO highTempeture, WeatherLowTemAccuracyDTO lowTempeture) {
        this.cityId = cityId;
        this.cityName = cityName;
        this.date = date;
        this.avgTempeture = avgTempeture;
        this.highTempeture = highTempeture;
        this.lowTempeture = lowTempeture;
    }

    public WeatherAvgTemAccuracyDTO getAvgTempeture() {
        return avgTempeture;
    }

    public void setAvgTempeture(WeatherAvgTemAccuracyDTO avgTempeture) {
        this.avgTempeture = avgTempeture;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }



    public WeatherHighTemAccuracyDTO getHighTempeture() {
        return highTempeture;
    }

    public void setHighTempeture(WeatherHighTemAccuracyDTO highTempeture) {
        this.highTempeture = highTempeture;
    }

    public WeatherLowTemAccuracyDTO getLowTempeture() {
        return lowTempeture;
    }

    public void setLowTempeture(WeatherLowTemAccuracyDTO lowTempeture) {
        this.lowTempeture = lowTempeture;
    }
}