package com.tsintergy.lf.serviceapi.base.bus.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 *
 * @description: 历史网损负荷表：历史网损 = 福建全口径-厂用电-地市全口径累加
 * <AUTHOR>
 * @date 2024/05/15 14:44
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "network_loss_load_his_basic")
@ApiModel(description = "厂用电历史负荷表")
@EntityUniqueIndex({"dateTime"})
public class NetworkLossLoadHisBasicDO extends Base96DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "日期")
    @Column(name = "DATE_TIME")
    private Date dateTime;


    @ApiModelProperty(value = "创建时间")
    @Column(name = "CREATETIME")
    @CreationTimestamp
    private Timestamp createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "UPDATETIME")
    @UpdateTimestamp
    private Timestamp updateTime;

    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO);
    }

}