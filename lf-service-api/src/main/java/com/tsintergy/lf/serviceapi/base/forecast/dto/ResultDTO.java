/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/20 2:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Result;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityBean;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmValueDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/20
 * @since 1.0.0
 */
@ApiModel
@Data
public class ResultDTO implements Result,Serializable {

    @ApiModelProperty(value = "分析")
    private List<SensitivityBean> analyze;

    @ApiModelProperty(value = "特性")
    private FeatureDTO feature;

    @ApiModelProperty(value = "提示信息")
    private String comment;

    private List<AccuracyAlgorithmValueDataDTO> accuracy;

    private String year;

    public ResultDTO() {}

    public ResultDTO(ResultDTO other) {
        if (other.analyze != null) {
            this.analyze = new ArrayList<>();
            for (SensitivityBean bean : other.analyze) {
                // 假设 SensitivityBean 也有拷贝构造函数
                this.analyze.add(new SensitivityBean(bean));
            }
        }
        if (other.feature != null) {
            this.feature = new FeatureDTO(other.feature);
        }
        this.comment = other.comment;
        this.year = other.year;
    }

    // Getter 和 Setter 方法
    public List<SensitivityBean> getAnalyze() {
        return analyze;
    }

    public void setAnalyze(List<SensitivityBean> analyze) {
        this.analyze = analyze;
    }

    public FeatureDTO getFeature() {
        return feature;
    }

    public void setFeature(FeatureDTO feature) {
        this.feature = feature;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

}