package com.tsintergy.lf.serviceapi.base.analyze.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.analyze.dto.SysTradeLoadDeviationDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.SysTradeLoadMonthDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.SysTradeMonthDeviationDTO;

import java.util.Date;
import java.util.List;

/**
 *
 * @description: 系统全行业负荷分析
 * <AUTHOR>
 * @date 2024/05/11 16:26
 * @version: 1.0
 */
public interface SysTradeLoadAnalysisService extends FacadeService {
    /**
     * what: 系统-全社会负荷分析-日度
     * <AUTHOR>
     * @date 2024/05/11 16:41
     */
    List<SysTradeLoadDeviationDTO> getDayLoadAnalysis(String cityId,Date date,String dataType) throws Exception;
    /**
     * what: 系统-全社会负荷分析-月度
     * <AUTHOR>
     * @date 2024/05/11 16:41
     */
    List<SysTradeLoadMonthDTO> getMonthLoadAnalysis(String ym,String cityId) throws Exception;
    /**
     * what: 系统-全社会负荷分析-月度偏差
     * <AUTHOR>
     * @date 2024/05/11 16:41
     */
    List<SysTradeMonthDeviationDTO> getMonthLoadDeviation(String ym, Integer dataType) throws Exception;
    /**
     * what: 系统-全社会负荷分析-年度
     * <AUTHOR>
     * @date 2024/05/11 16:41
     */
    List<SysTradeLoadMonthDTO> getYearLoadAnalysis(String year,String cityId) throws Exception;
    /**
     * what: 系统-全社会负荷分析-年度偏差
     * <AUTHOR>
     * @date 2024/05/11 16:41
     */
    List<SysTradeMonthDeviationDTO> getYearLoadDeviation(String year, Integer dataType) throws Exception;
}
