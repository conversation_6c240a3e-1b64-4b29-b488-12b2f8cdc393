package com.tsintergy.lf.serviceapi.base.common.enumeration;

/**
 * 节假日类型
 * <AUTHOR>
 * @date 2018/02/05
 */
public enum HolidayType {

    LEGAL(1,"法定"),

    WEEKEND(2,"周末"),

    CUSTOMIZE(3, "自定义");


    private Integer type;

    private String typeName;

    private HolidayType(Integer type , String typeName){
        this.type = type;
        this.typeName = typeName;
    }

    public Integer value(){
        return this.type;
    }

    public String typeName(){
        return this.typeName;
    }

}



