package com.tsintergy.lf.serviceapi.base.base.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 行业日负荷特性
 */
@MappedSuperclass
@Data
public class BaseLoadFeatureTradeDayDO extends BaseVO {


    @Column(name = "date")
    @ApiModelProperty("日期")
    protected Date date;

    @Column(name = "city_id")
    @ApiModelProperty("城市")
    protected String cityId;


    @Column(name = "trade_id")
    @ApiModelProperty("行业编码")
    protected String tradeId;


    @Column(name = "max_load")
    @ApiModelProperty("最大负荷")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal maxLoad;

    @Column(name = "min_load")
    @ApiModelProperty("最小负荷")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal minLoad;

    @Column(name = "ave_load")
    @ApiModelProperty("平均负荷")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal aveLoad;


    @Column(name = "max_time")
    @ApiModelProperty("最大负荷发生时刻")
    protected String maxTime;

    @Column(name = "min_time")
    @ApiModelProperty("最小负荷发生时刻")
    protected String minTime;

    @Column(name = "peak")
    @ApiModelProperty("尖峰平均负荷")
    protected BigDecimal peak;

    @Column(name = "trough")
    @ApiModelProperty("低谷平均负荷")
    protected BigDecimal trough;

    @Column(name = "different")
    @ApiModelProperty("峰谷差")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal different;

    @Column(name = "gradient")
    @ApiModelProperty("峰谷差率")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal gradient;

    @Column(name = "load_gradient")
    @ApiModelProperty("负荷率")
    protected BigDecimal loadGradient;

    @Column(name = "energy")
    @ApiModelProperty("日电量")
    protected BigDecimal energy;

    @Column(name = "createtime")
    @ApiModelProperty("创建时间")
    @CreationTimestamp
    protected Timestamp createtime;

    @Column(name = "updatetime")
    @ApiModelProperty("更新时间")
    @UpdateTimestamp
    protected Timestamp updatetime;


}
