package com.tsintergy.lf.serviceapi.base.load.pojo; /**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/3/214:04
 * History:
 * <author> <time> <version> <desc>
 */


import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/3/2
 *@since 1.0.0
 */
@Data
@Entity
@Table(name = "load_rolling_feature")
public class RollingLoadFeatureDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private  Date date;

    /**
     * 中调负荷有功减抽最大值
     */
    @Column(name = "zhongDiaoMax")
    private BigDecimal zhongDiaoMax;

    /**
     *中调有功负荷减抽最小值
     */
    @Column(name = "zhongDiaoMin")
    private BigDecimal zhongDiaoMin;

    /**
     *统调负荷有功减抽最大值
     */
    @Column(name = "tongDiaoMax")
    private BigDecimal tongDiaoMax;

    /**
     *统调负荷有功减抽最小值
     */
    @Column(name = "tongDiaoMin")
    private BigDecimal tongDiaoMin;

    /**
     *合计负荷小水电日20点负荷
     */
    @Column(name = "water20PointLoad")
    private BigDecimal water20PointLoad;

    /**
     *合计负荷小风电日20点负荷
     */
    @Column(name = "wind20PointLoad")
    private BigDecimal wind20PointLoad;

    /**
     *合计负荷小火电日20点负荷
     */
    @Column(name = "fire20PointLoad")
    private BigDecimal fire20PointLoad;

    /**
     *全省出力日上网电量
     */
    @Column(name = "provinceLoadEnergy")
    private BigDecimal provinceLoadEnergy;

    /**
     *西鲤三电出力有功出力最大值
     */
    @Column(name = "xiLiSanLoadMax")
    private BigDecimal xiLiSanLoadMax;

    /**
     *西鲤三电出力有功出力最小值
     */
    @Column(name = "xiLiSanLoadMin")
    private BigDecimal xiLiSanLoadMin;

    @Column(name = "createtime")
    @CreationTimestamp
    private Date createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Date updatetime;


}