package com.tsintergy.lf.serviceapi.base.industry.pojo;


import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.expression.spel.ast.Literal;

import javax.persistence.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
@Entity
@Table(name = "industry_city_load_day_fc_algo")
@EntityUniqueIndex({"date", "city_id", "tradeCode"})
public class IndustryCityLoadDayFcAlgoDO implements BaseDO , Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private Date date;

    @Column(name = "city_id")
    private String city_id;

    @Column(name = "trade_code")
    private String tradeCode;

    @Column(name = "trade_code_dsc")
    private String tradeCodeDsc;


    @Column(name = "p1")
    private Double p1;

    @Column(name = "p2")
    private Double p2;

    @Column(name = "p3")
    private Double p3;

    @Column(name = "p4")
    private Double p4;

    @Column(name = "p5")
    private Double p5;

    @Column(name = "p6")
    private Double p6;

    @Column(name = "p7")
    private Double p7;

    @Column(name = "p8")
    private Double p8;

    @Column(name = "p9")
    private Double p9;

    @Column(name = "p10")
    private Double p10;

    @Column(name = "p11")
    private Double p11;

    @Column(name = "p12")
    private Double p12;

    @Column(name = "p13")
    private Double p13;

    @Column(name = "p14")
    private Double p14;

    @Column(name = "p15")
    private Double p15;

    @Column(name = "p16")
    private Double p16;

    @Column(name = "p17")
    private Double p17;

    @Column(name = "p18")
    private Double p18;

    @Column(name = "p19")
    private Double p19;

    @Column(name = "p20")
    private Double p20;

    @Column(name = "p21")
    private Double p21;

    @Column(name = "p22")
    private Double p22;

    @Column(name = "p23")
    private Double p23;

    @Column(name = "p24")
    private Double p24;

    @Column(name = "p25")
    private Double p25;

    @Column(name = "p26")
    private Double p26;

    @Column(name = "p27")
    private Double p27;

    @Column(name = "p28")
    private Double p28;

    @Column(name = "p29")
    private Double p29;

    @Column(name = "p30")
    private Double p30;

    @Column(name = "p31")
    private Double p31;

    @Column(name = "p32")
    private Double p32;

    @Column(name = "p33")
    private Double p33;

    @Column(name = "p34")
    private Double p34;

    @Column(name = "p35")
    private Double p35;

    @Column(name = "p36")
    private Double p36;

    @Column(name = "p37")
    private Double p37;

    @Column(name = "p38")
    private Double p38;

    @Column(name = "p39")
    private Double p39;

    @Column(name = "p40")
    private Double p40;

    @Column(name = "p41")
    private Double p41;

    @Column(name = "p42")
    private Double p42;

    @Column(name = "p43")
    private Double p43;

    @Column(name = "p44")
    private Double p44;

    @Column(name = "p45")
    private Double p45;

    @Column(name = "p46")
    private Double p46;

    @Column(name = "p47")
    private Double p47;

    @Column(name = "p48")
    private Double p48;

    @Column(name = "p49")
    private Double p49;

    @Column(name = "p50")
    private Double p50;

    @Column(name = "p51")
    private Double p51;

    @Column(name = "p52")
    private Double p52;

    @Column(name = "p53")
    private Double p53;

    @Column(name = "p54")
    private Double p54;

    @Column(name = "p55")
    private Double p55;

    @Column(name = "p56")
    private Double p56;

    @Column(name = "p57")
    private Double p57;

    @Column(name = "p58")
    private Double p58;

    @Column(name = "p59")
    private Double p59;

    @Column(name = "p60")
    private Double p60;

    @Column(name = "p61")
    private Double p61;

    @Column(name = "p62")
    private Double p62;

    @Column(name = "p63")
    private Double p63;

    @Column(name = "p64")
    private Double p64;

    @Column(name = "p65")
    private Double p65;

    @Column(name = "p66")
    private Double p66;

    @Column(name = "p67")
    private Double p67;

    @Column(name = "p68")
    private Double p68;

    @Column(name = "p69")
    private Double p69;

    @Column(name = "p70")
    private Double p70;

    @Column(name = "p71")
    private Double p71;

    @Column(name = "p72")
    private Double p72;

    @Column(name = "p73")
    private Double p73;

    @Column(name = "p74")
    private Double p74;

    @Column(name = "p75")
    private Double p75;

    @Column(name = "p76")
    private Double p76;

    @Column(name = "p77")
    private Double p77;

    @Column(name = "p78")
    private Double p78;

    @Column(name = "p79")
    private Double p79;

    @Column(name = "p80")
    private Double p80;

    @Column(name = "p81")
    private Double p81;

    @Column(name = "p82")
    private Double p82;

    @Column(name = "p83")
    private Double p83;

    @Column(name = "p84")
    private Double p84;

    @Column(name = "p85")
    private Double p85;

    @Column(name = "p86")
    private Double p86;

    @Column(name = "p87")
    private Double p87;

    @Column(name = "p88")
    private Double p88;

    @Column(name = "p89")
    private Double p89;

    @Column(name = "p90")
    private Double p90;

    @Column(name = "p91")
    private Double p91;

    @Column(name = "p92")
    private Double p92;

    @Column(name = "p93")
    private Double p93;

    @Column(name = "p94")
    private Double p94;

    @Column(name = "p95")
    private Double p95;

    @Column(name = "p96")
    private Double p96;

    @Override
    public Date getDate() {
        return this.date;
    }

    private List<BigDecimal> gen96List(IndustryCityLoadDayFcAlgoDO industryCityLoadDayHisClctDO) throws Exception {
        List<BigDecimal> list = new ArrayList<>(96);
        for (int i = 1; i < 97; i++) {
            String methodName = "getP" + i;
            Method method = industryCityLoadDayHisClctDO.getClass().getMethod(methodName);
            Double invoke = (Double) method.invoke(industryCityLoadDayHisClctDO);
            if (invoke != null) {
                BigDecimal bigDecimal = BigDecimal.valueOf(invoke);
                list.add(bigDecimal);
            } else {
                list.add(null);
            }
        }
        return list;
    }
    @Override
    public List<BigDecimal> getloadList() {
        try {
            return gen96List(this);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public  String getDeviceId() {
        return this.tradeCode;
    }
}
