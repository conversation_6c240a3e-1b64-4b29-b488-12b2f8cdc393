package com.tsintergy.lf.serviceapi.base.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

/**
 * HolidayDTO
 * 节假日信息
 * <AUTHOR>
 * @date 2018/05/24 16:22
 */
@ApiModel
public class HolidayDTO implements Serializable {

    @ApiModelProperty(value = "年份",example = "2021")
    private String year;

    @ApiModelProperty(value = "节假日信息")
    private List<SimpleHolidayDTO> holidays;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public List<SimpleHolidayDTO> getHolidays() {
        return holidays;
    }

    public void setHolidays(List<SimpleHolidayDTO> holidays) {
        this.holidays = holidays;
    }
}
