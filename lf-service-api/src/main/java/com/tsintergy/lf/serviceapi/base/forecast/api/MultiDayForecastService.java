package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.datamanage.dto.MultiDayQueryDTO;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.MultiDayQueryWeatherDTO;

import java.util.Date;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 14:06
 **/
public interface MultiDayForecastService {


    MultiDayQueryDTO findMultiDayLoad(String cityId, String caliberId, Date forecastDate, Integer startDay, Integer endDay, String algorithmId, String batchId) throws Exception;

    MultiDayQueryWeatherDTO findMultiDayWeather(String cityId, Date forecastDate, Integer startDay, Integer endDay, String weatherSource, String batchId) throws Exception;
}
