package com.tsintergy.lf.serviceapi.base.load.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 日负荷特性
 */
@Data
@Entity
@Table(name = "load_feature_city_day_his_service")
public class LoadFeatureCityDayHisDO extends BaseLoadFeatureCityDayDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;
}
