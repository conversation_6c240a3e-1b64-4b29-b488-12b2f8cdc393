/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 9:33 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.api;

import com.tsintergy.lf.serviceapi.base.airconditioner.dto.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description: 空调负荷基础负荷管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
public interface AirConditionerLoadBaseManageService {

    /**
     * 查询负荷方案管理信息
     *
     * @param loadSolutionManageDTO 前端参数
     * @return {@link List<LoadSolutionManageRespDTO>}
     */
    List<LoadSolutionManageRespDTO> queryLoadSolutionManage(LoadSolutionManageDTO loadSolutionManageDTO);

    /**
     * 保存负荷方案管理信息
     *
     * @param loadSolutionManage 负荷方案管理信息
     */
    void saveOrUpdateLoadSolutionManage(SaveLoadSolutionManageDTO loadSolutionManage);

    void saveOrUpdateAcLoadSolutionManage(SaveLoadSolutionCongfigManageDTO loadSolutionManageDTO);

    void saveAcLoadSolutionManage(AcLoadCityConfigDataDTO acLoadCityConfigDataDTO, Integer weatherType);
    /**
     * 删除负荷方案管理信息
     *
     * @param solutionId 方案id
     */
    void deleteLoadSolutionManage(String solutionId);

    /**
     * 查询基础负荷曲线
     *
     * @param solutionId 方案id
     * @return {@link BaseLoadCurveRespDTO}
     */
    BaseLoadCurveRespDTO queryBaseLoadCurve(String solutionId) throws Exception;

    BaseLoadCurveRespDTO queryBaseNewLoadCurve(String solutionId) throws Exception;

    List<BigDecimal> queryBaseLoadCurveData(String solutionId, Date date) throws Exception;

    /**
     * 查询样本情况统计
     *
     * @param solutionId 方案id
     * @return {@link SampleStatisticsRespDTO}
     */
    SampleStatisticsRespDTO querySampleStatistics(String solutionId) throws Exception;

    SampleStatisticsRespDTO querySampleNewStatistics(String solutionId) throws Exception;

    List<SampleDateDataDTO> queryWeatherSampleStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception;

    List<SampleDateDataDTO> queryWeatherSampleNewStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception;

    List<SampleDateDataDTO> queryWeatherSampleReturnStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception;

    List<BigDecimal> queryFcHisData(Date date, String cityName, String caliberId, Integer weatherType);
}
