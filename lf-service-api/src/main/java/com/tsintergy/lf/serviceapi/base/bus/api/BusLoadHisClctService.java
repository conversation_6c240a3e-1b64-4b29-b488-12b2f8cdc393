/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司 Author:yangjin Date:2021/4/211:11 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.bus.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bus.pojo.BusLoadHisClctDO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2021/4/2
 *@since 1.0.0
 */
public interface BusLoadHisClctService extends FacadeService {


    List<BusLoadHisClctDO> findByDateCity(Date date, String cityId);

    List<BusLoadHisClctDO> findByDateCitys(Date date, List<String> cityIds);

    List<BigDecimal> loadBusHisAccumulation(Date date, List<String> cityIds) throws Exception;
}