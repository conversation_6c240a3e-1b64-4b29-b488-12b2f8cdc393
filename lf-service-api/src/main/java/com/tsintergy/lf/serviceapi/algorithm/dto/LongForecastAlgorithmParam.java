/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.longforecast.LongForecastParam;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: 中长期算法预测参数 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/13 14:44
 * @Version: 1.0.0
 */
public class LongForecastAlgorithmParam extends LongForecastParam {


    private String cityId;

    private String caliberId;

    public LongForecastAlgorithmParam(String cityName, Date beginDate, Date endDate,
        String[] distinguishParams, Integer outProb, BigDecimal confidence) {
        super(cityName, beginDate, endDate, distinguishParams, outProb, confidence);
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }
}