/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司 Author:   lixiaopeng Date:    2018/11/2113:35 History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceapi.base.datamanage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 气象预测准确率 展示对象
 *
 * <AUTHOR>
 */
@ApiModel
public class PrecisionData implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private String date;

    @ApiModelProperty(value = "天气数据集",example = "[1,2,3]")
    private List<BigDecimal> weatherList;

    @ApiModelProperty(value = "平均值",example = "0.33")
    private BigDecimal avg;

    @ApiModelProperty(value = "城市名称",example = "抚顺")
    private String cityName;

    @ApiModelProperty(value = "天气类型名称",example = "1")
    private String weatherTypeName;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public List<BigDecimal> getWeatherList() {
        return weatherList;
    }

    public void setWeatherList(List<BigDecimal> weatherList) {
        this.weatherList = weatherList;
    }

    public BigDecimal getAvg() {
        return avg;
    }

    public void setAvg(BigDecimal avg) {
        this.avg = avg;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getWeatherTypeName() {
        return weatherTypeName;
    }

    public void setWeatherTypeName(String weatherTypeName) {
        this.weatherTypeName = weatherTypeName;
    }
}

