/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wa<PERSON><PERSON>
 * Date:  2019/6/25 7:51
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.typhoon.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/6/25
 * @since 1.0.0
 */
public class TyphoonAnalyzeDTO implements Serializable {

    private Date loginDate;

    private List<TyphoonLoginDTO> loginData;

    private Date afterDate;

    private List<TyphoonLoginDTO> afterData;

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public List<TyphoonLoginDTO> getLoginData() {
        return loginData;
    }

    public void setLoginData(List<TyphoonLoginDTO> loginData) {
        this.loginData = loginData;
    }

    public Date getAfterDate() {
        return afterDate;
    }

    public void setAfterDate(Date afterDate) {
        this.afterDate = afterDate;
    }

    public List<TyphoonLoginDTO> getAfterData() {
        return afterData;
    }

    public void setAfterData(List<TyphoonLoginDTO> afterData) {
        this.afterData = afterData;
    }
}