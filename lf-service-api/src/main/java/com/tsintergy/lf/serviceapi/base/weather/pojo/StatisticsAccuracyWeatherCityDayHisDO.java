/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  gss Date:  2019/8/27 16:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;

//import com.tsieframework.core.base.vo.BasePeriod96VO;

import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/27 
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "statistics_accuracy_weather_city_day_his")
public class StatisticsAccuracyWeatherCityDayHisDO extends Base96DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private Date date;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "avg")
    private BigDecimal avg;

    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

}
