/**
 * Copyright(C),2015‐2024,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.index.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 行业负荷监控 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2024/5/12 13:13
 * @Version: 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoadMonitorTradeDTO {

    private SysPowerMonitorDTO sys;

    private SysPowerMonitorDTO society;

    private List<TradePowerMonitorTDO> trade;

    private String date;

}