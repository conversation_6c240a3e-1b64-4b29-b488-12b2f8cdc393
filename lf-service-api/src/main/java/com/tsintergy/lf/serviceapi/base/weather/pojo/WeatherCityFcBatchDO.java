package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 气象预测数据
 */
@Data
@Entity
@Table(name = "weather_city_fc_basic_batch")
@EntityUniqueIndex({"cityId", "date", "type", "predLoadDate","batchId","source"})
public class WeatherCityFcBatchDO extends BaseWeatherDO implements Weather {


    @Column(name = "pred_load_date")
    private Date predLoadDate;

    @Column(name = "source")
    private String source;

    @Column(name = "batch_id")
    private Integer batchId;

    public WeatherCityFcBatchDO() {
        super();
    }

    public WeatherCityFcBatchDO(String cityId, Integer type, Date date) {
        super(cityId, type, date);
    }

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
