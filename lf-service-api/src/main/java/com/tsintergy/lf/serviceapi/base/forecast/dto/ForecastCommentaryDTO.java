package com.tsintergy.lf.serviceapi.base.forecast.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预测评价
 * User:taojingui
 * Date:18-2-10
 * Time:下午2:33
 */
@ApiModel
public class ForecastCommentaryDTO implements Serializable {

    /**
     * 预测评价(1:非常准确; 2:准确; 3:精度较低)
     */
    @ApiModelProperty(value = "预测评价(1:非常准确; 2:准确; 3:精度较低)",example = "1")
    private Integer commentType;

    /**
     * 曲线评价(1:吻合实际;2:一定畸变;3:较大畸变)
     */
    @ApiModelProperty(value = "曲线评价(1:吻合实际;2:一定畸变;3:较大畸变)",example = "2")
    private Integer curveType;

    /**
     * 误差来源(1:高温;2:台风;3:暴雨)
     */
    @ApiModelProperty(value = "误差来源(1:高温;2:台风;3:暴雨)",example = "3")
    private Integer deviation;

    /**
     * 最大负荷提前时间（负:滞后:正:提前）
     */
    @ApiModelProperty(value = "最大负荷提前时间（负:滞后:正:提前）",example = "5")
    private Integer maxEarlyTime;

    /**
     * 最大负荷偏高值(负:偏低;正:偏高)
     */
    @ApiModelProperty(value = "最大负荷偏高值(负:偏低;正:偏高)",example = "212")
    private BigDecimal maxHigh;

    /**
     * 最小负荷提前时间（负:滞后:正:提前）
     */
    @ApiModelProperty(value = "最小负荷提前时间（负:滞后:正:提前）",example = "25")
    private Integer minEarlyTime;

    /**
     * 最小负荷偏高值(负:偏低;正:偏高)
     */
    @ApiModelProperty(value = "最小负荷偏高值",example = "21")
    private BigDecimal minHigh;

    public Integer getCommentType() {
        return commentType;
    }

    public void setCommentType(Integer commentType) {
        this.commentType = commentType;
    }

    public Integer getCurveType() {
        return curveType;
    }

    public void setCurveType(Integer curveType) {
        this.curveType = curveType;
    }

    public Integer getDeviation() {
        return deviation;
    }

    public void setDeviation(Integer deviation) {
        this.deviation = deviation;
    }

    public Integer getMaxEarlyTime() {
        return maxEarlyTime;
    }

    public void setMaxEarlyTime(Integer maxEarlyTime) {
        this.maxEarlyTime = maxEarlyTime;
    }

    public BigDecimal getMaxHigh() {
        return maxHigh;
    }

    public void setMaxHigh(BigDecimal maxHigh) {
        this.maxHigh = maxHigh;
    }

    public Integer getMinEarlyTime() {
        return minEarlyTime;
    }

    public void setMinEarlyTime(Integer minEarlyTime) {
        this.minEarlyTime = minEarlyTime;
    }

    public BigDecimal getMinHigh() {
        return minHigh;
    }

    public void setMinHigh(BigDecimal minHigh) {
        this.minHigh = minHigh;
    }
}
