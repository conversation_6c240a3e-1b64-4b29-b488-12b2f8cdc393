/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/7/30 17:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsieframework.util.compatible.StringUtils;
import org.joda.time.DateTime;

import java.io.Serializable;

/**
 * Description: 算法参数 <br>
 *
 * <AUTHOR>
 * @create 2018/7/30
 * @since 1.0.0
 */
public class Param implements Serializable {

    /**
     * 以时间(yyyyMMddHHmmssSSS)为标识目录比真正的UUID容易辨识和分析.
     */
    protected String uid;
    /**
     * 1 算法内部滚动预测 预测多天，只调用一次算法  2 程序循环一天一天调用 默认为 1
     */
    protected Integer forecastType;
    /**
     * 城市ID
     */
    protected String cityId;
    /**
     * 口径ID
     */
    protected String caliberId;
    /**
     * 调用用户id
     */
    protected String userId;
    /**
     * 算法枚举
     */
    AlgorithmEnum algorithmEnum;
    /**
     * 算法in out 等配置信息
     */
    AlgorithmDetail algorithmDetail;
    /**
     * 是否为回溯预测
     */
    private String isRecall;

    public Param(AlgorithmEnum algorithmEnum) {
        this.algorithmEnum = algorithmEnum;
    }

    public Param() {
    }

    public String getUid() {
        if (StringUtils.isNotBlank(uid)) {
            return uid;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(DateTime.now().toString("yyyyMMddHHmmssSSS"));
        uid = sb.toString();
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public AlgorithmDetail getAlgorithmDetail() {
        return algorithmDetail;
    }

    public void setAlgorithmDetail(AlgorithmDetail algorithmDetail) {
        this.algorithmDetail = algorithmDetail;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public AlgorithmEnum getAlgorithmEnum() {
        return algorithmEnum;
    }

    public void setAlgorithmEnum(AlgorithmEnum algorithmEnum) {
        this.algorithmEnum = algorithmEnum;
    }

    public Integer getForecastType() {
        return forecastType;
    }

    public void setForecastType(Integer forecastType) {
        this.forecastType = forecastType;
    }

    public String isRecall() {
        return isRecall;
    }

    public String getRecall() {
        return isRecall;
    }

    public void setRecall(String recall) {
        isRecall = recall;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}