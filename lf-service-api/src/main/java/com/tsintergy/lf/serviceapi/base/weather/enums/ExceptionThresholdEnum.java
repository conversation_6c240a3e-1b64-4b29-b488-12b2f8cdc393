package com.tsintergy.lf.serviceapi.base.weather.enums;


public enum ExceptionThresholdEnum {
    //湿度
    HUMIDITY(1, "湿度", "humidity"),

    TEMPERATURE(2, "温度", "temperature"),

    RAINFALL(3, "降雨量", "rainfall"),

    WINDSPEED(4, "风速", "windspeed"),

    LOAD(20, "负荷", "load");


    private Integer type;

    private String typeName;

    /**
     *  映射到setting_system_init的名字
     */
    private String systemSettingName;

    ExceptionThresholdEnum(Integer type, String typeName, String systemSettingName) {
        this.type = type;
        this.typeName = typeName;
        this.systemSettingName = systemSettingName;
    }

    public static String getValueByName(Integer type) {
        for (ExceptionThresholdEnum w : ExceptionThresholdEnum.values()) {
            if (w.getType().equals(type)) {
                return w.getTypeName();
            }
        }
        return null;
    }

    public static ExceptionThresholdEnum getEnumByType(Integer type) {
        for (ExceptionThresholdEnum w : ExceptionThresholdEnum.values()) {
            if (w.getType().equals(type)) {
                return w;
            }
        }
        return null;
    }


    public static Integer getTypeBySettingName(String systemSettingName) {
        for (ExceptionThresholdEnum w : ExceptionThresholdEnum.values()) {
            if (w.getSystemSettingName().equals(systemSettingName)) {
                return w.getType();
            }
        }
        return null;
    }


    public Integer value() {
        return this.type;
    }

    public String typeName() {
        return this.typeName;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getSystemSettingName() {
        return systemSettingName;
    }

    public void setSystemSettingName(String systemSettingName) {
        this.systemSettingName = systemSettingName;
    }
}
