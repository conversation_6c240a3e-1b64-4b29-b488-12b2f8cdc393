/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wa<PERSON><PERSON>
 * Date: 2018/8/27 20:30
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import java.util.Date;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2018/8/27
 * @since 1.0.0
 */
public class PredicForecastParam extends Param {
    /**
     * 训练起始日
     */
    private Date trainBeginDay;

    /**
     * 训练结束日
     */
    private Date trainEndDay;

    /**
     * 基准日
     */
    private Date baseDay;

    /**
     * 预测起始日
     */
    private Date forecastBeginDay;

    /**
     * 预测结束日
     */
    private Date forecastEndDay;

    /**
     * 算法参数
     */
    private AlgorithmParam algorithmParam;

    public Date getTrainBeginDay() {
        return trainBeginDay;
    }

    public void setTrainBeginDay(Date trainBeginDay) {
        this.trainBeginDay = trainBeginDay;
    }

    public Date getTrainEndDay() {
        return trainEndDay;
    }

    public void setTrainEndDay(Date trainEndDay) {
        this.trainEndDay = trainEndDay;
    }

    public Date getBaseDay() {
        return baseDay;
    }

    public void setBaseDay(Date baseDay) {
        this.baseDay = baseDay;
    }

    public Date getForecastBeginDay() {
        return forecastBeginDay;
    }

    public void setForecastBeginDay(Date forecastBeginDay) {
        this.forecastBeginDay = forecastBeginDay;
    }

    public Date getForecastEndDay() {
        return forecastEndDay;
    }

    public void setForecastEndDay(Date forecastEndDay) {
        this.forecastEndDay = forecastEndDay;
    }

    public AlgorithmParam getAlgorithmParam() {
        return algorithmParam;
    }

    public void setAlgorithmParam(AlgorithmParam algorithmParam) {
        this.algorithmParam = algorithmParam;
    }
}