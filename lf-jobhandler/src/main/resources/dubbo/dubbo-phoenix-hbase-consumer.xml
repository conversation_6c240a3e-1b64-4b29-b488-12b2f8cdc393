<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

  <!--生成远程服务代理，-->
  <!--<dubbo:reference registry="provider2" id="hbaseRead" check="false" interface="com.epri.sgcc.dcloud.dubbo.lineData.HbaseRead"></dubbo:reference>-->
</beans>
