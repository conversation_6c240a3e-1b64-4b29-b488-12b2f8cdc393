/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.collect.serviceimpl.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tsintergy.lf.jobhandler.collect.api.SourceWeatherStationHisService;
import com.tsintergy.lf.jobhandler.collect.pojo.SourceWeatherStationHisDO;
import com.tsintergy.lf.jobhandler.collect.serviceimpl.dao.SourceWeatherStationHisDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/18 19:53
 * @Version: 1.0.0
 */

@Slf4j
@Service("sourceWeatherStationHisService")
public class SourceWeatherStationHisServiceImpl extends
    ServiceImpl<SourceWeatherStationHisDAO, SourceWeatherStationHisDO> implements SourceWeatherStationHisService {

}