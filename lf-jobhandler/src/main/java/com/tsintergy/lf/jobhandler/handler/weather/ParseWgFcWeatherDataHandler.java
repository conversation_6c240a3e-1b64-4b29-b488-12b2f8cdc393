package com.tsintergy.lf.jobhandler.handler.weather;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.tool.core.utils.file.weather.Diamond11GridWeatherResolver;
import com.tsintergy.aif.tool.core.utils.file.weather.Diamond4GridWeatherResolver;
import com.tsintergy.aif.tool.core.utils.file.weather.GridWeatherResolver;
import com.tsintergy.aif.tool.core.utils.file.weather.pojo.GridWeather;
import com.tsintergy.lf.jobhandler.collect.api.WgFcRainWeather2D5kmService;
import com.tsintergy.lf.jobhandler.collect.api.WgFcSdWeather2D5kmService;
import com.tsintergy.lf.jobhandler.collect.api.WgFcWdWeather2D5kmService;
import com.tsintergy.lf.jobhandler.collect.api.WgFcWindWeather2D5kmService;
import com.tsintergy.lf.jobhandler.collect.pojo.WgFcRainWeather2D5kmDO;
import com.tsintergy.lf.jobhandler.collect.pojo.WgFcSdWeather2D5kmDO;
import com.tsintergy.lf.jobhandler.collect.pojo.WgFcWdWeather2D5kmDO;
import com.tsintergy.lf.jobhandler.collect.pojo.WgFcWindWeather2D5kmDO;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcClctWgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Component
@JobHandler("parseWgFcWeatherDataHandler")
@Slf4j
public class ParseWgFcWeatherDataHandler extends AbstractBaseHandler {

    @Value("${file.wgWeather.path}")
    public String path;

    @Autowired
    private WgFcSdWeather2D5kmService wgFcSdWeather2D5kmService;

    @Autowired
    private WgFcWdWeather2D5kmService wgFcWdWeather2D5kmService;

    @Autowired
    private WgFcRainWeather2D5kmService wgFcRainWeather2D5kmService;

    @Autowired
    private WgFcWindWeather2D5kmService wgFcWindWeather2D5kmService;

    @Autowired
    private WeatherStationFcClctWgService weatherStationFcClctWgService;

    @Override
    public ReturnT<String> execute(String dateStr) throws Exception {
        String startDateStr;
        String endDateStr;
        if (StringUtils.isEmpty(dateStr)) {
            //预测数据每天8点和20点各一批,如果当前时间晚于20:00，则使用 "20" 作为小时，否则使用 "08" 作为小时
            LocalDateTime now = LocalDateTime.now();
            String formatStr = now.format(DateTimeFormatter.ofPattern("yyMMdd"));
            startDateStr = now.getHour() >= 20 ? formatStr + "20" : formatStr + "08";
            endDateStr = startDateStr;
        } else {
            String[] params = dateStr.split(",");
            Date start = DateUtils.string2Date(params[0], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            Date end = DateUtils.string2Date(params[1], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            SimpleDateFormat startFormat = new SimpleDateFormat("yyMMdd00");
            SimpleDateFormat endFormat = new SimpleDateFormat("yyMMdd24");
            startDateStr = startFormat.format(start);
            endDateStr = endFormat.format(end);
        }
        //表中filename数据格式类似24081308.042
        startDateStr = startDateStr + ".000";
        endDateStr = endDateStr + ".999";

        this.parse2D5KmData(startDateStr, endDateStr);
        return ReturnT.SUCCESS;
    }

    private void parse2D5KmData(String startDateStr, String endDateStr) {
        this.parse2D5KmHumidity(startDateStr, endDateStr);
        this.parse2D5KmTemperature(startDateStr, endDateStr);
        this.parse2D5KmRain(startDateStr, endDateStr);
        this.parse2D5KmWind(startDateStr, endDateStr);
    }

    private void parse2D5KmHumidity(String startDateStr, String endDateStr) {
        //查询2D5Km格点湿度数据
        LambdaQueryWrapper<WgFcSdWeather2D5kmDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.ge(WgFcSdWeather2D5kmDO::getFileName, startDateStr);
        queryWrapper.le(WgFcSdWeather2D5kmDO::getFileName, endDateStr);
        queryWrapper.orderByAsc(WgFcSdWeather2D5kmDO::getFileName);
        List<WgFcSdWeather2D5kmDO> wgFcSdWeather2D5KmDOS = wgFcSdWeather2D5kmService.list(queryWrapper);
        if (CollectionUtils.isEmpty(wgFcSdWeather2D5KmDOS)) {
            XxlJobLogger.log("2D5Km的湿度数据为空,参数为:" + startDateStr + "," + endDateStr);
        }
        for (WgFcSdWeather2D5kmDO wgFcSdWeather2D5kmDO : wgFcSdWeather2D5KmDOS) {
            //将数据写入磁盘文件中
            String fileName = wgFcSdWeather2D5kmDO.getFileName();
            String outputFile = path + "sd/" + fileName + "_2D5Km_FC";
            String fileContent = new String(wgFcSdWeather2D5kmDO.getFileblob(), StandardCharsets.UTF_8);
            try (OutputStreamWriter bw = new OutputStreamWriter(
                    Files.newOutputStream(Paths.get(outputFile)), StandardCharsets.UTF_8)) {
                bw.write(fileContent);
                XxlJobLogger.log("2D5Km湿度数据写入文件成功: " + outputFile);
            } catch (IOException e) {
                XxlJobLogger.log("2D5Km湿度数据写入文件异常", e);
            }

            //解析文件气象数据存入格点气象表中
            GridWeatherResolver gridWeatherResolver = new Diamond4GridWeatherResolver();
            File file = new File(outputFile);
            try {
                GridWeather gridWeather = gridWeatherResolver.resolveFile(file);
                this.calcForecastDateFromFileName(gridWeather, fileName);
                weatherStationFcClctWgService.doParseGridFcWeatherData(gridWeather, WeatherEnum.HUMIDITY.getType());
            } catch (Exception e) {
                XxlJobLogger.log("解析2D5Km湿度数据异常，日期为:" + fileName, e);
            }
            XxlJobLogger.log("解析2D5Km湿度数据成功，日期为:" + fileName);
        }
    }

    private void parse2D5KmTemperature(String startDateStr, String endDateStr) {
        //查询2D5Km格点温度数据
        LambdaQueryWrapper<WgFcWdWeather2D5kmDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.ge(WgFcWdWeather2D5kmDO::getFileName, startDateStr);
        queryWrapper.le(WgFcWdWeather2D5kmDO::getFileName, endDateStr);
        queryWrapper.orderByAsc(WgFcWdWeather2D5kmDO::getFileName);
        List<WgFcWdWeather2D5kmDO> wgFcWdWeather2D5KmDOS = wgFcWdWeather2D5kmService.list(queryWrapper);
        if (CollectionUtils.isEmpty(wgFcWdWeather2D5KmDOS)) {
            XxlJobLogger.log("2D5Km的温度数据为空,参数为:" + startDateStr + "," + endDateStr);
        }
        for (WgFcWdWeather2D5kmDO wgFcWdWeather2D5kmDO : wgFcWdWeather2D5KmDOS) {
            //将数据写入磁盘文件中
            String fileName = wgFcWdWeather2D5kmDO.getFileName();
            String outputFile = path + "wd/" + fileName + "_2D5Km_FC";
            String fileContent = new String(wgFcWdWeather2D5kmDO.getFileblob(), StandardCharsets.UTF_8);
            try (OutputStreamWriter bw = new OutputStreamWriter(
                    Files.newOutputStream(Paths.get(outputFile)), StandardCharsets.UTF_8)) {
                bw.write(fileContent);
                XxlJobLogger.log("2D5Km温度数据写入文件成功: " + outputFile);
            } catch (IOException e) {
                XxlJobLogger.log("2D5Km温度数据写入文件异常", e);
            }

            //解析文件气象数据存入格点气象表中
            GridWeatherResolver gridWeatherResolver = new Diamond4GridWeatherResolver();
            File file = new File(outputFile);
            try {
                GridWeather gridWeather = gridWeatherResolver.resolveFile(file);
                this.calcForecastDateFromFileName(gridWeather, fileName);
                weatherStationFcClctWgService.doParseGridFcWeatherData(gridWeather, WeatherEnum.TEMPERATURE.getType());
            } catch (Exception e) {
                XxlJobLogger.log("解析2D5Km温度数据异常，日期为:" + fileName, e);
            }
            XxlJobLogger.log("解析2D5Km温度数据成功，日期为:" + fileName);
        }
    }

    private void parse2D5KmRain(String startDateStr, String endDateStr) {
        //查询2D5Km格点降雨数据
        LambdaQueryWrapper<WgFcRainWeather2D5kmDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.ge(WgFcRainWeather2D5kmDO::getFileName, startDateStr);
        queryWrapper.le(WgFcRainWeather2D5kmDO::getFileName, endDateStr);
        queryWrapper.orderByAsc(WgFcRainWeather2D5kmDO::getFileName);
        List<WgFcRainWeather2D5kmDO> wgFcRainWeather2D5kmDOS = wgFcRainWeather2D5kmService.list(queryWrapper);
        if (CollectionUtils.isEmpty(wgFcRainWeather2D5kmDOS)) {
            XxlJobLogger.log("2D5Km的降雨数据为空,参数为:" + startDateStr + "," + endDateStr);
        }
        for (WgFcRainWeather2D5kmDO wgFcRainWeather2D5kmDO : wgFcRainWeather2D5kmDOS) {
            //将数据写入磁盘文件中
            String fileName = wgFcRainWeather2D5kmDO.getFileName();
            String outputFile = path + "rain/" + fileName + "_2D5Km_FC";
            String fileContent = new String(wgFcRainWeather2D5kmDO.getFileblob(), StandardCharsets.UTF_8);
            try (OutputStreamWriter bw = new OutputStreamWriter(
                    Files.newOutputStream(Paths.get(outputFile)), StandardCharsets.UTF_8)) {
                bw.write(fileContent);
                XxlJobLogger.log("2D5Km降雨数据写入文件成功: " + outputFile);
            } catch (IOException e) {
                XxlJobLogger.log("2D5Km降雨数据写入文件异常", e);
            }

            //解析文件存入格点气象表中
            GridWeatherResolver gridWeatherResolver = new Diamond4GridWeatherResolver();
            File file = new File(outputFile);
            try {
                GridWeather gridWeather = gridWeatherResolver.resolveFile(file);
                this.calcForecastDateFromFileName(gridWeather, fileName);
                weatherStationFcClctWgService.doParseGridFcWeatherData(gridWeather, WeatherEnum.RAINFALL.getType());
            } catch (Exception e) {
                XxlJobLogger.log("解析2D5Km降雨数据异常，日期为:" + fileName, e);
            }
            XxlJobLogger.log("解析2D5Km降雨数据成功，日期为:" + fileName);
        }
    }

    private void parse2D5KmWind(String startDateStr, String endDateStr) {
        //查询2D5Km格点风速数据
        LambdaQueryWrapper<WgFcWindWeather2D5kmDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.ge(WgFcWindWeather2D5kmDO::getFileName, startDateStr);
        queryWrapper.le(WgFcWindWeather2D5kmDO::getFileName, endDateStr);
        queryWrapper.orderByAsc(WgFcWindWeather2D5kmDO::getFileName);
        List<WgFcWindWeather2D5kmDO> wgFcWindWeather2D5kmDOS = wgFcWindWeather2D5kmService.list(queryWrapper);
        if (CollectionUtils.isEmpty(wgFcWindWeather2D5kmDOS)) {
            XxlJobLogger.log("2D5Km的风速数据为空,参数为:" + startDateStr + "," + endDateStr);
        }
        for (WgFcWindWeather2D5kmDO wgFcWindWeather2D5kmDO : wgFcWindWeather2D5kmDOS) {
            //将数据写入磁盘文件中
            String fileName = wgFcWindWeather2D5kmDO.getFileName();
            String outputFile = path + "wind/" + fileName + "_2D5Km_FC";
            String fileContent = new String(wgFcWindWeather2D5kmDO.getFileblob(), StandardCharsets.UTF_8);
            try (OutputStreamWriter bw = new OutputStreamWriter(
                    Files.newOutputStream(Paths.get(outputFile)), StandardCharsets.UTF_8)) {
                bw.write(fileContent);
                XxlJobLogger.log("2D5Km风速数据写入文件成功: " + outputFile);
            } catch (IOException e) {
                XxlJobLogger.log("2D5Km风速数据写入文件异常", e);
            }

            //解析文件存入格点气象表中
            GridWeatherResolver gridWeatherResolver = new Diamond11GridWeatherResolver();
            File file = new File(outputFile);
            try {
                GridWeather gridWeather = gridWeatherResolver.resolveFile(file);
                this.calcForecastDateFromFileName(gridWeather, fileName);
                weatherStationFcClctWgService.doParseGridFcWeatherData(gridWeather, WeatherEnum.WINDSPEED.getType());
            } catch (Exception e) {
                XxlJobLogger.log("解析2D5Km风速数据异常，日期为:" + fileName, e);
            }
            XxlJobLogger.log("解析2D5Km风速数据成功，日期为:" + fileName);
        }
    }

    /**
     * 通过文件名计算预测时间，比如文件名为24081108.001,则代表24/08/11上午九点的预测数据
     *
     * @param fileName
     * @return
     */
    private void calcForecastDateFromFileName(GridWeather gridWeather, String fileName) {
        String[] parts = fileName.split("\\.");
        if (parts.length < 2) {
            XxlJobLogger.log("文件名格式不正确", fileName);
            return;
        }
        int hours = Integer.parseInt(parts[1]);
        Date date = DateUtils.addHours(gridWeather.getDate(), hours);
        gridWeather.setDate(date);
    }
}
