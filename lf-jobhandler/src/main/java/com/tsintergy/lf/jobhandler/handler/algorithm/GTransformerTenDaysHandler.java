package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.algorithm.api.ShortAlgorithmForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.GTransformerAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;

@Component
@JobHandler("gtTenDaysFcHandler")
@Slf4j
public class GTransformerTenDaysHandler extends AbstractBaseHandler {

    @Autowired
    private CityService cityService;

    @Autowired
    private ShortAlgorithmForecastService shortAlgorithmForecastService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate;
        Date endDate;
        if (!StringUtils.isEmpty(s)) {
            String[] params = s.split(",");
            startDate = DateUtils.string2Date(params[0], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(params[1], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        } else {
            startDate = DateUtils.addDays(DateUtil.getFormatDate(new Date()), 1);
            Integer days = 3;
            SettingSystemDO reportVOS = settingSystemService.findByFieldId(SystemConstant.FORECAST_DAY);
            if (reportVOS != null) {
                days = Integer.parseInt(reportVOS.getValue());
            }
            endDate = DateUtils.addDays(startDate, days - 1);
        }
        log.info("开始执行GTransformer十日算法预测，开始时间：{}，结束时间：{}", startDate, endDate);
        GTransformerAlgorithmParam gTransformerParam = new GTransformerAlgorithmParam(Constants.PROVINCE_NAME, startDate, endDate, null);
        gTransformerParam.setCityId(Constants.PROVINCE_ID);
        gTransformerParam.setCaliberId(Constants.CALIBER_QUAN);
        gTransformerParam.setForecastNum(DateUtil.getListBetweenDay(startDate, endDate).size() + "");
        gTransformerParam.setAlgorithm(AlgorithmEnum.G_TRANS_TDS_FORMER);
        shortAlgorithmForecastService.gTransformerTdsForecast(gTransformerParam);
        return ReturnT.SUCCESS;
    }

}
