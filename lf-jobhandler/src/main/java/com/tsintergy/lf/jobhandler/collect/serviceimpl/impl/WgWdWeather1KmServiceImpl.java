package com.tsintergy.lf.jobhandler.collect.serviceimpl.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.jobhandler.collect.api.WgWdWeather1kmService;
import com.tsintergy.lf.jobhandler.collect.pojo.WgWdWeather1kmDO;
import com.tsintergy.lf.jobhandler.collect.serviceimpl.dao.WgWdWeather1kmDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("wgWdWeather1kmService")
@DataSource("wgWeather")
public class WgWdWeather1KmServiceImpl extends ServiceImpl<WgWdWeather1kmDAO, WgWdWeather1kmDO> implements WgWdWeather1kmService {


}
