/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/11/14 20:41
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/11/14 
 * @since 1.0.0
 */
@Data
public class D5000LoadClctDO extends BaseVO {

    private static final long serialVersionUID = -3439973701341440250L;

    private Date occurTime;

    private BigDecimal load;

}
