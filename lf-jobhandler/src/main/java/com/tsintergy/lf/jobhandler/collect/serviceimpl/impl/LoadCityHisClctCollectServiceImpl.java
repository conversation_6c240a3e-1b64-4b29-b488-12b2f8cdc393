/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/11/12 15:33 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.serviceimpl.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.epri.sgcc.DCloud.ServiceBase.HisData;
import com.epri.sgcc.DCloud.ServiceBase.HisDataService;
import com.epri.sgcc.DCloud.ServiceBase.TimeInterval;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.collect.api.BaseCityClctRelationService;
import com.tsintergy.lf.jobhandler.collect.api.LoadCityHisClctCollectService;
import com.tsintergy.lf.jobhandler.collect.pojo.BaseCityClctRelationDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisByMinuteService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisBasicByMinuteDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisClctDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/11/12
 * @since 1.0.0
 */
@Slf4j
@Service("loadCityHisClctCollectService")
public class LoadCityHisClctCollectServiceImpl implements LoadCityHisClctCollectService {

    @Value("${dataId}")
    private String dataId;

    @Autowired
    private LoadCityHisClctService loadCityHisClctService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private BaseCityClctRelationService baseCityClctRelationService;

    @Autowired
    private LoadCityHisByMinuteService loadCityHisByMinuteService;

    @DubboReference(group = "hisdataServiceNew", check = false, registry = "load", timeout = 500000)
    HisDataService hisDataService;

    @Override
    public void collectLoadCityHisClct(Date date, boolean realTime, List<String> idList) throws Exception {
        //根据短期城市id获取d5000的负荷名称
        QueryWrapper<BaseCityClctRelationDO> queryWrapper = new QueryWrapper();
        List<BaseCityClctRelationDO> list = baseCityClctRelationService.list(queryWrapper);

        List<BaseCityClctRelationDO> bakRelationDOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(idList)) {
            String dataId = idList.get(0);
            for (BaseCityClctRelationDO baseCityClctRelationDO : list) {
                if (baseCityClctRelationDO.getDataId().equals(dataId)) {
                    bakRelationDOS.add(baseCityClctRelationDO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(bakRelationDOS)) {
            list = bakRelationDOS;
        }

        if (CollectionUtils.isNotEmpty(list)) {
            //实时采集
            if (realTime) {
                Date fifteenFcTime = DateUtil.getFifteenFcTime(null);
                String dateStr = DateUtils.date2String(fifteenFcTime, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                String startDateStr = dateStr + "000000";
                String endDateStr = dateStr + "235900";
                XxlJobLogger.log("当前为实时采集,采集的时刻点为:"
                    + startDateStr + "," + endDateStr);
                collectData(DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR),
                    DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR)
                    , list);
            } else {
                XxlJobLogger.log("当前为输入参数补采,采集的时刻点为:"
                    + DateUtils.date2String(date, DateFormatType.DATE_FORMAT_STR));
                String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                String startDateStr = dateStr + "000000";
                String endDateStr = dateStr + "235900";
                XxlJobLogger.log("当前为输入参数补采,采集的时刻点为:"
                    + startDateStr + "," + endDateStr);

                collectData(DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR),
                    DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR)
                    , list);
            }
        }
    }


    @Override
    public void collectLoadCityHisMinuteClct(Date date, boolean realTime, List<String> idList, int timespan)
        throws Exception {
        //根据短期城市id获取d5000的负荷名称
        QueryWrapper<BaseCityClctRelationDO> queryWrapper = new QueryWrapper();
        List<BaseCityClctRelationDO> list = baseCityClctRelationService.list(queryWrapper);

        List<BaseCityClctRelationDO> bakRelationDOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(idList)) {
            String dataId = idList.get(0);
            for (BaseCityClctRelationDO baseCityClctRelationDO : list) {
                if (baseCityClctRelationDO.getDataId().equals(dataId)) {
                    bakRelationDOS.add(baseCityClctRelationDO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(bakRelationDOS)) {
            list = bakRelationDOS;
        }

        if (CollectionUtils.isNotEmpty(list)) {
//            //实时采集
//            if (realTime) {
//                Date fifteenFcTime = DateUtil.getFifteenFcTime(null);
//                String dateStr = DateUtils.date2String(fifteenFcTime, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
//                String startDateStr = dateStr + "000000";
//                String endDateStr = dateStr + "235900";
//                XxlJobLogger.log("当前为实时采集,采集的时刻点为:"
//                    + startDateStr + "," + endDateStr);
//                collectTimeSpanData(DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR),
//                    DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR)
//                    , list, timespan);
//            } else {
                XxlJobLogger.log("当前为输入参数补采,采集的时刻点为:"
                    + DateUtils.date2String(date, DateFormatType.DATE_FORMAT_STR));
                String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                String startDateStr = dateStr + "000000";
                String endDateStr = dateStr + "235900";
                XxlJobLogger.log("当前为输入参数补采,采集的时刻点为:"
                    + startDateStr + "," + endDateStr);

                collectTimeSpanData(DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR),
                    DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR)
                    , list, timespan);
//            }
        }
    }


    public void collectTimeSpanData(Date startDate, Date endDate, List<BaseCityClctRelationDO> list, int sampleSpan)
        throws Exception {
        List<Long> dataIds = list.stream().map(BaseCityClctRelationDO::getDataId).map(Long::valueOf)
            .collect(Collectors.toList());
        Map<String, List<BaseCityClctRelationDO>> dataIdMap = list.stream()
            .collect(Collectors.groupingBy(BaseCityClctRelationDO::getDataId));
        Map<Long, ArrayList<HisData>> hisData = collectLoadCityHisClct(new ArrayList<>(dataIds), startDate, endDate,
            sampleSpan);


        if (hisData == null || hisData.size() == 0) {
            XxlJobLogger.log("调用调控云接口响应数据为空,当前采集结束");
            return;
        }
        XxlJobLogger.log("调用调控云接口响应数据:" + JSON.toJSONString(hisData));
        for (Map.Entry<Long, ArrayList<HisData>> entry : hisData.entrySet()) {
            Long data_id = entry.getKey();
            ArrayList<HisData> hisDatas = entry.getValue();
            //根据时间排序；
            hisDatas.sort(Comparator.comparing(HisData::getDate));
            List<BaseCityClctRelationDO> baseCityClctRelationDOS = dataIdMap.get(String.valueOf(data_id));
            for (BaseCityClctRelationDO relationDO : baseCityClctRelationDOS) {
                List<BigDecimal> loadList = new ArrayList<>();
                for (HisData data : hisDatas) {
                    XxlJobLogger.log("校验时间顺序："
                        + DateUtils.date2String(data.getDate(), DateFormatType.HOUR_MINUTE_SECOND));
                    loadList.add(new BigDecimal(Float.valueOf(data.getValue())).setScale(4, BigDecimal.ROUND_HALF_UP));
                }
                XxlJobLogger.log("1440点负荷数据为:"
                    + JSON.toJSONString(loadList) + "当前点数:" + loadList.size());
                Date dataDate = startDate;
                //逐1分钟暂不考虑填充2400点；入库1440个数据
                LoadCityHisBasicByMinuteDO loadCityHisClctDO = new LoadCityHisBasicByMinuteDO();
                loadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityHisClctDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadCityHisClctDO.setCityId(relationDO.getCityId());
                loadCityHisClctDO.setCaliberId(relationDO.getCaliberId());
                loadCityHisClctDO.setDate(new java.sql.Date(dataDate.getTime()));
                //对应获取：JSON.parseArray(bbb, BigDecimal.class)
                loadCityHisClctDO.setLoadData(JSON.toJSONString(loadList));
                loadCityHisByMinuteService.doSaveOrUpdate(loadCityHisClctDO);
            }
        }

    }


    public void collectData(Date startDate, Date endDate, List<BaseCityClctRelationDO> list) throws Exception {

        List<Long> dataIds = list.stream().map(BaseCityClctRelationDO::getDataId).map(Long::valueOf)
            .collect(Collectors.toList());
        Map<String, List<BaseCityClctRelationDO>> dataIdMap = list.stream()
            .collect(Collectors.groupingBy(BaseCityClctRelationDO::getDataId));
        Map<Long, ArrayList<HisData>> hisData = collectLoadCityHisClct(new ArrayList<>(dataIds), startDate, endDate);


        if (hisData == null || hisData.size() == 0) {
            XxlJobLogger.log("调用调控云接口响应数据为空,当前采集结束");
            return;
        }
        XxlJobLogger.log("调用调控云接口响应数据:" + JSON.toJSONString(hisData));
        for (Map.Entry<Long, ArrayList<HisData>> entry : hisData.entrySet()) {
            Long data_id = entry.getKey();
            ArrayList<HisData> hisDatas = entry.getValue();

            List<BaseCityClctRelationDO> baseCityClctRelationDOS = dataIdMap.get(String.valueOf(data_id));

            for (BaseCityClctRelationDO relationDO : baseCityClctRelationDOS) {
                Map<String, Map<String, BigDecimal>> loadCityHisDOHashMap = new HashMap<>();
                for (HisData data : hisDatas) {
                    Date dataDate = data.getDate();
                    float value = data.getValue();
                    String yyyyMMdd = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                    String yyyyMMddHHmmss = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
                    if (loadCityHisDOHashMap.get(yyyyMMdd) == null) {
                        Map<String, BigDecimal> map = new HashMap<>();
                        map.put("t" + yyyyMMddHHmmss.substring(8, 12),
                            new BigDecimal(Float.valueOf(value)).setScale(4, BigDecimal.ROUND_HALF_UP));
                        loadCityHisDOHashMap.put(yyyyMMdd, map);
                    } else {
                        Map<String, BigDecimal> map = loadCityHisDOHashMap.get(yyyyMMdd);
                        map.put("t" + yyyyMMddHHmmss.substring(8, 12),
                            new BigDecimal(Float.valueOf(value)).setScale(4, BigDecimal.ROUND_HALF_UP));
                    }
                }


                for (Map.Entry<String, Map<String, BigDecimal>> map : loadCityHisDOHashMap.entrySet()) {
                    String dateStr = map.getKey();
                    Map<String, BigDecimal> value = map.getValue();
                    Date date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

                    Date yesterDay = DateUtils.addDays(date, -1);
                    List<LoadCityHisClctDO> yesterDayLoadCityHisVOS = loadCityHisClctService
                        .findLoadCityHisVOS(relationDO.getCityId(), yesterDay, yesterDay, relationDO.getCaliberId());

                    if (!CollectionUtils.isEmpty(yesterDayLoadCityHisVOS)) {
                        LoadCityHisClctDO yesterdayData = yesterDayLoadCityHisVOS.get(0);
                        yesterdayData.setT2400(value.get("t0000"));
                        loadCityHisClctService.doUpdateLoadCityHisClctVO(yesterdayData);
                    } else {
                        LoadCityHisClctDO yesterDayLoadCityHisClctDO = new LoadCityHisClctDO();
                        yesterDayLoadCityHisClctDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                        yesterDayLoadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        yesterDayLoadCityHisClctDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                        yesterDayLoadCityHisClctDO.setCityId(relationDO.getCityId());
                        yesterDayLoadCityHisClctDO.setCaliberId(relationDO.getCaliberId());
                        yesterDayLoadCityHisClctDO.setDate(new java.sql.Date(yesterDay.getTime()));
                        yesterDayLoadCityHisClctDO.setT2400(value.get("t0000"));
                        loadCityHisClctService.doCreate(yesterDayLoadCityHisClctDO);
                    }


                    List<LoadCityHisClctDO> loadCityHisVOS = loadCityHisClctService
                        .findLoadCityHisVOS(relationDO.getCityId(), date, date, relationDO.getCaliberId());
                    if (CollectionUtils.isEmpty(loadCityHisVOS)) {
                        LoadCityHisClctDO loadCityHisClctDO = new LoadCityHisClctDO();
                        loadCityHisClctDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                        loadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        loadCityHisClctDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                        loadCityHisClctDO.setCityId(relationDO.getCityId());
                        loadCityHisClctDO.setCaliberId(relationDO.getCaliberId());
                        loadCityHisClctDO.setDate(new java.sql.Date(date.getTime()));
                        BeanMap beanMap = BeanMap.create(loadCityHisClctDO);
                        beanMap.putAll(value);
                        loadCityHisClctService.doCreate(loadCityHisClctDO);
                    } else {
                        LoadCityHisClctDO loadCityHisClctDO = loadCityHisVOS.get(0);
                        loadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        BeanMap beanMap = BeanMap.create(loadCityHisClctDO);
                        beanMap.putAll(value);
                        loadCityHisClctService.doUpdateLoadCityHisClctVO(loadCityHisClctDO);
                    }


                    List<LoadCityHisDO> yesterdayLoadCityHisBasicVOS = loadCityHisService.
                        findLoadCityHisDOS(relationDO.getCityId(), yesterDay, yesterDay, relationDO.getCaliberId());
                    if (!CollectionUtils.isEmpty(yesterdayLoadCityHisBasicVOS)) {
                        LoadCityHisDO yesterdayData = yesterdayLoadCityHisBasicVOS.get(0);
                        yesterdayData.setT2400(value.get("t0000"));
                        loadCityHisService.doUpdateLoadCityHisDO(yesterdayData);
                    } else {
                        LoadCityHisDO yesterDayLoadCityHisDO = new LoadCityHisDO();
                        yesterDayLoadCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                        yesterDayLoadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        yesterDayLoadCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                        yesterDayLoadCityHisDO.setCityId(relationDO.getCityId());
                        yesterDayLoadCityHisDO.setCaliberId(relationDO.getCaliberId());
                        yesterDayLoadCityHisDO.setDate(new java.sql.Date(yesterDay.getTime()));
                        yesterDayLoadCityHisDO.setT2400(value.get("t0000"));
                        loadCityHisService.doCreate(yesterDayLoadCityHisDO);
                    }


                    List<LoadCityHisDO> loadCityHisBasicVOS = loadCityHisService.
                        findLoadCityHisDOS(relationDO.getCityId(), date, date, relationDO.getCaliberId());
                    if (CollectionUtils.isEmpty(loadCityHisBasicVOS)) {
                        LoadCityHisDO loadCityHisBasictDO = new LoadCityHisDO();
                        loadCityHisBasictDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                        loadCityHisBasictDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        loadCityHisBasictDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                        loadCityHisBasictDO.setCityId(relationDO.getCityId());
                        loadCityHisBasictDO.setCaliberId(relationDO.getCaliberId());
                        loadCityHisBasictDO.setDate(new java.sql.Date(date.getTime()));
                        BeanMap beanMap = BeanMap.create(loadCityHisBasictDO);
                        beanMap.putAll(value);
                        loadCityHisService.doCreate(loadCityHisBasictDO);
                    } else {
                        LoadCityHisDO loadCityHisBasictDO = loadCityHisBasicVOS.get(0);
                        loadCityHisBasictDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        BeanMap beanMap = BeanMap.create(loadCityHisBasictDO);
                        beanMap.putAll(value);
                        loadCityHisService.doUpdateLoadCityHisDO(loadCityHisBasictDO);
                    }
                }


            }
        }
    }


    public void collectData(Date date, List<BaseCityClctRelationDO> list) throws Exception {
        List<Long> dataIds = list.stream().map(BaseCityClctRelationDO::getDataId).map(Long::valueOf)
            .collect(Collectors.toList());
        Map<String, List<BaseCityClctRelationDO>> dataIdMap = list.stream()
            .collect(Collectors.groupingBy(BaseCityClctRelationDO::getDataId));


        Map<Long, ArrayList<HisData>> hisData = collectLoadCityHisClct(new ArrayList<>(dataIds), date, date);
        if (hisData == null || hisData.size() == 0) {
            XxlJobLogger.log("调用调控云接口响应数据为空,当前采集结束");
            return;
        }
        XxlJobLogger.log("调用调控云接口响应数据:" + JSON.toJSONString(hisData));


        List<LoadCityHisClctDO> loadCityHisVOS = loadCityHisClctService
            .findLoadCityHisVOS(null, date, date, null);

        List<LoadCityHisDO> loadCityHisBasicVOS = loadCityHisService.findLoadCityHisDOS(null, date, date, null);


        Map<String, List<LoadCityHisClctDO>> clctDataMap = null;
        if (CollectionUtils.isNotEmpty(loadCityHisVOS)) {
            clctDataMap = loadCityHisVOS.stream()
                .collect(Collectors.groupingBy(LoadCityHisClctDO::getCityId));
        }


        Map<String, List<LoadCityHisDO>> basicDataMap = null;
        if (CollectionUtils.isNotEmpty(loadCityHisVOS)) {
            basicDataMap = loadCityHisBasicVOS.stream()
                .collect(Collectors.groupingBy(LoadCityHisDO::getCityId));
        }


        for (Map.Entry<Long, ArrayList<HisData>> entry : hisData.entrySet()) {

            Long data_id = entry.getKey();

            ArrayList<HisData> hisDatas = entry.getValue();
            for (HisData data : hisDatas) {
                Date dataDate = data.getDate();
                float value = data.getValue();
                String valueStr = Float.toString(value);

                List<BaseCityClctRelationDO> baseCityClctRelationDOS = dataIdMap.get(String.valueOf(data_id));
                BaseCityClctRelationDO baseCityClctRelationDO = baseCityClctRelationDOS.get(0);

                String cityId = baseCityClctRelationDO.getCityId();
                String caliberId = baseCityClctRelationDO.getCaliberId();


                if (clctDataMap == null) {
                    insertClctData(cityId, caliberId, date, dataDate, Float.toString(value));
                } else {
                    //城市id下的数据
                    List<LoadCityHisClctDO> loadCityHisClctDOS = clctDataMap.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityHisClctDOS)) {
                        Map<String, List<LoadCityHisClctDO>> collect = loadCityHisClctDOS.stream()
                            .collect(Collectors.groupingBy(LoadCityHisClctDO::getCaliberId));
                        List<LoadCityHisClctDO> loadCityHisClctDOS1 = collect.get(caliberId);
                        //口径下有数据
                        if (CollectionUtils.isNotEmpty(loadCityHisClctDOS1)) {
                            LoadCityHisClctDO loadCityHisClctDO = loadCityHisClctDOS1.get(0);
                            updateClctData(dataDate, valueStr, loadCityHisClctDO);
                        } else {
                            insertClctData(cityId, caliberId, date, dataDate, valueStr);
                        }
                    } else {
                        insertClctData(cityId, caliberId, date, dataDate, valueStr);
                    }
                }


                if (basicDataMap == null) {
                    insertBasicData(cityId, caliberId, date, dataDate, valueStr);
                } else {
                    //城市id下的数据
                    List<LoadCityHisDO> loadCityHisBaiscDOS = basicDataMap.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityHisBaiscDOS)) {
                        Map<String, List<LoadCityHisDO>> collect = loadCityHisBaiscDOS.stream()
                            .collect(Collectors.groupingBy(LoadCityHisDO::getCaliberId));
                        List<LoadCityHisDO> loadCityHisBasicDOS1 = collect.get(caliberId);
                        //口径下有数据
                        if (CollectionUtils.isNotEmpty(loadCityHisBasicDOS1)) {
                            LoadCityHisDO loadCityHisBasicDO = loadCityHisBasicDOS1.get(0);
                            updateBasicData(dataDate, valueStr, loadCityHisBasicDO);
                        } else {
                            insertBasicData(cityId, caliberId, date, dataDate, valueStr);
                        }
                    } else {
                        insertBasicData(cityId, caliberId, date, dataDate, valueStr);
                    }
                }
            }
        }
    }

    public void insertClctData(String cityId, String caliberId, Date date, Date dataDate, String value)
        throws Exception {
        LoadCityHisClctDO loadCityHisClctDO = new LoadCityHisClctDO();
        loadCityHisClctDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
        loadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisClctDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisClctDO.setCityId(cityId);
        loadCityHisClctDO.setCaliberId(caliberId);
        loadCityHisClctDO.setDate(new java.sql.Date(date.getTime()));
        String dataDateStr = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        String field = "t" + dataDateStr.substring(8, 12);
        updateYesterDayClctData(field, date, cityId, caliberId, value);
        HashMap<String, BigDecimal> values = new HashMap<>();
        values.put(field, new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
        BeanMap beanMap = BeanMap.create(loadCityHisClctDO);
        beanMap.putAll(values);
        loadCityHisClctService.doCreate(loadCityHisClctDO);
    }


    public void insertBasicData(String cityId, String caliberId, Date date, Date dataDate, String value)
        throws Exception {
        LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
        loadCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
        loadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisDO.setCityId(cityId);
        loadCityHisDO.setCaliberId(caliberId);
        loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
        String dataDateStr = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        String field = "t" + dataDateStr.substring(8, 12);
        updateYesterDayBasicData(field, date, cityId, caliberId, value);
        HashMap<String, BigDecimal> values = new HashMap<>();
        values.put(field, new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
        BeanMap beanMap = BeanMap.create(loadCityHisDO);
        beanMap.putAll(values);
        loadCityHisService.doCreate(loadCityHisDO);
    }


    public void updateClctData(Date dataDate, String value, LoadCityHisClctDO loadCityHisClctDO) throws Exception {
        String dataDateStr = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        String field = "t" + dataDateStr.substring(8, 12);
        Map<String, BigDecimal> decimalMap = BasePeriodUtils
            .toMap(loadCityHisClctDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        decimalMap.put(field, new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
        updateYesterDayClctData(field, loadCityHisClctDO.getDate(), loadCityHisClctDO.getCityId(),
            loadCityHisClctDO.getCaliberId(), value);
        BeanMap beanMap = BeanMap.create(loadCityHisClctDO);
        beanMap.putAll(decimalMap);
        loadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisClctService.doUpdateLoadCityHisClctVO(loadCityHisClctDO);
    }


    public void updateBasicData(Date dataDate, String value, LoadCityHisDO loadCityHisBasicDO) throws Exception {
        String dataDateStr = DateUtils.date2String(dataDate, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        String field = "t" + dataDateStr.substring(8, 12);
        Map<String, BigDecimal> decimalMap = BasePeriodUtils
            .toMap(loadCityHisBasicDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        decimalMap.put(field, new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
        updateYesterDayBasicData(field, loadCityHisBasicDO.getDate(), loadCityHisBasicDO.getCityId(),
            loadCityHisBasicDO.getCaliberId(), value);
        BeanMap beanMap = BeanMap.create(loadCityHisBasicDO);
        beanMap.putAll(decimalMap);
        loadCityHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisService.doUpdateLoadCityHisDO(loadCityHisBasicDO);
    }


    public void updateYesterDayClctData(String field, Date date, String cityId, String caliberId, String value)
        throws Exception {
        if (field.equals("t0000")) {
            Date yesterday = DateUtils.addDays(date, -1);
            List<LoadCityHisClctDO> yesyerdayLoadCityHisVOS = loadCityHisClctService
                .findLoadCityHisVOS(cityId, yesterday, yesterday, caliberId);
            if (CollectionUtils.isNotEmpty(yesyerdayLoadCityHisVOS)) {
                LoadCityHisClctDO yesterdayData = yesyerdayLoadCityHisVOS.get(0);
                yesterdayData.setT2400(new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
                yesterdayData.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityHisClctService.doUpdateLoadCityHisClctVO(yesterdayData);
            } else {
                LoadCityHisClctDO yesterdayLoadCityHisClctDO = new LoadCityHisClctDO();
                yesterdayLoadCityHisClctDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                yesterdayLoadCityHisClctDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                yesterdayLoadCityHisClctDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                yesterdayLoadCityHisClctDO.setCityId(cityId);
                yesterdayLoadCityHisClctDO.setCaliberId(caliberId);
                yesterdayLoadCityHisClctDO.setDate(new java.sql.Date(yesterday.getTime()));
                yesterdayLoadCityHisClctDO.setT2400(new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
                loadCityHisClctService.doCreate(yesterdayLoadCityHisClctDO);
            }
        }
    }


    public void updateYesterDayBasicData(String field, Date date, String cityId, String caliberId, String value)
        throws Exception {
        if (field.equals("t0000")) {
            Date yesterday = DateUtils.addDays(date, -1);
            List<LoadCityHisDO> yesyerdayLoadCityHisBasicVOS = loadCityHisService
                .findLoadCityHisDOS(cityId, yesterday, yesterday, caliberId);
            if (CollectionUtils.isNotEmpty(yesyerdayLoadCityHisBasicVOS)) {
                LoadCityHisDO yesterdayData = yesyerdayLoadCityHisBasicVOS.get(0);
                yesterdayData.setT2400(new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
                yesterdayData.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityHisService.doUpdateLoadCityHisDO(yesterdayData);
            } else {
                LoadCityHisDO yesterdayLoadCityHisBaiscDO = new LoadCityHisDO();
                yesterdayLoadCityHisBaiscDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                yesterdayLoadCityHisBaiscDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                yesterdayLoadCityHisBaiscDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                yesterdayLoadCityHisBaiscDO.setCityId(cityId);
                yesterdayLoadCityHisBaiscDO.setCaliberId(caliberId);
                yesterdayLoadCityHisBaiscDO.setDate(new java.sql.Date(yesterday.getTime()));
                yesterdayLoadCityHisBaiscDO.setT2400(new BigDecimal(value).setScale(4, BigDecimal.ROUND_HALF_UP));
                loadCityHisService.doCreate(yesterdayLoadCityHisBaiscDO);
            }
        }
    }


    private Map<Long, ArrayList<HisData>> collectLoadCityHisClct(ArrayList<Long> dataIds, Date startDate, Date endDate)
        throws Exception {
        int sampleSpan = 15;
        TimeInterval timeInterval = new TimeInterval(startDate, endDate, (byte) 0);
        Map<Long, ArrayList<HisData>> hisVal = hisDataService.getHisVal(dataIds, timeInterval, sampleSpan);
        return hisVal;
    }


    private Map<Long, ArrayList<HisData>> collectLoadCityHisClct(ArrayList<Long> dataIds, Date startDate, Date endDate,
        int sampleSpan)
        throws Exception {
        TimeInterval timeInterval = new TimeInterval(startDate, endDate, (byte) 0);
        Map<Long, ArrayList<HisData>> hisVal = hisDataService.getHisVal(dataIds, timeInterval, sampleSpan);
        return hisVal;
    }
}
