package com.tsintergy.lf.jobhandler.collect.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsieframework.core.base.dao.Entity;
import lombok.Data;

import javax.persistence.Column;

@Data
@TableName("gdprs.T_QX_WG2D5KMRAIN")
public class WgFcRainWeather2D5kmDO implements Entity {

    @Column(name = "OBJID")
    private String objid;

    @Column(name = "FILE_NAME")
    private String fileName;

//    @Column(name = "FILEUPDATE_TIME")
//    private String fileupdateTime;
//
//    @Column(name = "SCANSTART_TIME")
//    private String scanstartTime;
//
//    @Column(name = "TIME")
//    private String time;

    @Column(name = "FILEBLOB")
    private byte[] fileblob;

//    @Column(name = "RKTIME")
//    private String rktime;
//
//    @Column(name = "FLAG")
//    private Boolean flag;
}
