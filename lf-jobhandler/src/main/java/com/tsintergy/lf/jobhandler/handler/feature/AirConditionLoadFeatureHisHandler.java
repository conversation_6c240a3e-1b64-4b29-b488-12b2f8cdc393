/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/26 10:50 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.feature;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 空调实际负荷 <br>
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @since 1.0.0
 */
@Component
@JobHandler("airConditionLoadFeatureHisHandler")
@Slf4j
public class AirConditionLoadFeatureHisHandler extends AbstractBaseHandler {


    @Autowired
    private LoadFeatureAcHisService loadFeatureAcHisService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        Date endDate = paramDate.getEndDate();
        startDate = paramDate.getStartDate();
        try {
            XxlJobLogger.log("开始统计空调实际负荷特性，开始时间为:{},结束日期为：{}", startDate, endDate);
            loadFeatureAcHisService.doHisLoadFeatureCityDay(startDate, endDate);
            XxlJobLogger.log("统计空调实际负荷特性执行成功啦");
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
            throw new BusinessException(e.getMessage(), e.toString());
        }
        return ReturnT.SUCCESS;
    }
}  
