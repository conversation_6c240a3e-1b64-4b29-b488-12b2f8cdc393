package com.tsintergy.lf.jobhandler.collect.serviceimpl.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.jobhandler.collect.api.WgFcSdWeather2D5kmService;
import com.tsintergy.lf.jobhandler.collect.pojo.WgFcSdWeather2D5kmDO;
import com.tsintergy.lf.jobhandler.collect.serviceimpl.dao.WgFcSdWeather2D5kmDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("wgFcSdWeather2D5kmService")
@DataSource("wgWeather")
public class WgFcSdWeather2D5kmServiceImpl extends ServiceImpl<WgFcSdWeather2D5kmDAO, WgFcSdWeather2D5kmDO> implements WgFcSdWeather2D5kmService {


}
