/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.collect.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsieframework.core.base.dao.BaseDO;
import lombok.Data;

/**
 * Description:  采集data_id与城市之间关联关系<br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/10 15:51
 * @Version: 1.0.0
 */

@Data
@TableName("base_city_clct_relation")
public class BaseCityClctRelationDO implements BaseDO {

    private String id;

    private String cityId;

    private String caliberId;

    private String dataId;

}