/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.collect.handler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.epri.sgcc.dcloud.dubbo.vo.Condition;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.collect.api.*;
import com.tsintergy.lf.jobhandler.collect.dto.WeatherHisClctDTO;
import com.tsintergy.lf.jobhandler.collect.dto.WeatherHisDTO;
import com.tsintergy.lf.jobhandler.collect.pojo.*;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseWeatherStationHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherStationHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.stream.IntStream;
import net.sf.cglib.beans.BeanMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 气象台 站点实际气象采集；包含站点->城市的逻辑；  站点历史气象：base_weather_station_his  城市实际气象 weather_city_fc_basic
 *
 * @Date: 2022/5/17 10:21
 * @Version: 1.0.0
 */
@JobHandler(value = "weatherBaseStationHisClctJob")
@Component
public class WeatherBaseStationHisClctJob extends AbstractBaseHandler {


    @Autowired
    WeatherStationInfoService weatherStationInfoService;


    @Autowired
    WeatherClctService weatherClctService;

    @Autowired
    BaseWeatherStationHisService baseWeatherStationHisService;

    @Autowired
    WeatherCityHisService weatherCityHisService;


    @Autowired
    SourceWeatherStationHisService sourceWeatherStationHisService;


    private static Map<String, String> cityAndStationMap = new HashMap();

    static {
        cityAndStationMap.put("4223532027237892100", "2");
        cityAndStationMap.put("4223532027254669314", "3");
        cityAndStationMap.put("4223532026130595842", "4");
        cityAndStationMap.put("4223532027355332615", "5");
        cityAndStationMap.put("4223532027305000968", "6");
        cityAndStationMap.put("4223532027321778180", "7");
        cityAndStationMap.put("4223532027372109833", "8");
        cityAndStationMap.put("4223532027338555394", "9");
        cityAndStationMap.put("4223532027271446536", "10");
    }


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Set<String> keys = cityAndStationMap.keySet();
        List<WeatherStationInfoDO> list = weatherStationInfoService.list();
        Map<String, List<WeatherStationInfoDO>> collect = list.stream()
            .collect(Collectors.groupingBy(WeatherStationInfoDO::getStationId));
        keys = collect.keySet();

        Set<String> dateStrs = new HashSet<>();
        if (s.split(",").length == 1) {
            Date end = DateUtil.getFifteenFcTime(null);
            end = DateUtils.addMinutes(end, Integer.valueOf(s));
            Date start = DateUtils.addMinutes(end, -15);
            while (start.before(DateUtils.addMinutes(end, 0))) {
                String time = DateUtils.date2String(start, DateFormatType.DATE_FORMAT_STR);
                List<WeatherHisClctDTO> datas = new ArrayList<>();
                XxlJobLogger.log("调用开始时间:" + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                for (String id : keys) {
                    List<WeatherHisClctDTO> data = getCollectStationHisData(time, id);
                    if (!CollectionUtils.isEmpty(data)) {
                        datas.addAll(data);
                    }
                }
                XxlJobLogger.log(
                    "调用结束时间:" + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR) + "采集时间为time:"
                        + time);
                int dataSize = 0;
                List<BaseWeatherStationHisDO> saveList = new ArrayList<>();
                List<BaseWeatherStationHisDO> updateList = new ArrayList<>();

                for (WeatherHisClctDTO weatherHisClctDTO : datas) {
                    if (!keys.contains(weatherHisClctDTO.getID())) {
                        continue;
                    }
                    List<WeatherStationInfoDO> weatherStationInfoDOS = collect.get(weatherHisClctDTO.getID());
                    if (CollectionUtils.isEmpty(weatherStationInfoDOS)) {
                        XxlJobLogger.log("该区域id在关联关系表中不存在,请检查:" + weatherHisClctDTO.getID() + "," + JSON
                            .toJSONString(weatherHisClctDTO));
                        continue;
                    }

                    WeatherStationInfoDO weatherStationInfoDO = weatherStationInfoDOS.get(0);
                    String cityId = weatherStationInfoDO.getCityId();

                    String timeStr = weatherHisClctDTO.getTIME();
                    String field = getFieldName(timeStr);
                    dateStrs.add(timeStr.substring(0, 10));

                    Date dataDate = DateUtils.string2Date(timeStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                    LambdaQueryWrapper<BaseWeatherStationHisDO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BaseWeatherStationHisDO::getDate, dataDate);
                    queryWrapper.eq(BaseWeatherStationHisDO::getStationId, weatherHisClctDTO.getID());
                    List<BaseWeatherStationHisDO> queryData = baseWeatherStationHisService.list(queryWrapper);
                    if (CollectionUtils.isEmpty(queryData)) {
//                        XxlJobLogger.log("查询到历史库中不存在该站点数据");
                        for (WeatherNewEnum newEnum : WeatherNewEnum.values()) {
                            Integer type = newEnum.getType();
                            String value = null;
                            switch (type) {
                                case 1:
                                    value = weatherHisClctDTO.getHUMIDITY();
                                    break;
                                case 2:
                                    value = weatherHisClctDTO.getTEMPERATURE();
                                    break;
                                case 3:
                                    value = weatherHisClctDTO.getRAINFALL_HOUR();
                                    break;
                                case 4:
                                    value = weatherHisClctDTO.getTRANSIENT_SPEED();
                                    break;
                                default:
                                    ;
                            }

                            Map<String, BigDecimal> hashMap = new HashMap<>();

                            if (new BigDecimal(value).compareTo(new BigDecimal("999.9")) == 0) {
                                continue;
                            }

                            if (value != null) {
                                hashMap.put(field, new BigDecimal(value).setScale(2, BigDecimal.ROUND_HALF_UP));
                            } else {
                                hashMap.put(field, null);
                            }

                            BaseWeatherStationHisDO baseWeatherStationHisDO = new BaseWeatherStationHisDO();
                            baseWeatherStationHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                            baseWeatherStationHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                            baseWeatherStationHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                            baseWeatherStationHisDO.setCityId(cityId);
                            baseWeatherStationHisDO.setDate(new java.sql.Date(dataDate.getTime()));
                            baseWeatherStationHisDO.setStationId(weatherHisClctDTO.getID());
                            baseWeatherStationHisDO.setType(newEnum.getType());
                            BeanMap beanMap = BeanMap.create(baseWeatherStationHisDO);
                            beanMap.putAll(hashMap);
//                            baseWeatherStationHisService.save(baseWeatherStationHisDO);
//                            XxlJobLogger.log("保存该站点数据完成。。。。");
                            saveList.add(baseWeatherStationHisDO);

                        }
//                        baseWeatherStationHisService.saveBatch(saveList);
                    } else {
//                        XxlJobLogger.log("查询到历史库中存在该站点数据");
                        Map<Integer, List<BaseWeatherStationHisDO>> typeMap = queryData.stream()
                            .collect(Collectors.groupingBy(BaseWeatherStationHisDO::getType));
                        for (WeatherNewEnum newEnum : WeatherNewEnum.values()) {
                            Integer type = newEnum.getType();
                            String value = null;
                            switch (type) {
                                case 1:
                                    value = weatherHisClctDTO.getHUMIDITY();
                                    break;
                                case 2:
                                    value = weatherHisClctDTO.getTEMPERATURE();
                                    break;
                                case 3:
                                    value = weatherHisClctDTO.getRAINFALL_HOUR();
                                    break;
                                case 4:
                                    value = weatherHisClctDTO.getTRANSIENT_SPEED();
                                    break;
                                default:
                                    ;
                            }

                            Map<String, BigDecimal> hashMap = new HashMap<>();

                            if (new BigDecimal(value).compareTo(new BigDecimal("999.9")) == 0) {
                                continue;
                            }
                            if (value != null) {
                                hashMap.put(field, new BigDecimal(value).setScale(2, BigDecimal.ROUND_HALF_UP));
                            } else {
                                hashMap.put(field, null);
                            }

                            List<BaseWeatherStationHisDO> baseWeatherStationHisDOS = typeMap.get(type);
                            if (CollectionUtils.isEmpty(baseWeatherStationHisDOS)) {
//                                XxlJobLogger.log("查询到历史库中不存在该站点" + type + "类型数据");
                                BaseWeatherStationHisDO baseWeatherStationHisDO = new BaseWeatherStationHisDO();
                                baseWeatherStationHisDO
                                    .setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                                baseWeatherStationHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                                baseWeatherStationHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                                baseWeatherStationHisDO.setCityId(cityId);
                                baseWeatherStationHisDO.setDate(new java.sql.Date(dataDate.getTime()));
                                baseWeatherStationHisDO.setStationId(weatherHisClctDTO.getID());
                                baseWeatherStationHisDO.setType(newEnum.getType());
                                BeanMap beanMap = BeanMap.create(baseWeatherStationHisDO);
                                beanMap.putAll(hashMap);
                                saveList.add(baseWeatherStationHisDO);
//                                baseWeatherStationHisService.save(baseWeatherStationHisDO);
                            } else {
//                                XxlJobLogger.log("查询到历史库中存在该站点" + type + "类型数据");
                                BaseWeatherStationHisDO baseWeatherStationHisDO = baseWeatherStationHisDOS.get(0);
                                baseWeatherStationHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                                BeanMap beanMap = BeanMap.create(baseWeatherStationHisDO);
                                beanMap.putAll(hashMap);
//                                baseWeatherStationHisService.updateById(baseWeatherStationHisDO);
                                updateList.add(baseWeatherStationHisDO);
                            }
                        }
                    }
                }
                XxlJobLogger.log("站点气象保存入库时间:" + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                baseWeatherStationHisService.saveBatch(saveList);
                XxlJobLogger.log(
                    "站点气象保存入库完成开始更新：:" + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR) + "新增数量"
                        + saveList.size() + "待更新数量" + updateList.size());

                List<List<BaseWeatherStationHisDO>> groups = IntStream.range(0, updateList.size())
                    .boxed()
                    .collect(Collectors.groupingBy(index -> index / 100))
                    .values()
                    .stream()
                    .map(indices -> indices.stream().map(updateList::get).collect(Collectors.toList()))
                    .collect(Collectors.toList());
                for (List<BaseWeatherStationHisDO> toUpdate : groups) {
                    XxlJobLogger.log("分组更新中");
                    baseWeatherStationHisService.updateBatchById(toUpdate);
                }
                XxlJobLogger.log(
                    "站点气象更新结束时间:" + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR) + "更新数量"
                        + updateList.size());
                start = DateUtils.addMinutes(start, 15);
            }

            XxlJobLogger.log("开始统计城市气象数据。。");
//            wrapperCityWeather(dateStrs);
            List<String> dataList = new ArrayList<>(dateStrs);
            weatherCityHisService.doClctWeatherStationToCity(dataList, cityAndStationMap);
            XxlJobLogger.log("统计城市气象数据结束");
        }
        if (s.split(",").length == 2) {
            XxlJobLogger.log("开始进入补采程序补采参数为:" + s);
            String[] params = s.split(",");
            String startDateStr = params[0];
            String endDateStr = params[1];
            Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

            Map<String, List<WeatherHisClctDTO>> hashMap = new HashMap<>();
            while (startDate.before(DateUtils.addDays(endDate, 1))) {
                String time = DateUtils.date2String(startDate, DateFormatType.DATE_FORMAT_STR);
                String dayString = time.substring(0, 10);
                dateStrs.add(dayString);
                List<WeatherHisClctDTO> datas = new ArrayList<>();
                for (String id : keys) {
                    List<WeatherHisClctDTO> data = getCollectStationHisData(dayString + " 00:00:00",
                        dayString + " 23:45:00", id);
                    if (!CollectionUtils.isEmpty(data)) {
                        datas.addAll(data);
                    }
                }

                hashMap = datas.stream().collect(Collectors.groupingBy(t -> getFieldName(t.getTIME())));
                wraperWeatherStationHisData(time, hashMap, collect);
                hashMap.clear();
                XxlJobLogger.log("当前日期采集结束:" + time.substring(0, 10));
                startDate = DateUtils.addDays(startDate, 1);
            }
            XxlJobLogger.log("开始统计城市气象数据。。");
            //            wrapperCityWeather(dateStrs);
            List<String> dataList = new ArrayList<>(dateStrs);
            weatherCityHisService.doClctWeatherStationToCity(dataList, cityAndStationMap);
        }
        return ReturnT.SUCCESS;
    }

    private List<WeatherHisClctDTO> getCollectStationHisData(String startTime, String endTime, String id) {
        Condition condition = new Condition();
        condition.and()
            .and("TIME", ">=", startTime);
        condition.and("TIME", "<=", endTime);
        if (id != null) {
            condition.and()
                .and("ID", "=", id);
        }
        JSONObject weatherHisData = weatherClctService.getBaseWeatherHisData(condition);
//        XxlJobLogger.log("参数:" + startTime+","+endTime + ",调用结果响应:" + weatherHisData.toJSONString());
        WeatherHisDTO weatherHisDTO = JSONObject
            .parseObject(weatherHisData.toJSONString(), WeatherHisDTO.class);
        //获取其中的数据部分
        return weatherHisDTO.getData();
    }

    void wrapWeatherStationHisBasic(Map<String, Map<String, BigDecimal>> map,
        Map<String, List<WeatherStationInfoDO>> collect
        , Date date, Map<String, BaseWeatherStationHisDO> hisBasicDOHashMap) {


        List<BaseWeatherStationHisDO> updateList = new ArrayList<>();
        List<BaseWeatherStationHisDO> insertList = new ArrayList<>();

        for (Map.Entry<String, Map<String, BigDecimal>> entry : map.entrySet()) {
            String areaIdAndType = entry.getKey();
            String areaId = areaIdAndType.split("_")[0];
            Integer type = Integer.valueOf(areaIdAndType.split("_")[1]);

            Map<String, BigDecimal> value = entry.getValue();

            String cityId = null;
            if (CollectionUtils.isEmpty(collect.get(areaId))) {
                XxlJobLogger.log("该区域id在关联关系表中不存在,请检查:" + areaId);
                continue;
            } else {
                cityId = collect.get(areaId).get(0).getCityId();
            }

            if (hisBasicDOHashMap == null || hisBasicDOHashMap.get(areaIdAndType) == null) {
                BaseWeatherStationHisDO hisBasicDO = new BaseWeatherStationHisDO();
                hisBasicDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                hisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                hisBasicDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                hisBasicDO.setCityId(cityId);
                hisBasicDO.setDate(new java.sql.Date(date.getTime()));
                hisBasicDO.setStationId(areaId);
                hisBasicDO.setType(type);
                BeanMap beanMap = BeanMap.create(hisBasicDO);
                beanMap.putAll(value);
                insertList.add(hisBasicDO);
            } else {
                BaseWeatherStationHisDO baseWeatherStationHisDO = hisBasicDOHashMap.get(areaIdAndType);
                baseWeatherStationHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                BeanMap beanMap = BeanMap.create(baseWeatherStationHisDO);
                beanMap.putAll(value);
                updateList.add(baseWeatherStationHisDO);
            }


        }
        baseWeatherStationHisService.saveBatch(insertList);
        baseWeatherStationHisService.updateBatchById(updateList);
    }


    private Map<String, Map<String, BigDecimal>> initWeatherStationTypeDataMap(
        Map<String, List<WeatherHisClctDTO>> hashMap) {

        // key = areaId_typeId
        Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();
        for (Map.Entry<String, List<WeatherHisClctDTO>> entry : hashMap.entrySet()) {
            String field = entry.getKey();
            List<WeatherHisClctDTO> value = entry.getValue();

            for (WeatherHisClctDTO weatherHisClctDTO : value) {

                for (WeatherNewEnum weatherNewEnum : WeatherNewEnum.values()) {

                    BigDecimal weatherValue = null;
                    switch (weatherNewEnum.getType()) {

                        case 1:
                            weatherValue = BigDecimalUtils
                                .stringToBigdecimal(weatherHisClctDTO.getHUMIDITY(), 2,
                                    BigDecimal.ROUND_HALF_UP);
                            break;

                        case 2:
                            weatherValue = BigDecimalUtils
                                .stringToBigdecimal(weatherHisClctDTO.getTEMPERATURE(), 2,
                                    BigDecimal.ROUND_HALF_UP);
                            break;


                        case 3:
                            weatherValue = BigDecimalUtils
                                .stringToBigdecimal(weatherHisClctDTO.getRAINFALL_HOUR(), 2,
                                    BigDecimal.ROUND_HALF_UP);
                            break;


                        case 4:
                            weatherValue = BigDecimalUtils
                                .stringToBigdecimal(weatherHisClctDTO.getTRANSIENT_SPEED(), 2,
                                    BigDecimal.ROUND_HALF_UP);
                            break;
                        default:
                            break;
                    }
                    String key = weatherHisClctDTO.getID() + "_" + weatherNewEnum.getType();
                    if (resultMap.get(key) == null) {
                        Map<String, BigDecimal> map = new HashMap<>();
                        map.put(field, weatherValue);
                        resultMap.put(key, map);
                    } else {
                        Map<String, BigDecimal> decimalMap = resultMap.get(key);
                        decimalMap.put(field, weatherValue);
                    }
                }
            }
        }
        return resultMap;
    }


    private Map<String, BaseWeatherStationHisDO> initWeatherStationHisBasicMap(Date date) {
        LambdaQueryWrapper<BaseWeatherStationHisDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseWeatherStationHisDO::getDate, date);
        List<BaseWeatherStationHisDO> queryData = baseWeatherStationHisService.list(queryWrapper);


        Map<String, BaseWeatherStationHisDO> hisBasicDOHashMap = null;
        if (!CollectionUtils.isEmpty(queryData)) {
            hisBasicDOHashMap = new HashMap<>();
            for (BaseWeatherStationHisDO baseWeatherStationHisDO : queryData) {
                hisBasicDOHashMap.put(baseWeatherStationHisDO.getStationId() + "_"
                    + baseWeatherStationHisDO.getType(), baseWeatherStationHisDO);
            }
        }
        return hisBasicDOHashMap;
    }


    private String getFieldName(String dateStr) {
        return "t" + dateStr.substring(11, 16).replace(":", "");
    }


    private List<WeatherHisClctDTO> getCollectStationHisData(String time, String id) {
        Condition condition = new Condition();
        condition.and()
            .and("TIME", "=", time);
        if (id != null) {
            condition.and()
                .and("ID", "=", id);
        }
        JSONObject weatherHisData = weatherClctService.getBaseWeatherHisData(condition);
//        XxlJobLogger.log("参数:" + time + ",调用结果响应:" + weatherHisData.toJSONString());
        WeatherHisDTO weatherHisDTO = JSONObject
            .parseObject(weatherHisData.toJSONString(), WeatherHisDTO.class);
        //获取其中的数据部分
        return weatherHisDTO.getData();
    }


    private void wraperWeatherStationHisData(String time, Map<String, List<WeatherHisClctDTO>> hashMap
        , Map<String, List<WeatherStationInfoDO>> collect) {
        String dateStr = time.substring(0, 10);
        Date date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        XxlJobLogger.log("开始初始化数据库历史气象数据");
        Map<String, BaseWeatherStationHisDO> hisBasicDOHashMap = initWeatherStationHisBasicMap(date);
        XxlJobLogger.log("数据库历史气象数据完毕");


        XxlJobLogger.log("开始初始化采集到的历史气象数据");
        Map<String, Map<String, BigDecimal>> map = initWeatherStationTypeDataMap(hashMap);
        XxlJobLogger.log("采集到的历史气象数据");


        XxlJobLogger.log("开始更新历史气象数据");
        wrapWeatherStationHisBasic(map, collect, date, hisBasicDOHashMap);
        XxlJobLogger.log("历史气象数据更新完成");
    }


}