/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/11/20 22:29 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.util;



import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * 气象数据采集数据补充工具类
 *
 * <AUTHOR>
 * @create 2022/02/22
 * @since 1.0.0
 */
public class WeatherProcessDataUtil {


    /**
     * 站点数据转换成城市数据；其中主要逻辑：step1：站点24点数据异常值处理； step2：24点转换成96点；注意：两天加一起至少有两个点
     *
     * @param yesterdayMidData 昨日气象站点24点数据 数据请从0100~2400  昨日无数据请传null
     * @param todayDataSrc 今日气象站点24点数据 数据请从0100~2400
     * @return 今日气象0000~2400 97点数据
     */
    public static List<BigDecimal> stationTransitionToCityData(List<BigDecimal> yesterdayMidData,
        List<BigDecimal> todayDataSrc) throws Exception {
        boolean yesterdayNull = false;
        //昨日数据异常时，今日数据需要减去的数据数量
        int todayNullNum = 0;
        //昨日数据为空，则只补充0100~2400的数据；数据完整时共计93
        if (checkListThoroughIsNull(yesterdayMidData)) {
            yesterdayNull = true;
            //减去0000~0045 四个点
            todayNullNum = 4;
        }
        if (checkListThoroughIsNull(todayDataSrc)) {
            return null;
        }
        //清洗今日数据；去掉末尾的null
        List<BigDecimal> todayData = WeatherProcessDataUtil.cleanList(todayDataSrc);
        //当天的站点数据List 24点
        List<BigDecimal> todayDataResult = cleanStationData(todayData);
        //昨天的站点数据List 24点
        List<BigDecimal> yesterdayFcDataResult = cleanStationData(yesterdayMidData);
        List<BigDecimal> twoDaysData = new ArrayList<>();
        twoDaysData.addAll(yesterdayFcDataResult);
        twoDaysData.addAll(todayDataResult);
        //两天数据加在一起差值； 0100~2400 数据完整时共计96+93 189个点
        List<BigDecimal> bigDecimals = PeriodDataUtil.complementBetweenValue(twoDaysData);
        //如果昨日无数据，需要加上今天的空数据数
        if (yesterdayNull) {
            int i1 = numByFirstToHead(todayDataSrc) + todayNullNum;
            //今日数据前面的点用null补齐
            List<BigDecimal> resultList = ColumnUtil.getZeroOrNullList(97, null);
            for (int t = 0; t < bigDecimals.size(); t++) {
                resultList.set(i1+t, bigDecimals.get(t));
            }
            return resultList;
        } else {
            //昨天的填充点数
            int i = numByFirstToEnd(yesterdayFcDataResult);
            //两天点数-昨天点数 = 今日点数
            List<BigDecimal> bigDecimals1 = bigDecimals.subList(i - 1, bigDecimals.size());
            //昨天数据计算的0000~0100之间的点,今日数据后面的点用null补齐
            List<BigDecimal> resultList = ColumnUtil.getZeroOrNullList(97, null);
            if (bigDecimals1.size() <= 97) {
                for (int t = 0; t < bigDecimals1.size(); t++) {
                    resultList.set(t, bigDecimals1.get(t));
                }
            }
            return resultList;
        }
    }


    /**
     * 判断List是否为null；里面所有元素是null时，也判定为null
     *
     * @return true  为空  false 不是空
     */
    private static boolean checkListThoroughIsNull(List<BigDecimal> srcList) {
        List<BigDecimal> midList = srcList;
        if (CollectionUtils.isEmpty(srcList)) {
            return true;
        } else {
            int i = 0;
            for (BigDecimal src : srcList) {
                if (src == null) {
                    i++;
                }
            }
            if (i == srcList.size()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回第一个非空数据所在下标，也是前面有几个空点的数量
     */
    private static int firstNotNullNum(List<BigDecimal> srcList) {
        int i = 0;
        for (BigDecimal src : srcList) {
            if (src == null) {
                i++;
            } else {
                break;
            }
        }
        return i;
    }

    /**
     * 返回昨天补充的数据数量  第一个非空点到最后一个点数量  用于96点
     *
     * @param srcList 1~24共 24点list
     */
    private static int numByFirstToEnd(List<BigDecimal> srcList) {
        int i = firstNotNullNum(srcList);
        return (srcList.size() - i - 1) * 3 + (srcList.size() - i);
    }


    /**
     * 返回昨天补充的数据数量  第一个非空点到第一个点 0100点的数量  用于96点
     *
     * @param srcList 24点list  1~24共 24点list
     */
    private static int numByFirstToHead(List<BigDecimal> srcList) {
        int i = firstNotNullNum(srcList);
        return i * 4;
    }


    /**
     * 对站点的气象数据做数据清洗；
     */
    public static List<BigDecimal> cleanStationData(List<BigDecimal> srcList) {
        if (srcList == null) {
            return ColumnUtil.getZeroOrNullList(24, null);
        }
        List<BigDecimal> fcDataReslut = new ArrayList<>();
        for (BigDecimal data : srcList) {
            if (data == null) {
                fcDataReslut.add(null);
            } else {
                if (data.abs().compareTo(new BigDecimal(800)) > 0) {
                    fcDataReslut.add(null);
                } else {
                    fcDataReslut.add(data);
                }
            }
        }
        return fcDataReslut;
    }


    /**
     * 去除当天数据末尾的null数据
     */
    private static List<BigDecimal> cleanList(List<BigDecimal> srcList) {
        Collections.reverse(srcList);
        Iterator<BigDecimal> iterator = srcList.iterator();
        while (iterator.hasNext()) {
            BigDecimal i = iterator.next();
            if (i == null) {
                iterator.remove();
            } else {
                break;
            }
        }
        Collections.reverse(srcList);
        return srcList;
    }

}