/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.collect.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import lombok.Data;

import java.sql.Timestamp;


/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/18 19:42
 * @Version: 1.0.0
 */


@Data
@TableName("weather_station_fc_basic")
public class WeatherStationFcBasicDO extends Base96DO {


    private String id;


    /**
     * 日期
     */

    private java.sql.Date date;

    /**
     * 城市ID
     */
    private String stationId;


    private String cityId;

    /**
     * 气象类型
     */

    private Integer type;

    /**
     * 创建时间
     */

    private Timestamp createtime;

    /**
     * 更新时间
     */

    private Timestamp updatetime;
}