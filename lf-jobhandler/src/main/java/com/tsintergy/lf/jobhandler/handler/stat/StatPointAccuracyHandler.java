package com.tsintergy.lf.jobhandler.handler.stat;

import com.tsieframework.core.base.domain.period.BasePeriod96DO;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.base.BasePeriod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@JobHandler("statPointAccuracyHandler")
@Slf4j
public class StatPointAccuracyHandler extends AbstractBaseHandler {

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        String lowerTime = "0200,0600";
        String hightTime1 = "1000,1200";
        String hightTime2 = "1200,1600";
        String hightTime3 = "1600,2200";
        String startDateStr = "20220615";
        String endDateStr = "20220930";
        Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        while (startDate.before(DateUtils.addDays(endDate, 1))) {
            List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(CityConstants.PROVINCE_ID,
                startDate, startDate, "10");

            LoadCityFcDO reportCityFcDO = loadCityFcService.findReportCityFcDO(startDate, CityConstants.PROVINCE_ID,
                "10", null, true);
            if(reportCityFcDO == null){
                reportCityFcDO  = loadCityFcService.findReportCityFcDO(startDate, CityConstants.PROVINCE_ID,
                    "10", "10", false);
            }

            if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                LoadCityHisDO loadCityHisDO = loadCityHisDOS.get(0);
                Map<Integer, BigDecimal> low = getHightLowPoint(loadCityHisDO, reportCityFcDO, lowerTime, "low",
                    DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR));


                Map<Integer, BigDecimal> hight1 = getHightLowPoint(loadCityHisDO, reportCityFcDO,hightTime1, "hight",
                    DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR));

                Map<Integer, BigDecimal> hight2 = getHightLowPoint(loadCityHisDO, reportCityFcDO, hightTime2, "hight",
                    DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR));

                Map<Integer, BigDecimal> hight3 = getHightLowPoint(loadCityHisDO, reportCityFcDO, hightTime3, "hight",
                    DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR));


            }
            startDate = DateUtils.addDays(startDate, 1);
        }
        return ReturnT.SUCCESS;
    }

    public Map<Integer, BigDecimal> getHightLowPoint(BasePeriod96VO loadCityHisDO, BasePeriod96VO loadCityFcDO,
        String timeRange, String type, String date) {

        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO);


        List<BigDecimal> fcList = BasePeriodUtils.toList(loadCityFcDO,
            Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);

        String[] split = timeRange.split(",");
        String startTime = split[0];
        String endTime = split[1];

        int startPoint = 0;
        int endPoint = 0;
        // 从0000开始
        if (Constants.WEATHER_CURVE_START_WITH_ZERO) {
            // 0000,0015,0030,0045,[0100  0115  0130)  23:45
            //    0    1    2    3    4    5    6     95
            // 0045,0345;

            String startHourStr = startTime.substring(0, 2);
            String startMinStr = startTime.substring(2, 4);
            startPoint = Integer.valueOf(startHourStr) * 4 + Integer.valueOf(startMinStr) / 15;

            String endHourStr = endTime.substring(0, 2);
            String endMinStr = endTime.substring(2, 4);
            endPoint = Integer.valueOf(endHourStr) * 4 + Integer.valueOf(endMinStr) / 15;
        } else {
            // 0015,0030,0045,0100  0115  0130  0145  0200  0215  0230  0245  0300
            //    0    1    2    3    4     5      6    7     8    9     10    11
            //  0315,0330,0345,0400  0415  0430  0445  0500  0515  0530  0545 0600                                                    0545  0600
            //   12   13  14   15    16   17    18     19   20    21     22    23


            String startHourStr = startTime.substring(0, 2);
            String startMinStr = startTime.substring(2, 4);
            startPoint = Integer.valueOf(startHourStr) * 4 + Integer.valueOf(startMinStr) / 15 - 1;

            String endHourStr = endTime.substring(0, 2);
            String endMinStr = endTime.substring(2, 4);
            endPoint = Integer.valueOf(endHourStr) * 4 + Integer.valueOf(endMinStr) / 15 - 1;
        }

        List<BigDecimal> requireList = bigDecimals.subList(startPoint, endPoint);
        BigDecimal value = requireList.get(0);
        int valueIndex = 0;


        if (type.equals("low")) {
            for (int i = 0; i < requireList.size(); i++) {
                BigDecimal element = requireList.get(i);
                if (value.compareTo(element) > 0) {
                    value = element;
                    valueIndex = i;
                }
            }
        } else if (type.equals("hight")) {
            for (int i = 0; i < requireList.size(); i++) {
                BigDecimal element = requireList.get(i);
                if (value.compareTo(element) < 0) {
                    value = element;
                    valueIndex = i;
                }
            }
        }
        // 需要拿到在大的集合里的下标
        valueIndex = valueIndex + startPoint;
        String valueTime = "";
        if (Constants.WEATHER_CURVE_START_WITH_ZERO) {
            // 0000,0015,0030,0045,[0100  0115  0130)  23:45
            //    0    1    2    3    4    5    6     95
            int hour = valueIndex / 4;
            int min = valueIndex % 4;
            if (hour < 10) {
                valueTime = "0" + hour + ":" + (min * 15 == 0 ? "00" : "" + min * 15);
            } else {
                valueTime = "" + hour + ":" + (min * 15 == 0 ? "00" : "" + min * 15);
            }
        } else {
            // 0015,0030,0045,0100   0115                24:00
            //    0    1    2   3     4                    95
            int hour = (valueIndex + 1) / 4;
            int min = (valueIndex + 1) % 4;
            if (hour < 10) {
                valueTime = "0" + hour + ":" + (min * 15 == 0 ? "00" : "" + min * 15);
            } else {
                valueTime = "" + hour + ":" + (min * 15 == 0 ? "00" : "" + min * 15);
            }
        }
        // XxlJobLogger.log(date + "-->" + type + "------->" + valueTime + "(" + valueIndex + ")---->" + value);
        Map<Integer, BigDecimal> map = new HashMap();
        map.put(valueIndex, value);

        BigDecimal fc = null;
        BigDecimal accuracy = null;
        if(!CollectionUtils.isEmpty(fcList)) {

             fc = fcList.get(valueIndex);
            BigDecimal deviation = fc.subtract(value);
            if (deviation.compareTo(BigDecimal.ZERO) < 0) {
                deviation = BigDecimal.ZERO.subtract(deviation);
            }
            BigDecimal deviationAccuracy = BigDecimal.ONE.subtract(
                deviation.divide(value, 5, BigDecimal.ROUND_HALF_UP));

                accuracy = deviationAccuracy.multiply(
                new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);

        }else {
            XxlJobLogger.log("日期:" + date+"无上报预测数据");
        }

        XxlJobLogger.log("日期:" + date
            + "--->类型:" + type
            + "--->时间范围:" + timeRange
            + "--->实际数值类型时刻点:" + valueTime
            + "--->实际数值类型下标:" + valueIndex
            + "--->实际数值:" + value
            + "--->预测数值:" + fc
            + "--->准确率:" + accuracy);
        return map;
    }
}
