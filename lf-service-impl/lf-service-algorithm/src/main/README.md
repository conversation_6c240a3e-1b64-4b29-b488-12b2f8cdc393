## 算法模块调用说明

### 算法说明：
每日算法有：
1. 相似日 101 -----暂无结果
2. 偏差补偿 102 ----- 暂无结果
3. 支持向量机 103
4. 标点 104
5. 决策树 105
6. 梯度提升 106
 
> 决策树LGB，梯度提升XG 神经网络 外部调用时不可以用多线程，其他的每日算法可以使用
 
在手动预测页面还多多两个算法
1. 综合模型 800
2. 神经网络 109

在实施页面的正常日预测中，出去每日算法的6个，再一个神经网络。

这两个算法每日也跑，但是比较耗费资源，单独跑。在手动预测页面不会重复执行，只会查询结果。


算法模块对外顶层接口：lf-service-api中的`CustomizationForecastService`
```
/**
     * 预测顶层接口 默认实现中规范了整体的执行流程；自定义流程需重写方法
     */
    T doForecast(E param) throws Exception;
```
参数对象同意继承自 `Param`对象，结果对象统一继承自`Result`对象。


## 调用流程：

```
graph LR
lf-service-base模块-->CustomizationForecastService
CustomizationForecastService-->抽象AbstractForecastService
抽象AbstractForecastService-->实现类1
抽象AbstractForecastService-->实现类2
抽象AbstractForecastService-->实现类3
实现类1-->Executor
实现类2-->Executor
实现类3-->Executor
Executor-->规范流程AbstractBaseExecutor
规范流程AbstractBaseExecutor-->BaseMultiMethodExecutor
BaseMultiMethodExecutor-->扩展Executor1
BaseMultiMethodExecutor-->扩展Executor2
规范流程AbstractBaseExecutor-->规范流程Executor1
规范流程AbstractBaseExecutor-->规范流程Executor2
规范流程AbstractBaseExecutor-->规范流程Executor3
Executor-->高度定制Executor1
```
### 说明：
##### 参数：E extends Param
lf-service-base所传的参数对象E需继承自Param。Param中定义了通用参数；其中参数对象E不再包含具体的负荷数据or气象数据，只是查询指令参数；具体查询动作放在实现类的mergeData()中。
> lf-service-base创建Param子类对象时，需要指定Param中的`AlgorithmEnum`。该参数用于`抽象AbstractBaseExecutor`中的`工厂ForecastMethodFactory`适配具体的Executor。
Param中的`AlgorithmDetail algorithmDetail`参数postprocess()无需指定。后续的`工厂AlgorithmDetailFactory`会依据`AlgorithmEnum`适配算法路径参数。

##### CustomizationForecastService
其中定义3大方法：
1. doForecast():预测顶层接口，**有默认实现**。其中规范了整体的执行流程；自定义流程需。重写方法
2. execute()：方法只被 doForecast 调用。**有默认实现**。执行Executor内部流程的顶层接口 内部调用Executor的三大方法。
其中会根据Param的`AlgorithmEnum`参数适配Executor以及装载算法的路径；模板名；算法key等信息。
3. mergeData()：根据doForecast()方法传的参数查询算法所需要的数据。默认实现仅供**每日算法**使用；其他算法请自己实现。

##### 抽象AbstractForecastService
给了`CustomizationForecastService`的三个方法的默认实现。

##### 实现类1&2&3
一般情况无需关注`doForecast()`和`execute()`方法，实现类中只需重写mergeData()方法，以查询算法所需要的数据。  
实现类1&2&3的整体调用逻辑控制在父类`抽象AbstractForecastService`的`doForecast()`方法中。

##### Executor 算法执行器顶层接口
1. preprocess():执行预处理方法，**有默认实现**
2. execute():调用算法命令方法，**有默认实现**
3. postprocess():解析结果方法，**有默认实现**。后续针对返回不同的实体对象，各个Executor1&2&3需自行实现。

##### 抽象AbstractBaseExecutor
其中规范了接口Executor三大方法内部方法的调用流程，并提供了默认方法。其中添加了抽象方法：
1. writeData(E param, Map<String, Object> datas, Map<String, Object> map)
用于在preprocess()方法中二次处理写入的数据对象。需要所有子类重写。

##### BaseMultiMethodExecutor 每日算法通用基类
其中主要抽取每日预测算法公用的数据拼接，并统一的结果处理方法。
1. 实现writeData()
2. 实现postprocess()


##### 扩展Executor1&2
针对每日预测有特殊数据拼接要求的算法进行数据扩展。主要复写了writeData()方法。

##### 规范流程Executor1&2
使用常规算法调用流程，但是数据需自定义实现的Executor。例如：台风算法；综合相似分析；节假日分析等等。流程由AbstractBaseExecutor规范，只需补充其中的数据方法即可
1. 实现writeData()
2. 实现postprocess()


> 补充：
mergeData()与writeData()方法的不同：mergeData（）方法主要查询数据放入map；writeData()方法将map中的数据写入file_in文件。此处分成两步的主要原因是每日算法中多个算法file_in中的出力数据大同小异；放入map后直接从map获取数据；避免了多次查询数据库。

##### 高度定制Executor1
使用非常规算法调用流程，用户自行实现Executor中的方法。例如：稳定度分析算法。

##### 工厂ForecastMethodFactory
在`抽象AbstractForecastService`的`execute()`方法中，通过Param的`AlgorithmEnum`适配具体的Executor。

##### 工厂AlgorithmDetailFactory
在`抽象AbstractForecastService`的`execute()`方法中，通过Param的`AlgorithmEnum`装载各个算法的路径&In&Out&exe key等配置。

##### 注意：
在工厂`AlgorithmDetailFactory`中配置算法执行文件的名称时，配置的是查询yml文件的key，具体名称在`application-config-algorithm.yml`配置。
例如：
```
 switch (algorithmEnum) {
            //台风算法
            case TYPHOON_SVM:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.TYPHOON_IN, AlgorithmConstants.TYPHOON_OUT,
                    AlgorithmConstants.TYPHOON_RUN, AlgorithmConstants.TYPHOON_TEMPLATE_NAME,
                   //该值为： TYPHOON_EXE_NAME = "TyphoonLoadPredict";
                    AlgorithmConstants.TYPHOON_EXE_NAME,
                    AlgorithmConstants.LICENCE_PATH, true);
                break;
```

application-config-algorithm.yml 中：
```
#台风算法
TyphoonLoadPredict: TyphoonLoadPredict
```
因此更新旧有系统时需要在application-config-algorithm.yml文件中添加缺失的算法名称。目前已知缺失：
```
#综合模型
ModelFusion: STLF_ModelFusion3
#灵敏度分析
ANALYSIS_SENSITIVITY: analysis_sensitivity
#台风算法
TyphoonLoadPredict: TyphoonLoadPredict
```
重构调整了LGB决策树算法的file_in&out&run的路径，和其他的每日算法一样。请删除旧有系统的LGB相关的路径配置。
调整原系统LGB算法执行文件位置到FORECAST.RUN.PATH下。

