/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.Executor;
import com.tsintergy.lf.serviceapi.algorithm.dto.*;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcModifyService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.tsintergy.lf.serviceimpl.factory.AlgorithmDetailFactory;
import com.tsintergy.lf.serviceimpl.factory.ForecastMethodFactory;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 算法通用 逻辑实现 主要抽象出数据拼接方法 mergeData()
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractForecastService<E extends Param, T extends Result> implements
        CustomizationForecastService<E, T> {

    @Autowired
    CityService cityService;
    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;
    @Autowired
    private ForecastDataService forecastDataService;
    @Autowired
    private SettingSystemService systemService;
    @Autowired
    private CoreConfigInfo coreConfigInfo;
    @Autowired
    private WeatherCityFcModifyService weatherCityFcModifyService;
    @Autowired
    private HolidayDAO holidayDAO;
    @Autowired
    private LoadCityFcDAO loadCityFcDAO;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SettingSystemService settingSystemService;
    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;
    @Autowired
    private WeatherCityFcDAO weatherCityFcDAO;
    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    @Override
    public void forecast(E param) throws Exception {
//        if (!validAlgorithmRunable(param)) {
//            log.error(param.getCityId() + "没有配置算法-" + param.getAlgorithmEnum().getDescription() + "-运行权限");
//            return;
//        }
        Map<String, Object> dataMap = new HashMap<>(16);
        mergeData(param, dataMap);
        execute(param, dataMap);
    }

    /**
     * 1.匹配执行器；2.装载算法参数，例如in&out文件路径等
     *
     * @param param
     * @param dataMap
     */
    @SneakyThrows
    public void execute(E param, Map<String, Object> dataMap) {
//        if (!validAlgorithmRunable(param)) {
//            log.error(param.getCityId() + "没有配置算法-" + param.getAlgorithmEnum().getDescription() + "-运行权限");
//            return;
//        }
        //step1：匹配算法执行器
        Executor<E, T> executor = ForecastMethodFactory.create(param);
        //step2：装载算法执行路径 模板名称
        param.setAlgorithmDetail(AlgorithmDetailFactory.create(param));
        //step3： 执行预处理；个别算法in&out文件特殊，可以在此处处理
        executor.preprocess(param, dataMap);
        //step4：执行器调用算法命令
        executor.execute(param);
    }


    /**
     * 功能描述: 验证地市或者省调能否运行该算法<br> 目前使用aif算法包的算法没有关联这里的逻辑；
     *
     * @param
     * @return:
     * @since: 1.0.0
     * @Author:
     * @Date: 2022/8/8 16:37
     */

    public boolean validAlgorithmRunable(E param) throws Exception {
        String methodType = param.getAlgorithmEnum().getType();
        if (!StringUtils.isEmpty(param.getCityId())) {
            CityDO cityDO = cityService.findCityById(param.getCityId());
            if (cityDO != null) {
                Integer type = cityDO.getType();
                SettingSystemDO byFieldId = null;
                if (type.equals(CityConstants.CITY_TYPE)) {
                    byFieldId = settingSystemService.findByFieldId(SystemConstant.CITY_RUN_ALGORITHM);
                } else if (type.equals(CityConstants.PROVINCE_TYPE)) {
                    byFieldId = settingSystemService.findByFieldId(SystemConstant.PROVINCE_RUN_ALGORITHM);
                }


                if (byFieldId == null) {
                    log.error("省调，地调可执行算法列表配置没有设置。。。");
                } else {
                    String value = byFieldId.getValue();
                    String[] split = value.split(",");
                    List<String> ableRunAlgorithmList = Stream.of(split).collect(Collectors.toList());
                    boolean contains = ableRunAlgorithmList.contains(methodType);
                    return contains;
                }
            }
        }
        return true;
    }


    /**
     * 查询算法所需数据的默认方法 该方法 只针对ForecastParam 参数 通用于每日跑批的多算法预测&台风预测数据拼接
     *
     * @param param  数据参数
     * @param srcMap 数据集合map
     */
    @Override
    public void mergeData(E param, Map<String, Object> srcMap) throws Exception {
        ForecastParam mergeDataParam = (ForecastParam) param;
        List<Date> dateList = mergeDataParam.getDateList();
        Date startFcDay = CollectionUtils.isEmpty(dateList) ? mergeDataParam.getForecastDate() : dateList.get(0);
        Date endFcDay =
                CollectionUtils.isEmpty(dateList) ? mergeDataParam.getForecastDate() : dateList.get(dateList.size() - 1);
        String cityId = mergeDataParam.getCityId();
        String caliberId = mergeDataParam.getCaliberId();
        Integer fcDayWeatherType = mergeDataParam.getFcstDayWeatherType();
        Boolean weatherModify = mergeDataParam.getWeatherModify() == null ? false : mergeDataParam.getWeatherModify();
        //查询三年的历史负荷
        Date lastYearStartDate = DateUtil.getMoveDay(startFcDay, -1200);
        mergeHisLoad(srcMap, cityId, caliberId, lastYearStartDate, endFcDay);
        mergeWeatherData(cityId, srcMap, lastYearStartDate, endFcDay, weatherModify, fcDayWeatherType, caliberId, mergeDataParam);
        mergeHolidayData(srcMap);
        //气象特性往前推1200天
//        Date lastYearDate = DateUtils.addYears(startFcDay, -3);
        mergeWeatherFeature(srcMap, cityId, endFcDay, lastYearStartDate, weatherModify, fcDayWeatherType, caliberId, mergeDataParam);
    }

    /**
     * 节假日数据
     */
    void mergeHolidayData(Map<String, Object> datas) throws Exception {
        List<HolidayDO> holidayVOS = forecastDataService.findAllHolidays();
        holidayVOS.sort(Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        datas.put(AlgorithmConstants.HOLIDAY_INFO, holidayVOS);
    }

    /**
     * 匹配历史气象特性数据
     */
    public void mergeWeatherFeature(Map<String, Object> datas, String cityId, Date endFcstDay,
                                    Date lastYearStartDate, Boolean weatherModify, Integer fcstDayWeatherType, String caliberId, ForecastParam mergeDataParam) throws Exception {
        String weatherCityId = cityService.findWeatherCityId(cityId);
        if (caliberId.equals("1") && cityId.equals("1")) {
            weatherCityId = "1";
        }
        List<AlgorithmEnum> algorithmEnums = mergeDataParam.getAlgorithmEnums();
        if (caliberId.equals("1") && cityId.equals("1")&& algorithmEnums.size() ==1 && algorithmEnums.get(0).equals(AlgorithmEnum.FORECAST_INNOVATION_CHECK)) {
            weatherCityId = "2";
        }
        int weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather.feature");
        if (type != null) {
            weatherType = Integer.parseInt(type);
        }
        if (weatherType == 0) {//训练数据用历史数据做预测
            List<WeatherCityHisDO> weatherCityHisVOs = (List<WeatherCityHisDO>) datas.get(AlgorithmConstants.DATA_WEATHER_KEY);
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs = weatherFeatureStatService.statisticsDayFeature(weatherCityHisVOs);
            weatherFeatureCityDayHisVOs.sort(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate));
            datas.put(AlgorithmConstants.WEATHER_FEATURE_HIS, weatherFeatureCityDayHisVOs);
        } else { //从预测库中取数据
            List<WeatherFeatureCityDayHisDO> hisVOS = new ArrayList<>();
            List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS =
                    forecastDataService.findWeatherFeatureCityDayFcDO(weatherCityId, lastYearStartDate, endFcstDay);
            for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO : weatherFeatureCityDayFcVOS) {
                WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
                BeanUtils.copyProperties(weatherFeatureCityDayFcVO, weatherFeatureCityDayHisVO);
                hisVOS.add(weatherFeatureCityDayHisVO);
            }
            datas.put(AlgorithmConstants.WEATHER_FEATURE_HIS, hisVOS);
        }

        // 修正上报-手动预测使用
        if (weatherModify) {
            countFcEndDateWeatherFeature(datas, weatherCityId, endFcstDay);
        }

        if (fcstDayWeatherType != null) {
            //实施页面逻辑 自动预测不走这块
            List<WeatherFeatureCityDayHisDO> hisVOS = (List<WeatherFeatureCityDayHisDO>) datas
                    .get(AlgorithmConstants.WEATHER_FEATURE_HIS);
            if (CollectionUtils.isEmpty(hisVOS)) {
                return;
            }
            Map<Date, WeatherFeatureCityDayHisDO> dateWeatherMap = hisVOS.stream()
                    .collect(Collectors.toMap(e -> e.getDate(), e -> e, (oldv, curv) -> curv));
            //待预测日之前的气象使用历史气象特性，缺的日期使用预测补充
            dateWeatherMap.remove(endFcstDay);

            if (fcstDayWeatherType == SystemConstant.FCST_DAY_WEATHER_HIS) {
                //待预测日使用 历史气象特性
                List<WeatherFeatureCityDayHisDO> fcstDayWeatherVO = forecastDataService
                        .findWeatherFeatureCityDayHisDO(weatherCityId, endFcstDay, endFcstDay);
                dateWeatherMap
                        .put(endFcstDay, CollectionUtils.isEmpty(fcstDayWeatherVO) ? null : fcstDayWeatherVO.get(0));
            } else {
                //待预测日使用 预测气象特性
                List<WeatherFeatureCityDayFcDO> weatherCityFcVOS = forecastDataService
                        .findWeatherFeatureCityDayFcDO(weatherCityId, endFcstDay, endFcstDay);
                if (CollectionUtils.isEmpty(weatherCityFcVOS)) {
                    //待预测日使用 历史气象特性
                    List<WeatherFeatureCityDayHisDO> fcstDayWeatherVO = forecastDataService
                            .findWeatherFeatureCityDayHisDO(weatherCityId, endFcstDay, endFcstDay);
                    dateWeatherMap
                            .put(endFcstDay, CollectionUtils.isEmpty(fcstDayWeatherVO) ? null : fcstDayWeatherVO.get(0));
                } else {
                    WeatherFeatureCityDayHisDO hisVO = new WeatherFeatureCityDayHisDO();
                    BeanUtils.copyProperties(weatherCityFcVOS.get(0), hisVO);
                    dateWeatherMap.put(endFcstDay, hisVO);
                }
            }
            List<WeatherFeatureCityDayHisDO> resultWeatherFeature = new ArrayList<>();
            for (Date dateKey : dateWeatherMap.keySet()) {
                resultWeatherFeature.add(dateWeatherMap.get(dateKey));
            }
            resultWeatherFeature.remove(null);
            resultWeatherFeature = resultWeatherFeature.stream()
                    .sorted(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate)).collect(Collectors.toList());
            datas.put(AlgorithmConstants.WEATHER_FEATURE_HIS, resultWeatherFeature);
        }
    }


    public void countFcEndDateWeatherFeature(Map<String, Object> datas, String cityId, Date endFcstDay) throws Exception {
        List<WeatherFeatureCityDayHisDO> hisVOS = (List<WeatherFeatureCityDayHisDO>) datas
                .get(AlgorithmConstants.WEATHER_FEATURE_HIS);
        Map<java.sql.Date, List<WeatherFeatureCityDayHisDO>> his = hisVOS.stream()
                .collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endDateStr = format.format(endFcstDay);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = his
                .get(new java.sql.Date(endFcstDay.getTime()));
        WeatherFeatureCityDayHisDO fcDayFeature = null;
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisVOS)) {
            fcDayFeature = weatherFeatureCityDayHisVOS.get(0);
        }
        if (fcDayFeature == null) {
            fcDayFeature = new WeatherFeatureCityDayHisDO();
            fcDayFeature.setDate(new java.sql.Date(endFcstDay.getTime()));
            fcDayFeature.setCityId(cityId);
            hisVOS.add(fcDayFeature);
        }
        List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = weatherCityFcModifyService
                .findModifyWeatherByDateAndType(endDateStr, null, cityId);
        if (CollectionUtils.isNotEmpty(modifyWeatherByDateAndType)) {
            Map<Integer, List<WeatherCityFcModifyDO>> collect = modifyWeatherByDateAndType.stream()
                    .collect(Collectors.groupingBy(WeatherCityFcModifyDO::getType));
            for (Map.Entry<Integer, List<WeatherCityFcModifyDO>> entry : collect.entrySet()) {
                Integer key = entry.getKey();
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    WeatherCityFcModifyDO value = entry.getValue().get(0);
                    List<BigDecimal> bigDecimals = BasePeriodUtils
                            .toList(value, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                    Map<String, BigDecimal> maxMinAvg = BasePeriodUtils
                            .getMaxMinAvg(bigDecimals.stream().filter(t -> t != null).collect(Collectors.toList()), 2);
                    BigDecimal count = BigDecimalUtils.addAllValue(bigDecimals);
                    if (WeatherEnum.WINDSPEED.getType().equals(key)) {
                        fcDayFeature.setMaxWinds(maxMinAvg.get("max"));
                        fcDayFeature.setAveWinds(maxMinAvg.get("avg"));
                        fcDayFeature.setMinWinds(maxMinAvg.get("min"));
                    }

                    if (WeatherEnum.RAINFALL.getType().equals(key)) {
                        fcDayFeature.setRainfall(count);
                    }

                    if (WeatherEnum.HUMIDITY.getType().equals(key)) {
                        fcDayFeature.setHighestHumidity(maxMinAvg.get("max"));
                        fcDayFeature.setAveHumidity(maxMinAvg.get("avg"));
                        fcDayFeature.setLowestHumidity(maxMinAvg.get("min"));
                    }

                    if (WeatherEnum.TEMPERATURE.getType().equals(key)) {
                        fcDayFeature.setHighestTemperature(maxMinAvg.get("max"));
                        fcDayFeature.setAveTemperature(maxMinAvg.get("avg"));
                        fcDayFeature.setLowestTemperature(maxMinAvg.get("min"));
                    }
                }
            }
        }
    }

    /**
     * 查询历史负荷数据
     */
    private void mergeHisLoad(Map<String, Object> datas, String cityId, String caliberId, Date lastYearStartDate,
                              Date endFcstDay) throws Exception {
        List<LoadCityHisDO> loadCityHisDOS = forecastDataService.findHisLoad(cityId, caliberId,
                lastYearStartDate, endFcstDay);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(loadCityHisDOS)) {
            log.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("城市id:" + cityId + "口径id:" + caliberId + ",算法执行失败，历史负荷数据为空");
        }
        datas.put(AlgorithmConstants.HIS_LOAD, loadCityHisDOS);
    }

    /**
     * 匹配历史气象数据 如果要针对不同算法使用不同的气象，需要传入的algorithmEnums的size为1，否则写死的weatherCityId会影响algorithmEnums列表中的所有算法，请校验需求是否是这个意思
     */
    private void mergeWeatherData(String cityId, Map<String, Object> datas, Date lastYearStartDate, Date endFcstDay,
                                  Boolean weatherModify, Integer fcstDayWeatherType, String caliberId, ForecastParam mergeDataParam)
            throws Exception {
        //如果是预测省 则使用省会城市的气象(人为将省会城市排序为2)
        List<WeatherCityHisDO> weatherCityHisVOS = null;
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<AlgorithmEnum> algorithmEnums = mergeDataParam.getAlgorithmEnums();
        //全口径的的省预测 也是用省会的气象；现在 全口径的省就是 全省用电有功
        if (caliberId.equals("1") && cityId.equals("1")) {
            log.error("口径为1 城市为1故设置气象城市为1");
            weatherCityId = "1";
        }
        //神经网络优化算法使用福州气象；
        if (caliberId.equals("1") && cityId.equals("1") && algorithmEnums.size() ==
            1 && mergeDataParam.getAlgorithmEnums().get(0).equals(AlgorithmEnum.FORECAST_INNOVATION_CHECK)) {
            weatherCityId = "2";
        }
        int weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather");
        if (type != null) {
            weatherType = Integer.parseInt(type);
        }
        //系统设置的使用历史气象数据
        if (weatherType == 0) {
            weatherCityHisVOS = forecastDataService.findHisWeather(weatherCityId, null, lastYearStartDate, endFcstDay);
            //查看历史数据截止日期
            if (CollectionUtils.isNotEmpty(weatherCityHisVOS)) {
                mergeSynthesizeHisWeather(weatherCityId, datas, endFcstDay, weatherCityHisVOS);
            }
        }
        //系统设置的使用预测气象数据
        else {
            mergeSynthesizeFcWeather(weatherCityId, datas, lastYearStartDate, endFcstDay);
        }

        if (weatherModify) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String endDateStr = format.format(endFcstDay);

            List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = weatherCityFcModifyService
                    .findModifyWeatherByDateAndType(endDateStr, null, weatherCityId);
            if (CollectionUtils.isNotEmpty(modifyWeatherByDateAndType)) {
                Map<Integer, List<WeatherCityFcModifyDO>> collect = modifyWeatherByDateAndType.stream()
                        .collect(Collectors.groupingBy(WeatherCityFcModifyDO::getType));

                for (Map.Entry<Integer, List<WeatherCityFcModifyDO>> entry : collect.entrySet()) {
                    Integer key = entry.getKey();
                    List<WeatherCityFcModifyDO> value = entry.getValue();
                    if (!CollectionUtils.isEmpty(value)) {
                        WeatherCityFcModifyDO modifyVO = value.get(0);
                        WeatherCityHisDO his = new WeatherCityHisDO();
                        BeanUtils.copyProperties(modifyVO, his);
                        List<WeatherCityHisDO> hisWeather = (List<WeatherCityHisDO>) datas
                                .get(AlgorithmConstants.DATA_WEATHER_KEY);
                        for (int i = 0; i < hisWeather.size(); i++) {
                            WeatherCityHisDO weatherCityHisVO = hisWeather.get(i);
                            if (weatherCityHisVO.getDate().compareTo(modifyVO.getDate()) == 0 && key
                                    .equals(weatherCityHisVO.getType())) {
                                hisWeather.remove(i);
                                hisWeather.add(his);
                            }
                        }
                    }
                }
            }
        }
        if (fcstDayWeatherType != null) {
            //实施页面逻辑 自动预测不走这块
            List<WeatherCityHisDO> hisWeather = (List<WeatherCityHisDO>) datas.get(AlgorithmConstants.DATA_WEATHER_KEY);
            if (CollectionUtils.isEmpty(hisWeather)) {
                return;
            }
            Map<Date, List<WeatherCityHisDO>> dateWeatherMap = hisWeather.stream()
                    .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            //待预测日之前的气象使用历史气象，缺的日期使用预测补充
            dateWeatherMap.remove(endFcstDay);
            Set<Date> dateSet = dateWeatherMap.keySet();
            Date maxDate = Collections.max(dateSet);
            List<WeatherCityFcDO> supplementWeatherCityFcDOS = forecastDataService
                    .findFcWeather(weatherCityId, DateUtil.getMoveDay(maxDate, 1), DateUtil.getMoveDay(endFcstDay, -1));
            if (!CollectionUtils.isEmpty(supplementWeatherCityFcDOS)) {
                Map<Date, List<WeatherCityFcDO>> map = supplementWeatherCityFcDOS.stream()
                        .collect(Collectors.groupingBy(WeatherCityFcDO::getDate));
                for (Date date : map.keySet()) {
                    List<WeatherCityHisDO> supplementWeatherCityHisDOS = new ArrayList<>();
                    map.get(date).forEach(e -> {
                        WeatherCityHisDO hisVO = new WeatherCityHisDO();
                        BeanUtils.copyProperties(e, hisVO);
                        supplementWeatherCityHisDOS.add(hisVO);
                    });
                    dateWeatherMap.put(date, supplementWeatherCityHisDOS);
                }
            }
            if (fcstDayWeatherType == SystemConstant.FCST_DAY_WEATHER_HIS) {
                //待预测日使用 历史气象
                List<WeatherCityHisDO> fcstDayWeatherVO = forecastDataService
                        .findHisWeather(weatherCityId, null, endFcstDay, endFcstDay);
                dateWeatherMap.put(endFcstDay, CollectionUtils.isEmpty(fcstDayWeatherVO) ? null : fcstDayWeatherVO);
            } else {
                //待预测日使用 预测气象
                List<WeatherCityFcDO> weatherCityFcVOS = forecastDataService
                        .findFcWeather(weatherCityId, endFcstDay, endFcstDay);
                if (CollectionUtils.isEmpty(weatherCityFcVOS)) {
                    dateWeatherMap.put(endFcstDay, null);
                } else {
                    List<WeatherCityHisDO> weatherCityHisVOList = new ArrayList<>();
                    weatherCityFcVOS.forEach(e -> {
                        WeatherCityHisDO hisVO = new WeatherCityHisDO();
                        BeanUtils.copyProperties(e, hisVO);
                        weatherCityHisVOList.add(hisVO);
                    });
                    dateWeatherMap.put(endFcstDay, weatherCityHisVOList);
                }
            }
            List<WeatherCityHisDO> resultWeather = new ArrayList<>();
            for (Date dateKey : dateWeatherMap.keySet()) {
                if (dateWeatherMap.get(dateKey) == null) {
                    continue;
                }
                resultWeather.addAll(dateWeatherMap.get(dateKey));
            }
            resultWeather.remove(null);
            resultWeather = resultWeather.stream().sorted(Comparator.comparing(WeatherCityHisDO::getDate))
                    .collect(Collectors.toList());
            datas.put(AlgorithmConstants.DATA_WEATHER_KEY, resultWeather);
        }
    }

    private void mergeSynthesizeHisWeather(String cityId, Map<String, Object> datas, Date endFcstDay,
                                           List<WeatherCityHisDO> WeatherCityHisDOS) throws Exception {
        //最后一天的气象
        WeatherCityHisDO WeatherCityHisDO = WeatherCityHisDOS.get(WeatherCityHisDOS.size() - 1);
        Date endHisDate = WeatherCityHisDO.getDate();
        List<BigDecimal> list = BasePeriodUtils.toList(WeatherCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        //去除null数据
        list.removeAll(Collections.singleton(null));
        //若查询历史气象数据的最后的日期在预测结束日之前 说明历史数据缺少  则在预测数据里查找
        if (list.size() < 90 || endHisDate.before(endFcstDay)) {
            List<WeatherCityFcDO> fcVOS;
            //如果数据不够 则需要把这一天的数据先剔除掉
            if (list.size() < 90) {
                List<WeatherCityHisDO> hisVOS = forecastDataService.findHisWeather(cityId, null,
                        endHisDate, endFcstDay);
                WeatherCityHisDOS.removeAll(hisVOS);
                fcVOS = forecastDataService.findFcWeather(cityId, endHisDate, endFcstDay);
            } else {
                //如果历史数据的最后一日在预测日之前或者和预测日相等
                fcVOS = forecastDataService.findFcWeather(cityId, DateUtils.addDays(endHisDate, 1),
                        endFcstDay);
            }
            //有预测数据
            for (WeatherCityFcDO vo : fcVOS) {
                //将预测的数据放入历史数据里
                WeatherCityHisDO hisVO = new WeatherCityHisDO();
                hisVO.setDate(vo.getDate());
                hisVO.setType(vo.getType());
                hisVO.setCityId(vo.getCityId());
                List<BigDecimal> period = BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                WeatherCityHisDOS.add(hisVO);
            }
        }
        datas.put(AlgorithmConstants.DATA_WEATHER_KEY, WeatherCityHisDOS);
    }

    private void mergeSynthesizeFcWeather(String cityId, Map<String, Object> datas, Date lastYearStartDate,
                                          Date endFcstDay) throws Exception {
        List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcDAO.findWeatherCityFcDO(cityId, lastYearStartDate, endFcstDay);
        weatherCityFcDOS = forecastDataService.fillMissingFcDataWithDiankeyuan(Collections.singletonList(cityId), lastYearStartDate, endFcstDay, weatherCityFcDOS);
        List<WeatherCityHisDO> hisVOS = new ArrayList<>();
        for (WeatherCityFcDO fcVO : weatherCityFcDOS) {
            WeatherCityHisDO hisVO = new WeatherCityHisDO();
            BeanUtils.copyProperties(fcVO, hisVO);
            hisVOS.add(hisVO);
        }
        datas.put(AlgorithmConstants.DATA_WEATHER_KEY, hisVOS);
    }


    /**
     * 默认的预测后入库逻辑，即存入fc表中，考虑自动上报，推荐算法等因素
     *
     * @param tarPath        算法生成文件路径，不带文件名称
     * @param supplementData 补充参数；个别算法用于甄别file out。例如超短期的区分5or15分钟；稳定度分析的week信息等等
     */
    @Override
    public void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception {
        GeneralResult result = ALgorithmSupport.postGeneraResult(tarPath, null);
        List<LoadCityFcDO> forecastLoadVOS = new ArrayList<>();
        String cityId = supplementData.get(AlgorithmConstants.CITY_ID);
        String caliberId = supplementData.get(AlgorithmConstants.CALIBER_ID);
        String algorithmId = supplementData.get(AlgorithmConstants.ALGORITHM_ID);
        String recall = supplementData.get(Constants.IS_RECALL);
        //程序外部循环一天天调用时，返回值要放入redis
        if (!ForecastParam.FORECAST_CYCLIC_TYPE.equals(Integer.valueOf(supplementData.get(AlgorithmConstants.FORECAST_CYCLIC_TYPE)))) {
            redisService.redisAdd(CacheConstants.CACHE_FORECAST_KEY, result);

        }
        String[] normalAlgorithmId = systemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue()
                .split(Constants.SEPARATOR_PUNCTUATION);
        String[] autoReportData = systemService.findByFieldId(SystemConstant.AUTO_REPORT).getValue()
                .split(Constants.SEPARATOR_PUNCTUATION);
        //默认的系统算法id为相似日
        String systemAlgorithmId;
        //自动上报
        boolean autoReport;
        //正常日系统设置的算法id
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            systemAlgorithmId = normalAlgorithmId[0];
            autoReport = autoReportData[0].equals(ParamConstants.STRING_COMPOSITE_ON);
        } else {
            systemAlgorithmId = normalAlgorithmId[1];
            autoReport = autoReportData[1].equals(ParamConstants.STRING_COMPOSITE_ON);
        }

        List<Date> holidays = holidayDAO.getAllHolidays();
        for (LoadCityFcDO metaData : result.getResult()) {
            try {
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                        caliberId,
                        algorithmId);
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO);
                forecastLoadVOS.add(loadCityFcDO);
                if (Constants.IS_RECALL.equals(recall)) {
                    insertOrUpdateRecallData(cityId, caliberId, algorithmId, metaData, holidays, systemAlgorithmId, autoReport);
                } else {
                    insertOrUpdateData(cityId, caliberId, algorithmId, metaData, holidays, systemAlgorithmId, autoReport);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
        }
    }

    @SneakyThrows
    private void insertOrUpdateRecallData(String cityId, String caliberId, String algorithmId, LoadCityFcDO metaData,
                                          List<Date> holidays,
                                          String systemAlgorithmId, boolean autoReport) {
        //如果预测的日期是节假日，则需要将默认上报的是节假日算法，所以这里让所有正常日预测出的结果都不上报
        if (holidays.contains(metaData.getDate())) {
            systemAlgorithmId = AlgorithmEnum.HOLIDAY_FEST_SVM.getType();
        }
        LoadCityFcDO report = loadCityFcService.getReport(cityId, Constants.CALIBER_QUAN, metaData.getDate());
        LoadCityFcRecallDO entity = new LoadCityFcRecallDO();
        entity.setId(null);
        entity.setAlgorithmId(algorithmId);
        entity.setCityId(cityId);
        entity.setCaliberId(caliberId);
        entity.setDate(new java.sql.Date(metaData.getDate().getTime()));
        entity.setRecommend(false);
        entity.setReport(false);
        entity.setSucceed(false);
        if (algorithmId.equals(systemAlgorithmId)) {
            entity.setRecommend(true);
            // 如果没有上报记录
            if (autoReport && report == null) {
                entity.setReport(true);
                entity.setReportTime(new Timestamp(System.currentTimeMillis()));
                entity.setSucceed(true);
            }
        }
        entity.setCreatetime(new Timestamp(System.currentTimeMillis()));
        entity.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        com.tsintergy.lf.core.util.ColumnUtil.copyPropertiesIgnoreNull(metaData, entity);
        loadCityFcRecallService.doSaveOrUpdateLoadCityFcDO96(entity);
    }

    private void insertOrUpdateData(String cityId, String caliberId, String algorithmId, LoadCityFcDO metaData,
                                    List<Date> holidays,
                                    String systemAlgorithmId, boolean autoReport) throws Exception {
        List<LoadCityFcDO> loadCityFcVOS = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId, algorithmId, metaData.getDate(), metaData.getDate());
        //如果预测的日期是节假日，则需要将默认上报的是节假日算法，所以这里让所有正常日预测出的结果都不上报
        if (holidays.contains(metaData.getDate())) {
            systemAlgorithmId = AlgorithmEnum.HOLIDAY_FEST_SVM.getType();
        }
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, metaData.getDate());
        if (loadCityFcVOS.size() < 1) {
            //如果数据库中没有此算法id的数据  则创建，
            LoadCityFcDO loadCityFcVO1 = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                    caliberId, algorithmId);
            //如果和系统推荐算法id一致
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcVO1.setSucceed(true);
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            loadCityFcDAO.create(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        } else {
            //如果数据库中有数据的话 直接做update处理
            LoadCityFcDO loadCityFcVO1 = loadCityFcVOS.get(0);
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null || report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else if (autoReport && report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDAO.update(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        }
    }


}


