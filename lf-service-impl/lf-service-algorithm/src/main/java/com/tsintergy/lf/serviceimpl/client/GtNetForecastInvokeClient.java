package com.tsintergy.lf.serviceimpl.client;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.gtNet.GtNetForecastBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.gtNet.GtNetForecastParam;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.NotIncludedDate;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractGtNetForecastInvokeClient;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.GtNetForecastAlgorithmParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.NotIncludedDateDTO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

public class GtNetForecastInvokeClient extends AbstractGtNetForecastInvokeClient<GtNetForecastParam> {

    private static final int LOAD_DATA_SIZE_THRESHOLD = 90;

    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    WeatherCityHisService weatherCityHisService;
    @Autowired
    WeatherCityFcService weatherCityFcService;
    @Autowired
    CityService cityService;
    @Autowired
    ForecastDataService forecastDataService;

    @Override
    @SneakyThrows
    protected GtNetForecastBasicData generateBasicData(GtNetForecastParam param) {
        GtNetForecastBasicData result = new GtNetForecastBasicData();

        Date beginDate = param.getBeginDate();
        Date endDate = param.getEndDate();
        Date hisStartDate = DateUtils.string2Date("2018-01-01", DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date hisEndDate = DateUtils.addDays(beginDate, -1);

        //1.查询历史负荷
        List<LoadCityHisDO> hisProvinceList = loadCityHisService.findLoadCityHisDOS(null, hisStartDate, endDate, "1")
                .stream().filter(x -> Constants.PROVINCE_ID.equals(x.getCityId()))
                .collect(Collectors.toList());
        result.setLoadHisList(hisProvinceList);

        //2.查询九地市温度数据
        List<String> cityIds = cityService.findCitysByBelongId(Constants.PROVINCE_ID)
                .stream().map(CityDO::getId)
                .collect(Collectors.toList());

        Boolean recall = null;
        if (param instanceof GtNetForecastAlgorithmParam) {
            recall = ((GtNetForecastAlgorithmParam) param).getRecall();
        }
        List<Load> temperatureList = new ArrayList<>();
        if (BooleanUtils.isTrue(recall)) {
            List<WeatherCityHisDO> hisWeatherCityHisDOList = weatherCityHisService.getWeatherCityHisDOs(cityIds, null, hisStartDate, endDate);
            List<WeatherCityHisDO> hisTemperatureList = hisWeatherCityHisDOList.stream()
                    .filter(x -> WeatherEnum.TEMPERATURE.getType().equals(x.getType()))
                    .collect(Collectors.toList());
            temperatureList.addAll(hisTemperatureList);
        } else {
            List<WeatherCityHisDO> weatherCityHisDOList = weatherCityHisService.getWeatherCityHisDOs(cityIds, null, hisStartDate, hisEndDate);
            //最后一天的历史数据
            List<WeatherCityHisDO> lastWeatherCityHisDOList = weatherCityHisDOList.stream()
                    .filter(x -> x.getDate().compareTo(hisEndDate) == 0)
                    .collect(Collectors.toList());
            //倒数第二天之前的历史数据
            List<WeatherCityHisDO> hisWeatherCityHisDOList = weatherCityHisDOList.stream()
                    .filter(x -> x.getDate().compareTo(hisEndDate) < 0)
                    .collect(Collectors.toList());
            //如果历史数据最后一天的数据小于90个点，则用预测数据代替，用预测气象补全最后一天到预测日的气象
            Date fcStartDate;
            if (this.isLoadDataSizeBelowThreshold(lastWeatherCityHisDOList)) {
                fcStartDate = hisEndDate;
            } else {
                hisWeatherCityHisDOList.addAll(lastWeatherCityHisDOList);
                fcStartDate = beginDate;
            }
            List<WeatherCityFcDO> fcTemperatureList = weatherCityFcService.findWeatherCityHisDOs(cityIds, WeatherEnum.TEMPERATURE.getType(), fcStartDate, endDate);
            fcTemperatureList = forecastDataService.fillMissingFcDataWithDiankeyuan(cityIds, fcStartDate, endDate, fcTemperatureList)
                    .stream().filter(x -> WeatherEnum.TEMPERATURE.getType().equals(x.getType()))
                    .collect(Collectors.toList());
            List<WeatherCityHisDO> hisTemperatureList = hisWeatherCityHisDOList.stream()
                    .filter(x -> WeatherEnum.TEMPERATURE.getType().equals(x.getType()))
                    .collect(Collectors.toList());
            temperatureList.addAll(hisTemperatureList);
            temperatureList.addAll(fcTemperatureList);
        }
        result.setTemperatureList(temperatureList);

        //3.查询节假日
        List<HolidayDO> holidayDOList = forecastDataService.findAllHolidays();
        holidayDOList.sort(Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        result.setHolidays(holidayDOList);

        //4.排除掉的日期
        List<NotIncludedDate> notIncludedDateList = new ArrayList<>();
        NotIncludedDateDTO notIncludedDate = new NotIncludedDateDTO();
        notIncludedDate.setDateTime(DateUtil.getDateFromString("20210101", DateUtil.DATE_FORMAT4));
        notIncludedDate.setCauses("疫情");
        notIncludedDateList.add(notIncludedDate);
        result.setNotIncludedDateList(notIncludedDateList);
        return result;
    }

    private boolean isLoadDataSizeBelowThreshold(List<WeatherCityHisDO> lastWeatherCityHisDOList) {
        if (CollectionUtils.isEmpty(lastWeatherCityHisDOList)) {
            return true;
        }
        long loadDataSize = lastWeatherCityHisDOList.get(0).getloadList().
                stream().filter(Objects::nonNull)
                .count();
        return loadDataSize < LOAD_DATA_SIZE_THRESHOLD;
    }
}
