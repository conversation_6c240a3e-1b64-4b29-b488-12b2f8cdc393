/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wang<PERSON>@tsintergy.com Date:  2021/12/8 2:11 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.callback;

import com.tsintergy.algorithm.alginv.core.configure.properties.AlginvProperties;
import com.tsintergy.algorithm.alginv.core.invoker.parameters.AlgorithmInvokeId;
import com.tsintergy.algorithm.alginv.core.invoker.parameters.AlgorithmInvokeResult;
import com.tsintergy.algorithm.alginv.serviceimpl.client.invoker.ClientAlgorithmCompleteCallback;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceimpl.factory.ForecastMethodFactory;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:  <br> 算法完成后的回调函数
 *
 * <AUTHOR>
 * @create 2022/3/29
 * @since 1.0.0
 */
@Component
@Slf4j
public class AlgorithmCallBack implements ClientAlgorithmCompleteCallback {

    @Autowired
    private AlginvProperties alginvProperties;

    @SneakyThrows
    @Override
    @SuppressWarnings(value = "all")
    public void onComplete(AlgorithmInvokeId invokeId, AlgorithmInvokeResult result) {
        AlgorithmEnum byType = AlgorithmEnum.findByType(result.getAlgorithmType());
        String tarFilePath = result.getCaseOutput().getTarFile().pathResolver()
            .getTarFilePath(alginvProperties.getCaseBaseDir());
        String tarPath = tarFilePath.replace(AlgorithmConstants.ZIP_SUFFIX, "");
        Map<String, String> parameters = result.getParameters();
        ForecastMethodFactory.createService(byType).resolverResult(tarPath, result.getParameters());
    }
}