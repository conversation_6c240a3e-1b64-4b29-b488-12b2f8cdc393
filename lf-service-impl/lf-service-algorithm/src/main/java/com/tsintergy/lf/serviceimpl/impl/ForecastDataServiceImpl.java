/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/5 9:27 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.alibaba.fastjson.JSON;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.CityStationEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.DataCheckInfoDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisClctDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonArchiveDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.base.dao.CityDAO;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import com.tsintergy.lf.serviceimpl.datamanage.dao.DataCheckInfoDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisClctDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.typhoon.dao.TyphoonArchiveDao;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.weather.impl.WeatherFeatureStatServiceImpl;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.MATH_CONTEXT;
/**
 * 算法模块的基础数据查询接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service("forecastDataService")
public class ForecastDataServiceImpl extends BaseServiceImpl implements ForecastDataService {

    //网格标准站点数据起始日期
    public static final String DATA_SWITCH_DATE_STR = "2023-12-01";

    @Autowired
    private LoadCityHisDAO loadCityHisDAO;

    @Autowired
    private WeatherFeatureCityDayFcDAO weatherFeatureCityDayFcDAO;

    @Autowired
    private LoadCityHisClctDAO loadCityHisClctDAO;

    @Autowired
    private WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    private CityDAO cityDAO;

    @Autowired
    private DataCheckInfoDAO dataCheckInfodDAO;

    @Autowired
    private TyphoonArchiveDao typhoonArchiveDAO;

    @Autowired
    private DataCheckInfoDAO dataCheckInfoDAO;

    @Autowired
    private WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    private HolidayDAO holidayDAO;

    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    private WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Autowired
    private LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;

    @Autowired
    private WeatherCityBasedStationFcService weatherCityBasedStationFcService;

    @Autowired
    private WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    @Autowired
    private WeatherFeatureStatServiceImpl weatherFeatureStatService;

    @Autowired
    private WeatherStationFcBasicWgService weatherStationFcBasicWgService;


    @Override
    public List<LoadCityHisDO> findHisLoad(String cityId, String caliberId, Date start, Date end) throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, start, end, caliberId);
    }

    @Override
    public List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisDOs(String cityId, Date startDate, Date endDate,
                                                                      String caliberId) throws Exception {
        return loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityId, startDate, endDate, caliberId);
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeature(String cityId, Date startDate, Date endDate,
                                                               List<SearchDTO> screenData) throws Exception {
        return weatherFeatureCityDayHisDAO
                .findWeatherFeature(cityId, startDate, endDate, screenData);
    }

    @Override
    public List<LoadCityHis288DO> findHis288Load(String cityId, String caliberId, Date start, Date end)
            throws Exception {
        return loadCityHis288DAO.getLoadCityHisDO(cityId, start, end, caliberId);
    }

    @Override
    public List<LoadCityHisClctDO> findHisLoadClct(String cityId, String caliberId, Date startDate, Date endDate)
            throws Exception {
        return loadCityHisClctDAO.getLoadCityHisClctVO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public List<WeatherCityHisDO> findHisWeather(String cityId, Integer type, Date start, Date end) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        Date dataSwitchDate = DateUtil.getDateFromString(DATA_SWITCH_DATE_STR, DateUtil.DATE_FORMAT2);
        // 判断查询的时间段是否跨越了数据源切换日期
        boolean crossesDataSwitchDate = !start.after(dataSwitchDate) && !end.before(dataSwitchDate);
        if (crossesDataSwitchDate || start.before(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之前，则从旧的数据源获取数据
            Date oldEndDate = crossesDataSwitchDate ? DateUtils.addDays(dataSwitchDate, -1) : end;
            List<WeatherCityHisDO> oldData = weatherCityHisDAO.findWeatherCityHisDO(cityId, type, start, oldEndDate);
            result.addAll(oldData);
        }
        if (crossesDataSwitchDate || end.after(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之后，则从新的数据源获取数据
            Date newStartDate = crossesDataSwitchDate ? dataSwitchDate : start;
            List<WeatherStationHisBasicWgDO> hisWeatherBasic = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(cityId, type, newStartDate, end);
            for (WeatherStationHisBasicWgDO stationHisBasicWgDO : hisWeatherBasic) {
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                BeanUtils.copyProperties(stationHisBasicWgDO, weatherCityHisDO);
                weatherCityHisDO.setCityId(cityId);
                result.add(weatherCityHisDO);
            }
        }
        result.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        return result;
    }

    @Override
    public List<StatisticsSynthesizeWeatherCityDayHisDO> findSynthesizeHisWeather(String cityId, Date start, Date end,
        Integer weatherType)
        throws Exception {
        return statisticsSynthesizeWeatherCityDayHisService
            .findStatisticsSynthesizeWeatherCityDayHisDO(cityId,
                weatherType, start, end);
    }

    @Override
    public List<StatisticsSynthesizeWeatherCityDayFcDO> findSynthesizeFcWeather(String cityId, Date start, Date end,
        Integer weatherType)
        throws Exception {
        return statisticsSynthesizeWeatherCityDayFcService
            .findStatisticsSynthesizeWeatherCityDayFcDO(cityId, weatherType, start,
                end);
    }


    @SneakyThrows
    @Override
    public List<WeatherCityFcDO> findFcWeather(String cityId, Date start, Date end) {
        List<WeatherCityFcDO> result = new ArrayList<>();
        List<WeatherStationFcBasicWgDO> fcWeatherBasic = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(cityId, null, start, end);
        for (WeatherStationFcBasicWgDO stationFcBasicWgDO : fcWeatherBasic) {
            WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
            BeanUtils.copyProperties(stationFcBasicWgDO, weatherCityFcDO);
            //58847站点同时对应cityId为1和2，需要特殊处理
            if (Constants.PROVINCE_ID.equals(cityId)) {
                weatherCityFcDO.setCityId(Constants.PROVINCE_ID);
            } else {
                weatherCityFcDO.setCityId(getCityIdByStationId(stationFcBasicWgDO.getStationWgId()));
            }
            result.add(weatherCityFcDO);
        }
        result = this.fillMissingFcDataWithDiankeyuan(Collections.singletonList(cityId), start, end, result);
        return result;
    }


    @Override
    public List<WeatherCityFcDO> fillMissingFcDataWithDiankeyuan(List<String> cityIds, Date start, Date end, List<WeatherCityFcDO> existFcDOs) throws Exception {
        if (CollectionUtils.isEmpty(existFcDOs)) {
            return existFcDOs;
        }

        List<WeatherCityFcDO> results = new ArrayList<>();

        //排序取最后一天的日期
        existFcDOs.sort(new Comparator<WeatherCityFcDO>() {
            @Override
            public int compare(WeatherCityFcDO o1, WeatherCityFcDO o2) {
                return o1.getDate().compareTo(o2.getDate());
            }
        });
        Date lastDay = existFcDOs.get(existFcDOs.size() - 1).getDate();

        //最后一天的预测数据
        List<WeatherCityFcDO> lastDayFcData = existFcDOs.stream().filter(x -> x.getDate().compareTo(lastDay) == 0).collect(Collectors.toList());

        //倒数第二天之前的预测数据
        List<WeatherCityFcDO> cityFcDOS = existFcDOs.stream().filter(x -> x.getDate().compareTo(lastDay) < 0).collect(Collectors.toList());
        results.addAll(cityFcDOS);

        List<WeatherCityBasedStationFcDO> diankeyuanFcWeather = weatherCityBasedStationFcService.findByCondition(cityIds, lastDay, lastDay);
        if (CollectionUtils.isEmpty(diankeyuanFcWeather)) {
            return existFcDOs;
        }

        Map<String, WeatherCityFcDO> lastDayWeatherMap = lastDayFcData.stream()
                .collect(Collectors.toMap(src -> src.getCityId() + "-" + src.getType(), Function
                        .identity(), (key1, key2) -> key2));

        Map<String, List<BigDecimal>> fcWeatherMap = diankeyuanFcWeather.stream()
                .collect(Collectors.toMap(src -> src.getCityId() + "-" + src.getType(), WeatherCityBasedStationFcDO::getloadList));

        //使用电科院预测气象补最后一天气象局预测气象的缺点
        lastDayWeatherMap.forEach((type, data) -> {
            List<BigDecimal> value = data.getloadList();
            List<BigDecimal> fcWeatherList = fcWeatherMap.get(type);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fcWeatherList)) {
                for (int l = 0; l < value.size(); l++) {
                    if (Objects.isNull(value.get(l))) {
                        value.set(l, fcWeatherList.get(l));
                    }
                }
            }
            WeatherCityFcDO lastDayDo = new WeatherCityFcDO();
            lastDayDo.setType(data.getType());
            lastDayDo.setCityId(data.getCityId());
            lastDayDo.setCreatetime(data.getCreatetime());
            lastDayDo.setUpdatetime(data.getUpdatetime());
            lastDayDo.setDate(new java.sql.Date(lastDay.getTime()));
            try {
                BasePeriodUtils.setAllFiled(lastDayDo, ColumnUtil.listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            results.add(lastDayDo);
        });

        //剩下的数据用电科院预测数据补充
        Date date = DateUtils.addDays(lastDay, 1);
        if (date.after(end)) {
            return results;
        }
        List<WeatherCityBasedStationFcDO> diankeyuanFcWeathers = weatherCityBasedStationFcService.findByCondition(cityIds, date, end);
        if (CollectionUtils.isNotEmpty(diankeyuanFcWeathers)) {
            for (WeatherCityBasedStationFcDO fcWeather : diankeyuanFcWeathers) {
                WeatherCityFcDO fcVO = new WeatherCityFcDO();
                BeanUtils.copyProperties(fcWeather, fcVO);
                results.add(fcVO);
            }
        }
        return results;
    }

    @Override
    public LoadCityFcDO doCreate(LoadCityFcDO vo) throws Exception {
        return loadCityFcDAO.save(vo);
    }

    @Override
    public LoadCityFcDO findLoadCityFc(String cityId, Date date, String caliberId,
                                       String algorithmId) throws Exception {
        return loadCityFcDAO.getLoadCityFcDO(cityId, caliberId, algorithmId, date);
    }

    @Override
    public void removeLoadFc(LoadCityFcDO vo) throws Exception {
        loadCityFcDAO.deleteById(vo);
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeatureCityDayHisDO(String cityId, Date startDate, Date endDate) throws Exception {
        List<WeatherFeatureCityDayHisDO> result = new ArrayList<>();
        List<WeatherCityHisDO> hisWeather = this.findHisWeather(cityId, null, startDate, endDate);
        List<WeatherFeatureCityDayHisDO> featureCityDayHisDOS = weatherFeatureStatService.statisticsDayFeature(hisWeather);
        result.addAll(featureCityDayHisDOS);
        result.sort(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate));
        return result;
    }

    @Autowired
    CityService cityService;

    @Autowired
    private BaseWeatherStationHisService baseWeatherStationHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private BaseWeatherStationWgService baseWeatherStationWgService;

    @Autowired
    private WeatherStationHisClctWgService weatherStationHisClctWgService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public List<WeatherFeatureCityDayFcDO> findWeatherFeatureCityDayFcDO(String cityId, Date startDate, Date endDate)
            throws Exception {
        return weatherFeatureCityDayFcDAO.getWeatherFeatureCityDayFcDOs(cityId, startDate, endDate);

    }

    @Override
    public void doRemoveDataCheckInfoDO(DataCheckInfoDO checkInfoVo) throws Exception {
        dataCheckInfodDAO.removeAndFlush(checkInfoVo);

    }

    @Override
    public CityDO findCityDOByCityId(String cityId) throws Exception {
        return cityDAO.findVOByPk(cityId);
    }

    @Override
    public List<CityDO> findAllCity() throws Exception {
        return (List<CityDO>) cityDAO.findAll();
    }

    @Override
    public void doCreateDataCheck(DataCheckInfoDO dataCheckInfoVO) throws Exception {
        dataCheckInfoDAO.save(dataCheckInfoVO);
    }


    @Override
    public List<DataCheckInfoDO> findCheckInfoVOS(java.sql.Date startDate, java.sql.Date endDate, String cityId,
        String caliberId) throws Exception {
        DBQueryParam dbQueryParam = DBQueryParamBuilder.create()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.DateNoLessThan, "date", startDate)
            .where(QueryOp.DateNoMoreThan, "date", endDate)
            .build();
        return dataCheckInfoDAO.query(dbQueryParam).getDatas();
    }


    @Override
    public List<HolidayDO> findAllHolidays() throws Exception {
        return (List<HolidayDO>) holidayDAO.findAll();
    }

    @Override
    public List<TyphoonArchiveDO> getListRiskTyphoon(Date startDate, Date endDate) throws Exception {
        return typhoonArchiveDAO.getAllRiskTyphoonArchive(startDate, endDate);
    }

    private String getCityIdByStationId(String stationId) {
        for (Map.Entry<String, String> entry : CityConstants.standardWgStationMap.entrySet()) {
            if (entry.getValue().equals(stationId) && !entry.getKey().equals(CityConstants.PROVINCE_ID)) {
                return entry.getKey();
            }
        }
        return null;
    }

    @Override
    public List<WeatherCityHisDO> mergeHisAndFcWeather(List<WeatherCityHisDO> weatherCityHisDOS) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        WeatherCityHisDO hisDO = weatherCityHisDOS.get(0);
        List<WeatherCityFcDO> weatherCityFcDO = this.getFcWeather(null, hisDO.getDate(), hisDO.getDate());
        Map<String, WeatherCityHisDO> lastDayWeatherMap = weatherCityHisDOS.stream()
                .collect(Collectors.toMap(src -> src.getCityId() + "-" + src.getType(), Function
                        .identity(), (key1, key2) -> key2));
        List<WeatherCityFcDO> lastFcWeather = new ArrayList<>(weatherCityFcDO);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(lastFcWeather)) {
            Map<String, List<BigDecimal>> fcWeatherMap = lastFcWeather.stream()
                    .collect(Collectors.toMap(src -> src.getCityId() + "-" + src.getType(), WeatherCityFcDO::getloadList));
            //清理最后一天实际气象
            lastDayWeatherMap.forEach((type, data) -> {
                List<BigDecimal> value = data.getloadList();
                List<BigDecimal> fcWeatherList = fcWeatherMap.get(type);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fcWeatherList)) {
                    //使用预测气象替换最后一天实际气象的缺点
                    for (int l = 0; l < value.size(); l++) {
                        if (Objects.isNull(value.get(l))) {
                            value.set(l, fcWeatherList.get(l));
                        }
                    }
                }
                WeatherCityHisDO lastDayDo = new WeatherCityHisDO();
                lastDayDo.setType(data.getType());
                lastDayDo.setCityId(data.getCityId());
                lastDayDo.setCreatetime(data.getCreatetime());
                lastDayDo.setUpdatetime(data.getUpdatetime());
                lastDayDo.setDate(new java.sql.Date(hisDO.getDate().getTime()));
                try {
                    BasePeriodUtils
                            .setAllFiled(lastDayDo, ColumnUtil.listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                result.add(lastDayDo);
            });
        }
        return result;
    }

    @Override
    public List<WeatherCityHisDO> getHisWeather(List<String> cityIds, Integer type, Date start, Date end) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        Date dataSwitchDate = DateUtil.getDateFromString(DATA_SWITCH_DATE_STR, DateUtil.DATE_FORMAT2);
        // 判断查询的时间段是否跨越了数据源切换日期
        boolean crossesDataSwitchDate = !start.after(dataSwitchDate) && !end.before(dataSwitchDate);
        if (crossesDataSwitchDate || start.before(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之前，则从旧的数据源获取数据
            Date oldEndDate = crossesDataSwitchDate ? com.tsieframework.core.base.format.datetime.DateUtils.addDays(dataSwitchDate, -1) : end;
            List<WeatherCityHisDO> oldData = weatherCityHisService.getWeatherCityHisDOs(cityIds, type, start, oldEndDate);
            result.addAll(oldData);
        }
        if (crossesDataSwitchDate || end.after(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之后，则从新的数据源获取数据
            Date newStartDate = crossesDataSwitchDate ? dataSwitchDate : start;
            List<WeatherStationHisBasicWgDO> hisWeatherBasic = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(null, type, newStartDate, end);
            for (WeatherStationHisBasicWgDO stationHisBasicWgDO : hisWeatherBasic) {
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                BeanUtils.copyProperties(stationHisBasicWgDO, weatherCityHisDO);
                weatherCityHisDO.setCityId(getCityIdByStationId(stationHisBasicWgDO.getStationWgId()));
                result.add(weatherCityHisDO);
            }
        }
        result.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        return result;
    }

    @Override
    public List<WeatherCityFcDO> getFcWeather(Integer type, Date start, Date end) {
        List<WeatherCityFcDO> result = new ArrayList<>();
        List<WeatherStationFcBasicWgDO> fcWeatherBasic = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(null, type, start, end);
        for (WeatherStationFcBasicWgDO stationFcBasicWgDO : fcWeatherBasic) {
            WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
            BeanUtils.copyProperties(stationFcBasicWgDO, weatherCityFcDO);
            weatherCityFcDO.setCityId(getCityIdByStationId(stationFcBasicWgDO.getStationWgId()));
            result.add(weatherCityFcDO);
        }
        return result;
    }

    @Override
    public List<WeatherCityHisDO> getProvinceWeightAvgHisWeatherData(Integer type, Date start, Date end) throws Exception {
        List<WeatherCityHisDO> fcWeatherDOS = new ArrayList<>();
        String str = settingSystemService.getValue("province_weather_express");
        Map map = (Map) JSON.parse(str);
        List<WeatherStationHisBasicWgDO> weatherStationFcBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(null, type, start, end);
        if (org.springframework.util.CollectionUtils.isEmpty(weatherStationFcBasicWgDOS)) {
            return Collections.emptyList();
        }
        Map<String, List<WeatherStationHisBasicWgDO>> weatherMap = weatherStationFcBasicWgDOS.stream()
                .collect(Collectors.groupingBy(WeatherStationHisBasicWgDO -> WeatherStationHisBasicWgDO.getDate() + "_" + WeatherStationHisBasicWgDO.getType()));
        for (Map.Entry<String, List<WeatherStationHisBasicWgDO>> entry : weatherMap.entrySet()) {
            String[] split = entry.getKey().split("_");
            String date = split[0];
            String weatherType = split[1];
            List<WeatherStationHisBasicWgDO> value = entry.getValue();
            List<List<BigDecimal>> lists = new ArrayList<>();
            // 用于记录每个地市的比率
            List<BigDecimal> ratioList = new ArrayList<>();
            for (WeatherStationHisBasicWgDO weatherStationFcBasicWgDO : value) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherStationFcBasicWgDO, 96, Constants.WEATHER_CURVE_START_WITH_ZERO);
                String ratioStr = map.get(CityStationEnum.getCityIdByStation(weatherStationFcBasicWgDO.getStationWgId())).toString();
                BigDecimal ratio = new BigDecimal(ratioStr);
                ratioList.add(ratio);
                List<BigDecimal> weather96RatioList = zero96List();
                for (int i = 0; i < bigDecimals.size(); i++) {
                    BigDecimal bigDecimal = bigDecimals.get(i);
                    if (com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.isValidValue(bigDecimal)) {
                        BigDecimal scaledValue = bigDecimal.multiply(ratio);
                        weather96RatioList.set(i, scaledValue);
                    }
                }
                lists.add(weather96RatioList);
            }
            List<BigDecimal> decimalList = zero96List();
            for (int i = 0; i < 96; i++) {
                BigDecimal sum = BigDecimal.ZERO;
                BigDecimal totalRatio = BigDecimal.ZERO;
                for (int j = 0; j < lists.size(); j++) {
                    List<BigDecimal> cityLoads = lists.get(j);
                    BigDecimal decimal = cityLoads.get(i);
                    if (decimal != null) {
                        sum = sum.add(decimal);
                        totalRatio = totalRatio.add(ratioList.get(j)); // 使用已知的比率
                    }
                }
                BigDecimal curValue = totalRatio.equals(BigDecimal.ZERO) ? null : sum.divide(totalRatio, MATH_CONTEXT);
                decimalList.set(i, curValue);
            }
            WeatherCityHisDO cityFcDO = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap = com.tsintergy.lf.core.util.ColumnUtil.listToMap(decimalList, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO, decimalMap);
            cityFcDO.setType(Integer.valueOf(weatherType));
            cityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO.setCityId("1");
            cityFcDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO.setDate(new java.sql.Date(com.tsieframework.core.base.format.datetime.DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
            fcWeatherDOS.add(cityFcDO);
        }
        return fcWeatherDOS;
    }

    @Override
    public List<WeatherCityHisDO> getCityAvgHisWeatherData(Integer type, Date start, Date end) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        Date dataSwitchDate = DateUtil.getDateFromString(DATA_SWITCH_DATE_STR, DateUtil.DATE_FORMAT2);
        // 判断查询的时间段是否跨越了数据源切换日期
        boolean crossesDataSwitchDate = !start.after(dataSwitchDate) && !end.before(dataSwitchDate);
        if (crossesDataSwitchDate || start.before(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之前，则从旧的数据源获取数据
            Date oldEndDate = crossesDataSwitchDate ? com.tsieframework.core.base.format.datetime.DateUtils.addDays(dataSwitchDate, -1) : end;
            List<WeatherCityHisDO> oldData = weatherCityHisService.getWeatherCityHisDOs(Arrays.asList("1"), type, start, oldEndDate);
            result.addAll(oldData);
        }
        if (crossesDataSwitchDate || end.after(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之后，则从新的数据源获取数据
            Date newStartDate = crossesDataSwitchDate ? dataSwitchDate : start;
            List<WeatherCityHisDO> provinceWeightAvgHisWeatherData = this.getProvinceWeightAvgHisWeatherData(type, newStartDate, end);
            result.addAll(provinceWeightAvgHisWeatherData);
        }
        result.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        return result;
    }

    @Override
    public List<WeatherCityHisDO> getCityAvgHisEffectiveWeatherData(Integer type, Date start, Date end) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        Date dataSwitchDate = DateUtil.getDateFromString(DATA_SWITCH_DATE_STR, DateUtil.DATE_FORMAT2);
        // 判断查询的时间段是否跨越了数据源切换日期
        boolean crossesDataSwitchDate = !start.after(dataSwitchDate) && !end.before(dataSwitchDate);
        if (crossesDataSwitchDate || start.before(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之前，则从旧的数据源获取数据
            Date oldEndDate = crossesDataSwitchDate ? com.tsieframework.core.base.format.datetime.DateUtils.addDays(dataSwitchDate, -1) : end;
            List<WeatherCityHisDO> oldData = weatherCityHisService.getWeatherCityHisDOs(Arrays.asList("1"), type, start, oldEndDate);
            result.addAll(oldData);
        }
        if (crossesDataSwitchDate || end.after(dataSwitchDate)) {
            // 如果查询的时间段包括或完全在切换日期之后，则从新的数据源获取数据
            Date newStartDate = crossesDataSwitchDate ? dataSwitchDate : start;
            List<WeatherStationHisBasicWgDO> hisWeatherBasic = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic("1", type, newStartDate, end);
            for (WeatherStationHisBasicWgDO stationHisBasicWgDO : hisWeatherBasic) {
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                BeanUtils.copyProperties(stationHisBasicWgDO, weatherCityHisDO);
                weatherCityHisDO.setCityId(getCityIdByStationId(stationHisBasicWgDO.getStationWgId()));
                result.add(weatherCityHisDO);
            }
        }
        result.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        return result;
    }

    public List<BigDecimal> zero96List() {
        List<BigDecimal> list = new ArrayList<>(96);
        for (int i = 0; i < 96; i++) {
            list.add(null);
        }
        return list;
    }
}