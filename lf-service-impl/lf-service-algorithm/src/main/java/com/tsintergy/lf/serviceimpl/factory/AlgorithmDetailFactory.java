/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/5/14 16:48 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.factory;

import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmDetail;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.Param;

/**
 * 算法执行路径等信息对象创建工厂
 *
 * <AUTHOR>
 * @create 2020/8/27
 * @since 1.0.0
 */
public class AlgorithmDetailFactory {

    public static AlgorithmDetail create(Param param) {
        AlgorithmEnum algorithmEnum = param.getAlgorithmEnum();
        AlgorithmDetail algorithmDetail;
        Boolean async = true;
        //程序循环一天一天调用时 每日预测同步调用
        if (!ForecastParam.FORECAST_CYCLIC_TYPE.equals(param.getForecastType())) {
            async = false;
        }
        switch (algorithmEnum) {
            //台风算法
            case TYPHOON_SVM:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.TYPHOON_IN, AlgorithmConstants.TYPHOON_OUT,
                        AlgorithmConstants.TYPHOON_RUN, AlgorithmConstants.TYPHOON_TEMPLATE_NAME,
                        AlgorithmConstants.TYPHOON_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true);
                break;
            //超短期 超短期的模板有两个，应用时模板名称手动注入
            case SHORT_FORECAST:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.SHORT_IN_PATH,
                        AlgorithmConstants.SHORT_OUT_PATH,
                        AlgorithmConstants.SHORT_RUN_PATH, null,
                        AlgorithmConstants.SHORT_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true);
                break;
            //决策树
            case REPLENISH_LGB:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH, AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.LGB_TEMPLATE_NAME,
                        AlgorithmConstants.LGB_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            //节假日预测
            case HOLIDAY_FEST_SVM:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.HOLIDAY_IN, AlgorithmConstants.HOLIDAY_OUT,
                        AlgorithmConstants.HOLIDAY_RUN, AlgorithmConstants.HOLIDAY_TEMPLATE_NAME,
                        AlgorithmConstants.HOLIDAY_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
            //数据清洗
            case LOAD_PRE_PROCESS:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.PREPROCESS_IN_PATH,
                        AlgorithmConstants.PREPROCESS_OUT_PATH,
                        AlgorithmConstants.PREPROCESS_RUN_PATH, AlgorithmConstants.PREPROCESS_TEMPLATE_NAME,
                        AlgorithmConstants.PREPROCESS_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true);
                break;
            //标点
            case FORECAST_SVM_LARGE:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.FORECAST_TEMPLATE_NAME,
                        AlgorithmConstants.PRECDIC_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
            //灵敏度
            case SENSITIVITY:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.SENSITIVITY_IN_PATH,
                        AlgorithmConstants.SENSITIVITY_OUT_PATH,
                        AlgorithmConstants.SENSITIVITY_RUN_PATH, AlgorithmConstants.SENSITIVITY_TEMPLATE_NAME,
                        AlgorithmConstants.SENSITIVITY_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            //稳定度分析 模板和exe取自配置
            case STABILITY:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.PRECISION_IN, AlgorithmConstants.PRECISION_OUT,
                        AlgorithmConstants.PRECISION_RUN, null,
                        AlgorithmConstants.PRECISION_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            //神经网络（原来的新息算法）
            case FORECAST_INNOVATION:
            case FORECAST_INNOVATION_CHECK:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.FORECAST_TEMPLATE_NAME,
                        AlgorithmConstants.NEW_EXE,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
            //综合模型
            case COMPREHENSIVE_MODEL:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.MF_TEMPLATE_NAME,
                        AlgorithmConstants.MF_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
            //中长期综合模型
            case LONE_COMPOSITE:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.LONG_COMPOSITE_TEMPLATE_NAME,
                        AlgorithmConstants.LONG_COMPOSITE_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, false, async);
                break;

            //梯度提升
            case FORECAST_XGBOOST:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.FORECAST_TEMPLATE_NAME,
                        AlgorithmConstants.XG_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
            //中长期vec
            case LONG_VEC_MAX:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.LONGVEC_IN_PATH,
                        AlgorithmConstants.LONGVEC_OUT_PATH,
                        AlgorithmConstants.LONGVEC_RUN_PATH,
                        AlgorithmConstants.LONGVEC_MAX_TEMPLATE_NAME,
                        AlgorithmConstants.LONG_VEC_MAX_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;

            case LONG_VEC_MIN:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.LONGVEC_IN_PATH,
                        AlgorithmConstants.LONGVEC_OUT_PATH,
                        AlgorithmConstants.LONGVEC_RUN_PATH,
                        AlgorithmConstants.LONGVEC_MIN_TEMPLATE_NAME,
                        AlgorithmConstants.LONG_VEC_MIN_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            case LONG_VEC_ENERGY:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.LONGVEC_IN_PATH,
                        AlgorithmConstants.LONGVEC_OUT_PATH,
                        AlgorithmConstants.LONGVEC_RUN_PATH,
                        AlgorithmConstants.LONGVEC_ENERGY_TEMPLATE_NAME,
                        AlgorithmConstants.LONG_VEC_ENERGY_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            //综合相似日查找
            case SIMILAR_DAY_SEARCH:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.SIMILARITY_IN_PATH,
                        AlgorithmConstants.SIMILARITY_OUT_PATH,
                        AlgorithmConstants.SIMILARITY_RUN_PATH, AlgorithmConstants.SIMILARITY_TEMPLATE_NAME,
                        AlgorithmConstants.SIMILARITY_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, false);
                break;
            //相似日101&偏差补偿102&支持向量机103（101&102暂无结果）
            default:
                algorithmDetail = new AlgorithmDetail(AlgorithmConstants.FORECAST_IN_PATH,
                        AlgorithmConstants.FORECAST_OUT_PATH,
                        AlgorithmConstants.FORECAST_RUN_PATH, AlgorithmConstants.FORECAST_TEMPLATE_NAME,
                        AlgorithmConstants.STLF_EXE_NAME,
                        AlgorithmConstants.LICENCE_PATH, true, async);
                break;
        }
        return algorithmDetail;
    }

}
