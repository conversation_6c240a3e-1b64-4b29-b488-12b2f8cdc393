/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/3/17 3:01 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.configure;


import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Description:  <br> 算法路径
 *
 * <AUTHOR>
 * @create 2020/3/17
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "algorithm.path")
public class AlgorithmPathProperties {

    private String similarPath;

    private String similarName;

    private String forecastPath;

    private String forecastName;

    private String licPath;

    public String getSimilarPath() {
        return similarPath;
    }

    public void setSimilarPath(String similarPath) {
        this.similarPath = similarPath;
    }

    public String getSimilarName() {
        return similarName;
    }

    public void setSimilarName(String similarName) {
        this.similarName = similarName;
    }

    public String getForecastPath() {
        return forecastPath;
    }

    public void setForecastPath(String forecastPath) {
        this.forecastPath = forecastPath;
    }

    public String getForecastName() {
        return forecastName;
    }

    public void setForecastName(String forecastName) {
        this.forecastName = forecastName;
    }

    public String getLicPath() {
        return licPath;
    }

    public void setLicPath(String licPath) {
        this.licPath = licPath;
    }
}