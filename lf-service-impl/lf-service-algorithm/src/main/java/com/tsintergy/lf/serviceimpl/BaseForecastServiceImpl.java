/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AutoForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import java.sql.Timestamp;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/25 14:36
 * @Version: 1.0.0
 */
public class BaseForecastServiceImpl {

    @Autowired
    protected SettingSystemService settingSystemService;

    @Autowired
    protected LoadCityFcService loadCityFcService;

    @Autowired
    protected LoadCityFcBatchService loadCityFcBatchService;

    /**
     * 是否自动上报
     */
    protected AutoForecastDTO reportSwitch(String cityId, Integer dayType, Date startDate, Date endDate)
        throws Exception {
        boolean autoReport;
        SystemData systemSetting = settingSystemService.getSystemSetting();
        String systemAlgorithmId;
        Date cuteEndDate;
        //获取自动上报算法id
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            systemAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
            autoReport = systemSetting.getProvinceAutoReportSwitch().equals(ParamConstants.STRING_COMPOSITE_ON);
            if (ParamConstants.HOLIDAY.equals(dayType)) {
                systemAlgorithmId = systemSetting.getProvinceHolidayAlgorithm();
            }
        } else {
            systemAlgorithmId = systemSetting.getCityNormalAlgorithm();
            autoReport = systemSetting.getCityAutoReportSwitch().equals(ParamConstants.STRING_COMPOSITE_ON);
            if (ParamConstants.HOLIDAY.equals(dayType)) {
                systemAlgorithmId = systemSetting.getCityHolidayAlgorithm();
            }
        }
        //获取上报的截止日期（自动上报几天）
        Integer autoReportDays = Integer.valueOf(systemSetting.getForecastDay());
        //节假日时 自动上报 全节假日+节假日后一天
        if (ParamConstants.HOLIDAY.equals(dayType)) {
            cuteEndDate = DateUtils.addDays(endDate, 1);
        } else {
            cuteEndDate = DateUtils.addDays(startDate, autoReportDays);
        }
        AutoForecastDTO dto = new AutoForecastDTO();
        dto.setReportSwitch(autoReport);
        dto.setCuteDate(cuteEndDate);
        dto.setSelectAlgorithmId(systemAlgorithmId);
        return dto;
    }


    /**
     * 保存上报结果
     */
    protected void doSaveOrUpdateResult(LoadCityFcDO metaData, AutoForecastDTO autoForecastDTO) throws Exception {

        String cityId = metaData.getCityId();
        String caliberId = metaData.getCaliberId();

        //查询库里是否有此算法，如果有，则覆盖，如果没有，则创建
        LoadCityFcDO fcVO = loadCityFcService
            .getLoadFc(metaData.getDate(), cityId, caliberId, metaData.getAlgorithmId());
        if (fcVO == null) {
            LoadCityFcDO loadCityFcDO = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                caliberId,
                metaData.getAlgorithmId());
            loadCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO);
            loadCityFcDO.setRecommend(false);
            //需要默认上报开启&&算法id和上报id一致&&上报天数的范围内才上报
            if (autoForecastDTO.isReportSwitch() && autoForecastDTO.getSelectAlgorithmId()
                .equals(metaData.getAlgorithmId()) && metaData.getDate().before(autoForecastDTO.getCuteDate())) {
                RepeatReport(cityId, caliberId, metaData.getDate());
                loadCityFcDO.setReport(true);
                loadCityFcDO.setSucceed(true);
                loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            } else {
                loadCityFcDO.setReport(false);
                loadCityFcDO.setSucceed(false);
            }
            //装载 推荐算法标识
            if (metaData.getAlgorithmId().equals(autoForecastDTO.getSelectAlgorithmId())) {
                loadCityFcDO.setRecommend(true);
            }
            loadCityFcService.doCreateAndFlush(loadCityFcDO);
            //插入算法预测批次表
            loadCityFcBatchService.doSave(loadCityFcDO);
        } else {
            //防止覆盖人工修正上报的数据
            if (fcVO.getReport() && AlgorithmEnum.FORECAST_MODIFY.getId().equals(fcVO.getAlgorithmId())) {
                return;
            }
            fcVO.setRecommend(false);
            ColumnUtil.copyPropertiesIgnoreNull(metaData, fcVO);
            if (autoForecastDTO.isReportSwitch() && autoForecastDTO.getSelectAlgorithmId()
                .equals(fcVO.getAlgorithmId()) && metaData.getDate().before(autoForecastDTO.getCuteDate())) {
                RepeatReport(cityId, caliberId, metaData.getDate());
                fcVO.setReport(true);
                fcVO.setSucceed(true);
                fcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            } else {
                fcVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                fcVO.setReport(false);
                fcVO.setSucceed(false);
            }
            if (fcVO.getAlgorithmId().equals(autoForecastDTO.getSelectAlgorithmId())) {
                fcVO.setRecommend(true);
            }
            loadCityFcService.doUpdateLoadCityFcDO(fcVO);
            //插入算法预测批次表
            loadCityFcBatchService.doSave(fcVO);
        }

    }


    /**
     * 判断重复上报 如果有已上报的数据且上报的数据不是人工修正，则将已上报的数据改为未上报
     */
    private void RepeatReport(String cityId, String caliberId, Date date) throws Exception {
        //以防万一有两个上报结果，所以这里在判断一下是否有正常日已经上报了,如果已经上报了正常日的算法并且不是人工修正，则把上报取消掉
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, date);
        if (report != null && !report.getAlgorithmId().equals(AlgorithmConstants.MD_ALGORITHM_ID)) {
            report.setReport(false);
            report.setSucceed(false);
            report.setRecommend(false);
            loadCityFcService.doUpdateLoadCityFcDO(report);
        }
    }


}