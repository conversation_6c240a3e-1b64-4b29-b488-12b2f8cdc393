package com.tsintergy.lf.serviceimpl.client;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.dayMaxLoadForecast.DayMaxLoadForecastBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractDayMaxLoadForecastInvokeClient;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.DayMaxLoadParam;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcAdapterService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisAdapterService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
public class DayMaxLoadClient extends AbstractDayMaxLoadForecastInvokeClient<DayMaxLoadParam> {


    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    HolidayService holidayService;

    @Autowired
    private SettingSystemService settingSystemService;


    @Autowired
    private WeatherCityHisAdapterService weatherCityHisAdapterService;

    @Autowired
    private WeatherCityFcAdapterService weatherCityFcAdapterService;



    @Override
    protected DayMaxLoadForecastBasicData generateBasicData(DayMaxLoadParam param) {
        Date beginDate = param.getBeginDate();
        Date lastDate = param.getEndDate();

        //气象开始时间
//        String trainBeginDay = param.getTrainBeginDay();
        String trainBeginDay = param.getTrainBeginDay();
        Date trainBeginDate = DateUtil.getDate(trainBeginDay, "yyyy-MM-dd");
        //气象数据源
        List<LoadCityHisDO> loadCityHisDOS = null;
        DayMaxLoadForecastBasicData dayMaxLoadForecastBasicData = null;
        String userCity = param.getUserCity();
        List<String> cityIds = Arrays.asList(userCity.split(","));
        try {
            loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(param.getCityId(),param.getCaliberId(),trainBeginDate,lastDate);
            List<HolidayDO> holidayDOS = holidayService.findHolidayVOS(null, null);

            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisAdapterService.findWeatherCityHisDOS(param.getWeatherSource(), null, WeatherEnum.TEMPERATURE.getType(), trainBeginDate, lastDate);
            weatherCityHisDOs = weatherCityHisDOs.stream().filter(t->cityIds.contains(t.getCityId())).collect(Collectors.toList());

            List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcAdapterService.findWeatherCityFcDOS(param.getWeatherSource(), null,  WeatherEnum.TEMPERATURE.getType(), trainBeginDate, lastDate);
            weatherCityFcDOs = weatherCityFcDOs.stream().filter(t->cityIds.contains(t.getCityId())).collect(Collectors.toList());
            dayMaxLoadForecastBasicData = new DayMaxLoadForecastBasicData(loadCityHisDOS,weatherCityHisDOs,weatherCityFcDOs,holidayDOS,settingSystemService.getNotIncludedDateList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return dayMaxLoadForecastBasicData;
    }
}
