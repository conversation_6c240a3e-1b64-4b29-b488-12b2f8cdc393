<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>lf-service-impl</artifactId>
    <groupId>com.tsintergy.lf</groupId>
    <version>${lf.version}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>lf-service-algorithm</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.tsieframework.boot</groupId>
      <artifactId>tsie-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-starter-service-impl</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-config</artifactId>
      <optional>true</optional>
    </dependency>


    <dependency>
      <groupId>com.tsintergy.algorithm</groupId>
      <artifactId>tsie-alginv-service-client</artifactId>
    </dependency>
    <!--
        1）tsie-alginv-service-client和tsie-alginv-service-server放到一起表示algorithmInvokerClient和algorithmInvokerServer部署到一起
        2）如果把algorithmInvokerServer单独成一个服务则需要创建一个模块并添加tsie-alginv-service-server
     -->
    <dependency>
      <groupId>com.tsintergy.algorithm</groupId>
      <artifactId>tsie-alginv-service-server</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>org.apache.commons</groupId>-->
<!--      <artifactId>commons-compress</artifactId>-->
<!--      <version>1.21</version>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.tsintergy.lf</groupId>
      <artifactId>lf-service-base</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tsintergy.aif</groupId>
      <artifactId>aif-algorithm-service-api</artifactId>
    </dependency>
    <dependency>
      <artifactId>aif-algorithm-service-client</artifactId>
      <groupId>com.tsintergy.aif</groupId>
    </dependency>
    <dependency>
      <artifactId>aif-algorithm-service-base</artifactId>
      <groupId>com.tsintergy.aif</groupId>
    </dependency>

    <dependency>
      <groupId>com.tsieframework.cloud.security</groupId>
      <artifactId>tsie-cloud-security-service-api</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>com.tsieframework.cloud.security</groupId>
      <artifactId>tsie-cloud-security-service-base</artifactId>
      <optional>true</optional>
    </dependency>

  </dependencies>

  <profiles>
    <profile>
      <id>package-springboot</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <version>2.1.6.RELEASE</version>
            <configuration>
              <excludes>
                <exclude>
                  <groupId>com.tsintergy.lf</groupId>
                  <artifactId>lf-config</artifactId>
                </exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>package-docker</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-maven-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>