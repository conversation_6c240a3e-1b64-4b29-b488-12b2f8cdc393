package com.tsintergy.lf.serviceimpl.forecast.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.ThresholdCityFcDO;
import com.tsintergy.lf.core.constants.Constants;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: ThresholdCityFcBO.java, v 0.1 2018-02-28 15:20:42 tao Exp $$
 */

@Component
public class ThresholdCityFcDAO extends BaseAbstractDAO<ThresholdCityFcDO> {

    public List<ThresholdCityFcDO> findThresholdCityFcVOS(String cityId, Date date, Integer type, String caliberId) throws Exception{
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (!StringUtils.isBlank(cityId)){
            param.getQueryConditions().put("_se_cityId",cityId);
        }
        if (null!=date){
            param.getQueryConditions().put("_de_date",new java.sql.Date(date.getTime()));
        }
        if (null!=type){
            param.getQueryConditions().put("_ne_type",type);
        }
        if (!StringUtils.isBlank(caliberId)){
            param.getQueryConditions().put("_se_caliberId",caliberId);
        }
        List<ThresholdCityFcDO> thresholdCityFcDOS = this.query(param).getDatas();
        return thresholdCityFcDOS;
    }
}