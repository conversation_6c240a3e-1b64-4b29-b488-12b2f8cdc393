package com.tsintergy.lf.serviceimpl.forecast.impl;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.MultiDayForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.dto.DayValueDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 14:08
 **/
@Service("multiDayForecastService")
public class MultiDayForecastServiceImpl implements MultiDayForecastService {

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private LoadCityHisService loadCityHisService;


    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherCityHisAdapterService weatherCityHisService;

    @Autowired
    private WeatherCityFcBatchService weatherCityFcBatchService;

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    /**
     * 查询多日负荷数据（预测、上报和实际值）
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param forecastDate 预测基准日期
     * @param startDay 起始天数偏移（相对于forecastDate）
     * @param endDay 结束天数偏移
     * @param algorithmId 算法ID
     * @param batchId 批次ID
     * @return MultiDayQueryDTO 包含多日负荷数据和特征值
     * @throws Exception
     */
    public MultiDayQueryDTO findMultiDayLoad(String cityId, String caliberId, Date forecastDate,
                                             Integer startDay, Integer endDay, String algorithmId,
                                             String batchId) throws Exception {

        // 1. 计算日期范围
        Date startDate = DateUtil.getMoveDay(forecastDate, startDay);
        Date endDate = DateUtil.getMoveDay(forecastDate, endDay);
        List<Date> dateRange = DateUtil.getListBetweenDay(startDate, endDate);

        // 2. 获取三种类型的数据源
        // 2.1 多日算法预测负荷
        List<LoadCityFcBatchDO> algoForecasts = loadCityFcBatchService.findLoadByCreateTime(
                cityId, forecastDate, caliberId, algorithmId, batchId, null);
        Map<Date, LoadCityFcBatchDO> algoForecastMap = algoForecasts.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (o, n) -> n));

        // 2.2 多日人工上报负荷
        List<LoadCityFcBatchDO> manualReports = loadCityFcBatchService.findLoadByCreateTime(
                cityId, forecastDate, caliberId, null, batchId, true);
        Map<Date, LoadCityFcBatchDO> manualReportMap = manualReports.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (o, n) -> n));

        // 2.3 多日历史实际负荷
        List<LoadCityHisDO> historicalLoads = loadCityHisService.getLoadCityHisDOS(
                cityId, caliberId, startDate, endDate);
        Map<Date, LoadCityHisDO> historicalLoadMap = historicalLoads.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (o, n) -> n));

        // 3. 初始化返回结果结构
        MultiDayQueryDTO result = new MultiDayQueryDTO();
        List<MultiDayLoadQueryDTO> loadResults = new ArrayList<>();
        List<MultiDayFeatureQueryDTO> forecastFeatures = new ArrayList<>();
        List<MultiDayFeatureQueryDTO> historicalFeatures = new ArrayList<>();

        result.setMultiDayLoad(loadResults);
        result.setMultiDayFcFeature(forecastFeatures);
        result.setMultiDayHisFeature(historicalFeatures);

        // 4. 按日期填充数据
        for (Date currentDate : dateRange) {
            // 4.1 创建基础负荷数据对象
            MultiDayLoadQueryDTO dailyLoad = new MultiDayLoadQueryDTO();
            dailyLoad.setDate(currentDate);
            loadResults.add(dailyLoad);

            // 4.2 处理预测数据
            MultiDayFeatureQueryDTO forecastFeature = new MultiDayFeatureQueryDTO();
            forecastFeature.setDate(currentDate);
            forecastFeatures.add(forecastFeature);

            LoadCityFcBatchDO algoForecast = algoForecastMap.get(currentDate);
            if (algoForecast != null) {
                dailyLoad.setFc(algoForecast.getLoadList());
                LoadFeatureFcDTO forecastStats = loadFeatureStatService.findStatisticsDayFeature(algoForecast);
                forecastFeature.setFeature(forecastStats);
            }

            // 4.3 处理上报数据
            LoadCityFcBatchDO manualReport = manualReportMap.get(currentDate);
            if (manualReport != null) {
                dailyLoad.setModify(manualReport.getLoadList());
            }

            // 4.4 处理历史数据
            MultiDayFeatureQueryDTO historicalFeature = new MultiDayFeatureQueryDTO();
            historicalFeature.setDate(currentDate);
            historicalFeatures.add(historicalFeature);

            LoadCityHisDO historicalLoad = historicalLoadMap.get(currentDate);
            if (historicalLoad != null) {
                dailyLoad.setReal(historicalLoad.getloadList());
                LoadFeatureFcDTO historicalStats = loadFeatureStatService.findStatisticsDayFeature(algoForecast);
                historicalFeature.setFeature(historicalStats);
            }
        }

        // 5. 根据forecastFeatures集合计算多日负荷特性并赋值给MultiDayQueryDTO
        calculateMultiDayLoadCharacteristics(result, forecastFeatures);

        return result;
    }


    /**
     * 查询多日天气数据（预测和实际值）
     * @param cityId 城市ID
     * @param forecastDate 预测基准日期
     * @param startDay 起始天数偏移（相对于forecastDate）
     * @param endDay 结束天数偏移
     * @param weatherSource 天气数据源
     * @param batchId 批次ID
     * @return MultiDayQueryWeatherDTO 包含多日天气数据和特征值
     * @throws Exception
     */
    public MultiDayQueryWeatherDTO findMultiDayWeather(String cityId, Date forecastDate,
                                                       Integer startDay, Integer endDay,
                                                       String weatherSource, String batchId)
            throws Exception {
        // 1. 初始化并验证参数
        if (cityId == null || forecastDate == null) {
            throw new IllegalArgumentException("城市ID和预测日期不能为空");
        }

        // 2. 准备日期范围
        String weatherCityId = cityService.findWeatherCityId(cityId);
        Date startDate = DateUtil.getMoveDay(forecastDate, startDay);
        Date endDate = DateUtil.getMoveDay(forecastDate, endDay);
        List<Date> dateRange = DateUtil.getListBetweenDay(startDate, endDate);

        // 3. 获取三种数据源
        // 3.1 天气预测数据
        List<WeatherCityFcBatchDO> weatherForecasts = weatherCityFcBatchService.findByCondition(
                weatherCityId, weatherSource, forecastDate, null, batchId);
        Map<Date, List<WeatherCityFcBatchDO>> weatherForecastMap = weatherForecasts.stream()
                .collect(Collectors.groupingBy(WeatherCityFcBatchDO::getDate));

        // 3.2 历史天气数据
        List<WeatherCityHisDO> historicalWeather = weatherCityHisService.findHisWeather(weatherSource,
                weatherCityId, startDate, endDate);
        Map<Date, List<WeatherCityHisDO>> historicalWeatherMap = historicalWeather.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));


        // 4. 初始化返回结果
        MultiDayQueryWeatherDTO result = new MultiDayQueryWeatherDTO();
        List<MultiDayLoadQueryDTO> weatherDataList = new ArrayList<>();
        List<MultiDayWeatherFeatureQueryDTO> historicalFeatureList = new ArrayList<>();
        List<MultiDayWeatherFeatureQueryDTO> forecastFeatureList = new ArrayList<>();

        result.setMultiDayLoad(weatherDataList);
        result.setMultiDayHisFeature(historicalFeatureList);
        result.setMultiDayFcFeature(forecastFeatureList);

        // 5. 按日期填充数据
        for (Date currentDate : dateRange) {
            // 5.1 创建基础天气数据对象
            MultiDayLoadQueryDTO dailyWeather = new MultiDayLoadQueryDTO();
            dailyWeather.setDate(currentDate);
            weatherDataList.add(dailyWeather);

            // 5.2 处理预测数据
            processForecastData(currentDate, weatherForecastMap, dailyWeather, forecastFeatureList);

            // 5.3 处理历史数据
            processHistoricalData(currentDate, historicalWeatherMap,
                    dailyWeather, historicalFeatureList);
        }

        // 6. 根据forecastFeatureList集合计算多日气象特性并赋值给MultiDayQueryWeatherDTO
        calculateMultiDayWeatherCharacteristics(result, forecastFeatureList);

        return result;
    }

    /**
     * 处理天气预测数据
     */
    private void processForecastData(Date date, Map<Date, List<WeatherCityFcBatchDO>> forecastMap,
                                     MultiDayLoadQueryDTO dailyWeather,
                                     List<MultiDayWeatherFeatureQueryDTO> featureList) throws Exception {
        List<WeatherCityFcBatchDO> forecastList = forecastMap.get(date);
        MultiDayWeatherFeatureQueryDTO forecastFeature = new MultiDayWeatherFeatureQueryDTO();
        forecastFeature.setDate(date);
        featureList.add(forecastFeature);

        if (!CollectionUtils.isEmpty(forecastList)) {
            // 获取温度预测数据
            List<WeatherCityFcBatchDO> tempForecasts = forecastList.stream()
                    .filter(t -> WeatherEnum.TEMPERATURE.getType().equals(t.getType()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(tempForecasts)) {
                dailyWeather.setFc(tempForecasts.get(0).getWeatherList());
            }

            // 计算天气特征
            WeatherFeatureDTO featureDTO = new WeatherFeatureDTO();
            WeatherFeatureCityDayFcDO featureData = weatherFeatureStatService
                    .doStatWeatherByCityAndType(forecastList);
            BeanUtils.copyProperties(featureData, featureDTO);
            forecastFeature.setFeature(featureDTO);
        }
    }

    /**
     * 处理历史天气数据
     */
    private void processHistoricalData(Date date, Map<Date, List<WeatherCityHisDO>> historicalMap,
                                       MultiDayLoadQueryDTO dailyWeather,
                                       List<MultiDayWeatherFeatureQueryDTO> featureList) throws Exception {
        // 设置历史天气数据
        // 设置历史天气特征
        MultiDayWeatherFeatureQueryDTO historicalFeature = new MultiDayWeatherFeatureQueryDTO();
        historicalFeature.setDate(date);
        featureList.add(historicalFeature);
        List<WeatherCityHisDO> historicalData = historicalMap.get(date);
        if (historicalData != null) {
            List<WeatherCityHisDO> tempList = historicalData.stream().filter(t -> t.getType().equals(WeatherEnum.TEMPERATURE.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempList)) {
                dailyWeather.setReal(tempList.get(0).getloadList());
            }
            WeatherFeatureDTO featureDTO = new WeatherFeatureDTO();
            WeatherFeatureCityDayFcDO featureData = weatherFeatureStatService
                    .doStatWeatherByCityAndType(historicalData);
            BeanUtils.copyProperties(featureData, featureDTO);
            historicalFeature.setFeature(featureDTO);

        }
    }

    /**
     * 根据预测特性集合计算多日负荷特性并赋值给MultiDayQueryDTO
     * @param result 结果对象
     * @param forecastFeatures 预测特性集合
     */
    private void calculateMultiDayLoadCharacteristics(MultiDayQueryDTO result,
                                                     List<MultiDayFeatureQueryDTO> forecastFeatures) {
        if (CollectionUtils.isEmpty(forecastFeatures)) {
            return;
        }

        // 初始化最值变量
        BigDecimal maxLoad = null;
        Date maxLoadDate = null;
        BigDecimal maxNoonLoad = null;
        Date maxNoonLoadDate = null;
        BigDecimal maxEveningLoad = null;
        Date maxEveningLoadDate = null;
        BigDecimal minLoad = null;
        Date minLoadDate = null;
        BigDecimal maxDifferent = null;
        Date maxDifferentDate = null;
        BigDecimal minNoonLoad = null;
        Date minNoonLoadDate = null;
        BigDecimal minEveningLoad = null;
        Date minEveningLoadDate = null;
        BigDecimal maxGradient = null;
        Date maxGradientDate = null;

        // 遍历所有预测特性数据
        for (MultiDayFeatureQueryDTO featureQuery : forecastFeatures) {
            if (featureQuery.getFeature() == null) {
                continue;
            }

            LoadFeatureFcDTO feature = featureQuery.getFeature();
            Date currentDate = featureQuery.getDate();

            // 多日最大负荷及其发生日期
            if (feature.getMaxLoad() != null && (maxLoad == null || feature.getMaxLoad().compareTo(maxLoad) > 0)) {
                maxLoad = feature.getMaxLoad();
                maxLoadDate = currentDate;
            }

            // 多日最大午峰负荷及其发生日期
            if (feature.getNoonMaxLoad() != null && (maxNoonLoad == null || feature.getNoonMaxLoad().compareTo(maxNoonLoad) > 0)) {
                maxNoonLoad = feature.getNoonMaxLoad();
                maxNoonLoadDate = currentDate;
            }

            // 多日最大晚峰负荷及其发生日期
            if (feature.getEveningMaxLoad() != null && (maxEveningLoad == null || feature.getEveningMaxLoad().compareTo(maxEveningLoad) > 0)) {
                maxEveningLoad = feature.getEveningMaxLoad();
                maxEveningLoadDate = currentDate;
            }

            // 多日最小负荷及其发生日期
            if (feature.getMinLoad() != null && (minLoad == null || feature.getMinLoad().compareTo(minLoad) < 0)) {
                minLoad = feature.getMinLoad();
                minLoadDate = currentDate;
            }

            // 单日最大峰谷差及其发生日期
            if (feature.getDifferent() != null && (maxDifferent == null || feature.getDifferent().compareTo(maxDifferent) > 0)) {
                maxDifferent = feature.getDifferent();
                maxDifferentDate = currentDate;
            }

            // 多日最小午峰负荷及其发生日期
            if (feature.getNoonMaxLoad() != null && (minNoonLoad == null || feature.getNoonMaxLoad().compareTo(minNoonLoad) < 0)) {
                minNoonLoad = feature.getNoonMaxLoad();
                minNoonLoadDate = currentDate;
            }

            // 多日最小晚峰负荷及其发生日期
            if (feature.getEveningMaxLoad() != null && (minEveningLoad == null || feature.getEveningMaxLoad().compareTo(minEveningLoad) < 0)) {
                minEveningLoad = feature.getEveningMaxLoad();
                minEveningLoadDate = currentDate;
            }

            // 单日最大峰谷差率及其发生日期
            if (feature.getGradient() != null && (maxGradient == null || feature.getGradient().compareTo(maxGradient) > 0)) {
                maxGradient = feature.getGradient();
                maxGradientDate = currentDate;
            }
        }

        // 赋值给结果对象
        if (maxLoad != null) {
            DayValueDTO maxLoadDTO = new DayValueDTO();
            maxLoadDTO.setValue(maxLoad);
            maxLoadDTO.setDate(maxLoadDate);
            result.setMultiDayMaxLoad(maxLoadDTO);
        }

        if (maxNoonLoad != null) {
            DayValueDTO maxNoonLoadDTO = new DayValueDTO();
            maxNoonLoadDTO.setValue(maxNoonLoad);
            maxNoonLoadDTO.setDate(maxNoonLoadDate);
            result.setMultiDayMaxNoonLoad(maxNoonLoadDTO);
        }

        if (maxEveningLoad != null) {
            DayValueDTO maxEveningLoadDTO = new DayValueDTO();
            maxEveningLoadDTO.setValue(maxEveningLoad);
            maxEveningLoadDTO.setDate(maxEveningLoadDate);
            result.setMultiDayMaxEveningLoad(maxEveningLoadDTO);
        }

        if (minLoad != null) {
            DayValueDTO minLoadDTO = new DayValueDTO();
            minLoadDTO.setValue(minLoad);
            minLoadDTO.setDate(minLoadDate);
            result.setMultiDayMinLoad(minLoadDTO);
        }

        if (maxDifferent != null) {
            DayValueDTO maxDifferentDTO = new DayValueDTO();
            maxDifferentDTO.setValue(maxDifferent);
            maxDifferentDTO.setDate(maxDifferentDate);
            result.setMultiDayDifMaxLoad(maxDifferentDTO);
        }

        if (minNoonLoad != null) {
            DayValueDTO minNoonLoadDTO = new DayValueDTO();
            minNoonLoadDTO.setValue(minNoonLoad);
            minNoonLoadDTO.setDate(minNoonLoadDate);
            result.setMultiDayMinNoonLoad(minNoonLoadDTO);
        }

        if (minEveningLoad != null) {
            DayValueDTO minEveningLoadDTO = new DayValueDTO();
            minEveningLoadDTO.setValue(minEveningLoad);
            minEveningLoadDTO.setDate(minEveningLoadDate);
            result.setMultiDayMinEveningLoad(minEveningLoadDTO);
        }

        if (maxGradient != null) {
            DayValueDTO maxGradientDTO = new DayValueDTO();
            maxGradientDTO.setValue(maxGradient);
            maxGradientDTO.setDate(maxGradientDate);
            result.setMultiDayDifMaxRate(maxGradientDTO);
        }
    }

    /**
     * 根据预测气象特性集合计算多日气象特性并赋值给MultiDayQueryWeatherDTO
     * @param result 结果对象
     * @param forecastFeatureList 预测气象特性集合
     */
    private void calculateMultiDayWeatherCharacteristics(MultiDayQueryWeatherDTO result,
                                                        List<MultiDayWeatherFeatureQueryDTO> forecastFeatureList) {
        if (CollectionUtils.isEmpty(forecastFeatureList)) {
            return;
        }

        // 初始化最值变量（这里以最高温度作为代表性的气象特性）
        BigDecimal maxTemperature = null;
        Date maxTemperatureDate = null;

        // 遍历所有预测气象特性数据
        for (MultiDayWeatherFeatureQueryDTO featureQuery : forecastFeatureList) {
            if (featureQuery.getFeature() == null) {
                continue;
            }

            WeatherFeatureDTO feature = featureQuery.getFeature();
            Date currentDate = featureQuery.getDate();

            // 多日最高温度及其发生日期（作为代表性的气象特性）
            if (feature.getHighestTemperature() != null &&
                (maxTemperature == null || feature.getHighestTemperature().compareTo(maxTemperature) > 0)) {
                maxTemperature = feature.getHighestTemperature();
                maxTemperatureDate = currentDate;
            }
        }

        // 赋值给结果对象（使用multiDayMaxLoad字段存储最高温度信息）
        if (maxTemperature != null) {
            DayValueDTO maxTemperatureDTO = new DayValueDTO();
            maxTemperatureDTO.setValue(maxTemperature);
            maxTemperatureDTO.setDate(maxTemperatureDate);
            result.setMultiDayMaxLoad(maxTemperatureDTO);
        }
    }
}
