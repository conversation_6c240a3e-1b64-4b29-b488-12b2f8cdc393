/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/4/10 17:22
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseWeatherService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/10 
 * @since 1.0.0
 */
public abstract class AbstractBaesWeatherServiceImpl extends BaseServiceImpl implements BaseWeatherService {

    @Autowired
    private CityService cityService;

//    public CityService getCityService() {
//        return (CityService) SpringContextManager.getApplicationContext().getBean("cityService");
//    }

    @Override
    public List<WeatherDTO> findWeatherDTO(String cityId, Integer type, Date startDate, Date endDate) {
        try {
            if (WeatherEnum.EFFECTIVE_TEMPERATURE.getType().equals(type) || WeatherEnum.COLD_DAMPNESS.getType().equals(type)) {
                List<? extends BaseWeatherDO> weatherVOS = findStatisticsSynthesizeWeatherCityDayVO(cityId,type,startDate,endDate);
                List<WeatherDTO> weatherDTOS = getWeatherDTOS(weatherVOS);
                if (weatherDTOS != null) {
                    return weatherDTOS;
                }
            }
            List<? extends BaseWeatherDO> weatherCityVOS = findWeatherCityVO(cityId, type, startDate, endDate);
            List<WeatherDTO> weatherDTOS = getWeatherDTOS(weatherCityVOS);
            if (weatherDTOS != null) {
                return weatherDTOS;
            }
            return new ArrayList<>();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e.getMessage());
        }
    }

    protected abstract List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    protected abstract List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type, Date startDate, Date endDate) throws Exception;


    private List<WeatherDTO> getWeatherDTOS(List<? extends BaseWeatherDO> weatherVOS) throws Exception {
        if (CollectionUtils.isNotEmpty(weatherVOS)) {
            List<WeatherDTO> weatherDTOS = new ArrayList<>(weatherVOS.size());
            Map<String, CityDO> cityDOMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getId, Function.identity()));
            for (BaseWeatherDO weatherVO : weatherVOS) {
                WeatherDTO weatherDTO = wrapWeatherDTO(weatherVO, cityDOMap);
                weatherDTOS.add(weatherDTO);
            }
            return weatherDTOS;
        }
        return null;
    }


    private WeatherDTO wrapWeatherDTO(BaseWeatherDO weatherVO, Map<String, CityDO> cityDOMap) throws Exception {
        WeatherDTO weatherDTO = new WeatherDTO();
        weatherDTO.setId(weatherVO.getId());
        weatherDTO.setDate(weatherVO.getDate());
        weatherDTO.setWeek(DateUtil.getWeek(weatherVO.getDate()));
        weatherDTO.setCity(cityDOMap.get(weatherVO.getCityId()).getCity());
        weatherDTO.setCityId(weatherVO.getCityId());
        weatherDTO.setData(BasePeriodUtils.toList(weatherVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
        return weatherDTO;
    }
}
