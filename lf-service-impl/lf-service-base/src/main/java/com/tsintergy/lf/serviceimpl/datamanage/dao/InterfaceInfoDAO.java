package com.tsintergy.lf.serviceimpl.datamanage.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.InterfaceInfoDO;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: InterfaceInfoDAO.java, v 0.1 2018-04-02 14:17:39 tao Exp $$
 */
@Component
public class InterfaceInfoDAO extends BaseAbstractDAO<InterfaceInfoDO> {

    /**
     * 查询接口采集状态
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public List<InterfaceInfoDO> getInterfaceInfoVO(Date startDate, Date endDate) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (null != startDate) {
            param.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        param.addOrderByDesc("date");
        return this.query(param.build()).getDatas();
    }


}