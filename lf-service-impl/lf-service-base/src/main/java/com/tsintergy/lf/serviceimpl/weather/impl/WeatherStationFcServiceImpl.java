package com.tsintergy.lf.serviceimpl.weather.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsintergy.lf.serviceapi.base.industry.pojo.ExceptionIndustryCityDayHisStatsDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationFcDAO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class WeatherStationFcServiceImpl implements WeatherStationFcService {

    @Resource
    WeatherStationFcDAO weatherStationFcDAO;

    @Override
    public List<WeatherStationFcDO> findByDateAndType(String stationId, Integer type, Date date) {
        return weatherStationFcDAO.selectList(new LambdaQueryWrapper<WeatherStationFcDO>()
                .eq(WeatherStationFcDO::getStationId, stationId)
                .eq(WeatherStationFcDO::getType, type)
                .eq(WeatherStationFcDO::getDate, date));
    }

    @Override
    public void saveOrUpdate(WeatherStationFcDO weatherStationFcDO) {
        weatherStationFcDAO.saveOrUpdateBatch(Arrays.asList(weatherStationFcDO));

    }

    @Override
    public List<WeatherStationFcDO> findByCityAndType(String cityId, Integer type, Date startDate, Date endDate) {
        return weatherStationFcDAO.selectList(new LambdaQueryWrapper<WeatherStationFcDO>()
                .eq(StringUtils.isNotEmpty(cityId), WeatherStationFcDO::getCityId, cityId)
                .eq(type != null, WeatherStationFcDO::getType, type)
                .ge(startDate != null, WeatherStationFcDO::getDate, startDate)
                .le(endDate != null,WeatherStationFcDO::getDate, endDate));
    }


}
