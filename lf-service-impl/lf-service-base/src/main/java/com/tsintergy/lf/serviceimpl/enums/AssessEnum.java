/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/10/12 9:03 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.enums;

/**
 * 综合相似日 计算方法枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum AssessEnum {

    /**
     * 数值相似
     */
    MAX(1, "max"),
    /**
     * 最小
     */
    MIN(2, "min"),
    /**
     * 平均
     */
    AVG(3, "avg");


    private Integer type;

    /**
     * 后端数值
     */
    private String value;


    AssessEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}