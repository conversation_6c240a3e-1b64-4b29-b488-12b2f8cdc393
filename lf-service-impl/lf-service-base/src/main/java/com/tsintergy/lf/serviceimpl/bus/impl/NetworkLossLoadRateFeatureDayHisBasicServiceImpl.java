package com.tsintergy.lf.serviceimpl.bus.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.bus.api.NetworkLossLoadRateFeatureDayHisBasicService;
import com.tsintergy.lf.serviceapi.base.bus.pojo.NetworkLossLoadRateFeatureDayHisBasicDO;
import com.tsintergy.lf.serviceimpl.bus.dao.NetworkLossLoadRateFeatureDayHisBasicDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@DataSource("buslf")
@Service
public class NetworkLossLoadRateFeatureDayHisBasicServiceImpl implements NetworkLossLoadRateFeatureDayHisBasicService {

    @Autowired
    NetworkLossLoadRateFeatureDayHisBasicDAO dao;

    @Override
    public void saveOrUpdateBatch(List<NetworkLossLoadRateFeatureDayHisBasicDO> result) {
        dao.saveOrUpdateBatchByTemplate(result);
    }

    @Override
    public List<NetworkLossLoadRateFeatureDayHisBasicDO> getListByDate(List<Date> dateList) {

        return  dao.findAll(JpaWrappers.<NetworkLossLoadRateFeatureDayHisBasicDO>lambdaQuery()
                .in(NetworkLossLoadRateFeatureDayHisBasicDO::getDateTime,dateList));
    }
}
