package com.tsintergy.lf.serviceimpl.forecast.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 14:18
 */
@Component
public class LoadCityFcBatchDAO extends BaseAbstractDAO<LoadCityFcBatchDO> {

    public List<LoadCityFcBatchDO> selectList(Date date, String cityId, String algorithmId, String caliberId,
        Boolean report) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != date) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != report) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", report);
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> delete(Date date, String cityId, String algorithmId, String caliberId,
         Integer batchId) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != date) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != batchId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "batchId", batchId);
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(weatherCityFcLoadForecastDOS)) {
            for (LoadCityFcBatchDO one : weatherCityFcLoadForecastDOS) {
                this.deleteByPk(one.getId());
            }
        }
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> selectListByBatchIds(Date startDate, Date endDate, String cityId, List<String> algorithmId,
        String caliberId, List<Integer> batchIds) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != startDate) {
            java.sql.Date value = new java.sql.Date(startDate.getTime());
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", value);
        }
        if (null != endDate) {
            java.sql.Date value = new java.sql.Date(endDate.getTime());
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", value);
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != batchIds) {
            dbQueryParamBuilder.where(QueryOp.NumberIsIn, "batchId", batchIds);
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> selectList(Date startDate, Date endDate, String cityId,
        String caliberId, String algorithmId, Boolean report) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != startDate) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != report) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", report);
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> selectListByBatchId(Date startDate, Date endDate, String cityId,
        String caliberId, String algorithmId, Integer batchId) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != startDate) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != batchId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "batchId", batchId);
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> selectListByBatchId(Date date, String cityId, String caliberId, String algorithmId,
                                                       String batchId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != date) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != batchId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "batchId", Integer.valueOf(batchId));
        }
        List<LoadCityFcBatchDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<LoadCityFcBatchDO> selectListByCreateTime(String cityId, Date forecastDate, String caliberId, String algorithmId,Boolean report) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != forecastDate) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "createtime", new java.sql.Date(forecastDate.getTime()));
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "createtime", new java.sql.Date(DateUtil.getMoveDay(forecastDate,1).getTime()));
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "cityId", cityId);
        }

        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != report) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", report);
        }
        return this.query(dbQueryParamBuilder.build()).getDatas();

    }
}
