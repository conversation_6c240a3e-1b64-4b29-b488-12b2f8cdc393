package com.tsintergy.lf.serviceimpl.forecast.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.LoadBatchQueryDTO;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.LoadDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcBatchDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 14:30
 */
@Service
public class LoadCityFcBatchServiceImpl implements LoadCityFcBatchService {

    @Autowired
    private LoadCityFcBatchDAO loadCityFcBatchDAO;

    @Autowired
    private LoadCityHisDAO loadCityHisDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Override
    public void doSave(LoadCityFcDO loadCityFcDO) throws Exception {
        LoadCityFcBatchDO loadCityFcBatchDO = new LoadCityFcBatchDO();
        BeanUtils.copyProperties(loadCityFcDO, loadCityFcBatchDO, "id");
        //此处的创建时间和修改时间都是 load_city_fc_basic的时间, 所以updateTime才是此批次的预测时间
        loadCityFcBatchDO.setCreatetime(loadCityFcBatchDO.getUpdatetime() == null ? loadCityFcBatchDO.getCreatetime()
            : loadCityFcBatchDO.getUpdatetime());
        //防止出现相同批次号
        synchronized (this) {
            List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchDAO.selectList(loadCityFcBatchDO.getDate(),
                    loadCityFcBatchDO.getCityId(),loadCityFcBatchDO.getAlgorithmId(), loadCityFcBatchDO.getCaliberId(),null);
            if (CollectionUtils.isEmpty(loadCityFcBatchDOS)) {
                loadCityFcBatchDO.setBatchId(1);
            } else {
                Integer batchId = loadCityFcBatchDOS.stream().max(Comparator.comparing(LoadCityFcBatchDO::getBatchId)).get().getBatchId();
                loadCityFcBatchDO.setBatchId(++batchId);
            }
            loadCityFcBatchDAO.save(loadCityFcBatchDO);
            //预测完成保存预测使用的预测气象数据
            weatherCityFcLoadForecastService
                .insertOrUpdateWeatherFcInfo(loadCityFcBatchDO.getCityId(), loadCityFcBatchDO.getDate(),
                    loadCityFcBatchDO.getAlgorithmId(), loadCityFcBatchDO.getCaliberId(), loadCityFcBatchDO.getBatchId(), loadCityFcBatchDO.getCreatetime());
            //预测完成保存预测使用的实际气象数据
            weatherCityFcLoadForecastService
                .insertOrUpdateWeatherHisInfo(loadCityFcBatchDO.getCityId(), loadCityFcBatchDO.getDate(),
                    loadCityFcBatchDO.getAlgorithmId(), loadCityFcBatchDO.getCaliberId(), loadCityFcBatchDO.getBatchId(), loadCityFcBatchDO.getCreatetime());
        }


    }

    @Override
    public void doSaveData(LoadCityFcDO loadCityFcDO) throws Exception {
        LoadCityFcBatchDO loadCityFcBatchDO = new LoadCityFcBatchDO();
        BeanUtils.copyProperties(loadCityFcDO, loadCityFcBatchDO, "id");
        //此处的创建时间和修改时间都是 load_city_fc_basic的时间, 所以updateTime才是此批次的预测时间
        loadCityFcBatchDO.setCreatetime(loadCityFcBatchDO.getUpdatetime() == null ? loadCityFcBatchDO.getCreatetime()
                : loadCityFcBatchDO.getUpdatetime());
        //防止出现相同批次号
        synchronized (this) {
            List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchDAO.selectList(loadCityFcBatchDO.getDate(),
                    loadCityFcBatchDO.getCityId(),loadCityFcBatchDO.getAlgorithmId(), loadCityFcBatchDO.getCaliberId(),null);
            if (CollectionUtils.isEmpty(loadCityFcBatchDOS)) {
                loadCityFcBatchDO.setBatchId(1);
            } else {
                Integer batchId = loadCityFcBatchDOS.stream().max(Comparator.comparing(LoadCityFcBatchDO::getBatchId)).get().getBatchId();
                loadCityFcBatchDO.setBatchId(++batchId);
            }
            loadCityFcBatchDAO.save(loadCityFcBatchDO);
        }
    }

    @Override
    public List<LoadCityFcBatchDO> findDataList(Date date, String cityId, String algorithmId, String caliberId,
        Boolean report) throws Exception{
        return loadCityFcBatchDAO.selectList(date, cityId, algorithmId, caliberId, report);
    }

    @Override
    public void doDelete(List<LoadCityFcBatchDO> data) throws Exception{
        for (LoadCityFcBatchDO one : data) {
            loadCityFcBatchDAO.delete(one);;
        }
    }

    @Override
    public void doSaveOrUpdate(LoadCityFcBatchDO data) throws Exception{
        loadCityFcBatchDAO.saveOrUpdateByTemplate(data);
    }


    @Override
    public List<LoadCityFcBatchDO> findByCondition(String cityId, Date date, String caliberId, String algorithmId)
        throws Exception {
        List<LoadCityFcBatchDO> result = new ArrayList<>();
        if (AlgorithmEnum.RESULT_REPORT.getId().equals(algorithmId)) {
            //最终上报包括自动上报以及人工手动上报结果
            List<LoadCityFcBatchDO> autoReportList = loadCityFcBatchDAO
                .selectList(date, cityId, caliberId, AlgorithmEnum.FORECAST_MODIFY.getId(), true);
            if (!CollectionUtils.isEmpty(autoReportList)) {
                result.addAll(autoReportList);
            }
            List<LoadCityFcBatchDO> modifyReportList = loadCityFcBatchDAO
                .selectList(date, cityId, caliberId, AlgorithmEnum.FORECAST_MODIFY.getId(), null);
            if (!CollectionUtils.isEmpty(modifyReportList)) {
                result.addAll(modifyReportList);
            }
            if (!CollectionUtils.isEmpty(result)) {
                AtomicInteger num = new AtomicInteger(1);
                result.forEach(loadCityFcBatchDO -> loadCityFcBatchDO.setBatchId(num.getAndIncrement()));
            }

        } else {
            result = loadCityFcBatchDAO
                .selectList(date, cityId, caliberId, algorithmId, null);
        }
        return result;
    }

    /**
     * 预测查询--负荷预测
     */
    @Override
    public LoadBatchQueryDTO findLoad(List<Integer> batchIds, List<String> algorithmIds, String cityId,
        String caliberId,
        Date startDate, Date endDate)
        throws Exception {
        LoadBatchQueryDTO queryDTO = new LoadBatchQueryDTO();
        //真实数据
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        //查询算法批次数据
        List<LoadCityFcBatchDO> fcVOS = loadCityFcBatchDAO
            .selectListByBatchIds(startDate, endDate, cityId, algorithmIds, caliberId, batchIds);
        if (LoadCityHisDOS.size() < 1 && fcVOS.size() < 1) {
            return null;
        }
        //分别转map
        Map<Date, LoadCityHisDO> realMap = LoadCityHisDOS.stream()
            .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        //key 批次id-算法id
        Map<String, List<LoadCityFcBatchDO>> fcMap = fcVOS.stream().collect(Collectors.groupingBy(
            src -> src.getBatchId() + Constants.SEPARATOR_PUNCTUATION
                + src.getAlgorithmId()));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> real = new ArrayList<>();
        for (Date date : dateList) {
            LoadCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                real.addAll(BasePeriodUtils
                    .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                real.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        List<LoadDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<LoadCityFcBatchDO>> entry : fcMap.entrySet()) {
            List<LoadCityFcBatchDO> value = entry.getValue();
            Map<Date, LoadCityFcBatchDO> collect = value.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (key1, key2) -> key2));
            LoadDTO result = new LoadDTO();
            List<BigDecimal> fc = new ArrayList<>();
            for (Date date : dateList) {
                LoadCityFcBatchDO fcBatch = collect.get(date);
                if (fcBatch != null) {
                    fc.addAll(BasePeriodUtils
                        .toList(fcBatch, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                } else {
                    fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
                }
            }
            result.setFc(fc);
            String[] key = entry.getKey().split(Constants.SEPARATOR_PUNCTUATION);
            AlgorithmEnum byId = AlgorithmEnum.findById(key[1]);
            result.setAlgorithmBatchName(byId == null ? null : byId.getDescription() + "-第" + key[0] + "批次");

            resultList.add(result);
        }
        queryDTO.setFcList(resultList);
        queryDTO.setReal(real);
        return queryDTO;
    }

    @Override
    public LoadBatchQueryDTO findWeather(List<Integer> batchIds, List<String> algorithmIds, String srcCityId,
        String caliberId, Date startDate, Date endDate, Integer type) throws Exception {
        String cityId = cityService.findWeatherCityId(srcCityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService
            .findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<WeatherCityFcLoadForecastDO> fcVOS = weatherCityFcLoadForecastService
            .findWeatherInfoFcLoadForecastBatch(srcCityId, type, algorithmIds, batchIds, startDate, endDate, caliberId);
        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return null;
        }
        //转map
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream()
            .collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Integer, List<WeatherCityFcLoadForecastDO>> fcMap = fcVOS.stream().collect(Collectors.groupingBy(
            WeatherCityFcLoadForecastDO::getBatchId));

        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        LoadBatchQueryDTO dataResp = new LoadBatchQueryDTO();

        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils
                    .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        List<LoadDTO> resultList = new ArrayList<>();
        for (Map.Entry<Integer, List<WeatherCityFcLoadForecastDO>> entry : fcMap.entrySet()) {
            List<WeatherCityFcLoadForecastDO> value = entry.getValue();
            Map<Date, WeatherCityFcLoadForecastDO> collect = value.stream()
                .collect(
                    Collectors.toMap(WeatherCityFcLoadForecastDO::getDate, Function.identity(), (key1, key2) -> key2));
            LoadDTO result = new LoadDTO();
            List<BigDecimal> fc = new ArrayList<>();
            for (Date date : dateList) {
                WeatherCityFcLoadForecastDO fcBatch = collect.get(date);
                if (fcBatch != null) {
                    fc.addAll(BasePeriodUtils
                        .toList(fcBatch, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                } else {
                    fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
                }
            }
            result.setFc(fc);
            result.setAlgorithmBatchName("预测气象-第" + entry.getKey() + "批次");
            resultList.add(result);
        }
        dataResp.setFcList(resultList);
        dataResp.setReal(his);
        return dataResp;
    }

    @Override
    public List<LoadCityFcBatchDO> findByCondition(String cityId, Date startDate, Date endDate, String caliberId,
        String algorithmId) throws Exception {
        List<LoadCityFcBatchDO> result = new ArrayList<>();
        if (AlgorithmEnum.RESULT_REPORT.getId().equals(algorithmId)) {
            //最终上报包括自动上报以及人工手动上报结果
            List<LoadCityFcBatchDO> modifyReportList = loadCityFcBatchDAO
                .selectList(startDate, endDate, cityId, caliberId, AlgorithmEnum.FORECAST_MODIFY.getId(), null);
            if (!CollectionUtils.isEmpty(modifyReportList)) {
                result.addAll(modifyReportList);
            }
        } else {
            result = loadCityFcBatchDAO.selectList(startDate, endDate, cityId, caliberId, algorithmId, null);
        }
        return result;
    }

    @Override
    public List<LoadCityFcBatchDO> findByAllAlgorithmCondition(String cityId, Date startDate, Date endDate,
        String caliberId, List<String> algorithmId) throws Exception {
        List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchDAO.selectListByBatchIds(startDate, endDate, cityId,
            algorithmId, caliberId, null);
        return loadCityFcBatchDOS;
    }

    @Override
    public List<LoadCityFcBatchDO> findByConditionByBatchId(String cityId, Date startDate, Date endDate,
        String caliberId, String algorithmId, Integer batchId) throws Exception {
        return loadCityFcBatchDAO.selectListByBatchId(startDate, endDate, cityId, caliberId, algorithmId, batchId);

    }

    @Override
    public LoadCityFcBatchDO findOneByConditionByBatchId(String cityId, Date date, String caliberId,
                                                         String algorithmId, String batchId, Integer day) throws Exception {
        List<LoadCityFcBatchDO> byConditionByBatchId = loadCityFcBatchDAO.selectListByBatchId( date, cityId,
                caliberId, algorithmId, null);
        if (CollectionUtil.isEmpty(byConditionByBatchId)){
            return null;
        }
        List<LoadCityFcBatchDO> result = new ArrayList<>();
        SettingBatchInitDO batch = settingBatchInitService.getBatchById(batchId);
        for (LoadCityFcBatchDO loadCityFcBatchDO : byConditionByBatchId){
            if (DateUtil
                    .isWithinTimeRange(loadCityFcBatchDO.getCreatetime(), batch.getStartTime(), batch.getEndTime())){
                result.add(loadCityFcBatchDO);
            }
        }
        if (CollectionUtil.isEmpty(result)){
            return null;
        }
        List<LoadCityFcBatchDO> resultFilterByDay = result.stream().filter(src -> {
            Date anObject = DateUtils.addDays(src.getDate(), (-day));
            return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(resultFilterByDay)){
            return null;
        }
        Optional<LoadCityFcBatchDO> resultDO = resultFilterByDay.stream().sorted(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed()).findFirst();
        if (resultDO.isPresent()){
            return resultDO.get();
        }
        return null;
    }

    private List<BigDecimal> getList(LoadCityFcBatchDO LoadCityFcDO) throws Exception {
        if (LoadCityFcDO != null) {
            return BasePeriodUtils
                .toList(LoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        } else {
            return LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM);
        }
    }

    @Override
    public List<LoadCityFcBatchDO> findOneByBatchTime(String cityId, Date startDate, Date endDate, String caliberId,
                                                      String algorithmId, String batchId) throws Exception {
        List<LoadCityFcBatchDO> tarList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<LoadCityFcBatchDO> srcList = loadCityFcBatchDAO.selectListByBatchId(startDate, endDate, cityId,
                caliberId, algorithmId, null);
        List<LoadCityFcBatchDO> collect = srcList.stream()
                .filter(one -> {
                    // 条件1：在批次时间范围内
                    boolean inBatchTime = DateUtil.isWithinTimeRange(
                            one.getCreatetime(),
                            batchById.getStartTime(),
                            batchById.getEndTime()
                    );
                    if (!inBatchTime) {
                        return false;
                    }

                    // 条件2：创建时间的日期部分在数据日期之前
                    Date dataDate = one.getDate();
                    Date createTime = one.getCreatetime();
                    Date createDate = DateUtils.truncate(createTime, Calendar.DATE);
                    Date targetDate = DateUtils.truncate(dataDate, Calendar.DATE);
                    return createDate.before(targetDate);
                })
                .collect(Collectors.toList());
        Map<Date, List<LoadCityFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                LoadCityFcBatchDO::getDate, TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

    @Override
    public List<LoadCityFcBatchDO> findByBatchTime(String cityId, Date startDate, Date endDate, String caliberId, List<String> algorithmIds, String batchId) throws Exception {
        List<LoadCityFcBatchDO> tarList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<LoadCityFcBatchDO> srcList = loadCityFcBatchDAO.selectListByBatchIds(startDate, endDate, cityId, algorithmIds, caliberId, null);
        List<LoadCityFcBatchDO> collect = srcList.stream()
                .filter(one -> {
                    // 条件1：在批次时间范围内
                    boolean inBatchTime = DateUtil.isWithinTimeRange(
                            one.getCreatetime(),
                            batchById.getStartTime(),
                            batchById.getEndTime()
                    );
                    if (!inBatchTime) {
                        return false;
                    }

                    // 条件2：创建时间的日期部分在数据日期之前
                    Date dataDate = one.getDate();
                    Date createTime = one.getCreatetime();
                    Date createDate = DateUtils.truncate(createTime, Calendar.DATE);
                    Date targetDate = DateUtils.truncate(dataDate, Calendar.DATE);
                    return createDate.before(targetDate);
                })
                .collect(Collectors.toList());
        Map<String, List<LoadCityFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate() + "+" + src.getAlgorithmId(), TreeMap::new, Collectors.toList()));
        //获取批次时间段内最新的一条数据
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

    @Override
    public List<LoadCityFcBatchDO> findLoadByCreateTime(String cityId, Date forecastDate, String caliberId, String algorithmId, String batchId,Boolean report) {
        List<LoadCityFcBatchDO> fcBatchDOS = loadCityFcBatchDAO.selectListByCreateTime(cityId, forecastDate, caliberId, algorithmId,report);
        SettingBatchInitDO batch = settingBatchInitService.getBatchById(batchId);
        //过滤对应批次
        return fcBatchDOS.stream().filter(t -> DateUtil
                .isWithinTimeRange(t.getCreatetime(), batch.getStartTime(), batch.getEndTime())).collect(Collectors.toList());
    }
}
