/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司
 * Author:   lixiaopeng
 * Date:    2018/11/2115:26
 * History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceimpl.typhoon.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonHisService;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonHisDO;
import com.tsintergy.lf.serviceimpl.typhoon.dao.TyphoonHisDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description:<br>
 *
 * <AUTHOR>
 * @create2018/11/21
 * @since1.0.0
 */
@Service(value = "typhoonHisService")
public class TyphoonHisServiceImpl extends BaseServiceImpl implements TyphoonHisService {

    @Autowired
    TyphoonHisDAO typhoonHisDao;
    @Override
    public void doUpdate(TyphoonHisDO typhoonHisVO) throws Exception {
        typhoonHisDao.update(typhoonHisVO);
    }

    @Override
    public List<TyphoonHisDO> findAll() throws Exception {
        return (List<TyphoonHisDO>) typhoonHisDao.findAll();
    }

    @Override
    public List<TyphoonHisDO> findByTyphoonId(String typhoonId) throws Exception {
        return null;
    }


    @Override
    public void doInsert(TyphoonHisDO typhoonHisVO) throws Exception {
        try {
            typhoonHisDao.create(typhoonHisVO);
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }


    @Override
    public List<TyphoonHisDO> listTyphoonHisVO(Date startDate, Date endDate) throws Exception {
       return typhoonHisDao.listTyphoonHisVOs(startDate, endDate);
    }
}


