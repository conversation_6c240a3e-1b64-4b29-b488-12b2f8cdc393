/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.recall.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.AccuracyLoadFcRecallDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.api.StatisticsCityDayFcRecallService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceimpl.recall.dao.StatisticsCityDayFcRecallDAO;
import com.tsintergy.lf.serviceimpl.system.constant.SystemConstant;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:27
 * @Version: 1.0.0
 */

@Service("statisticsCityDayFcRecallService")
public class StatisticsCityDayFcRecallServiceImpl implements StatisticsCityDayFcRecallService {

    @Autowired
    StatisticsCityDayFcRecallDAO statisticsCityDayFcRecallDAO;

    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    StatisticsCityDayFcRecallService statisticsCityDayFcRecallService;

    @Autowired
    WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    SettingSystemService settingSystemService;


    @Override
    public StatisticsAccuracyDTO getStatisticsAccuracyDTO(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) {
        try {
            StatisticsAccuracyDTO statisticsAccuracyDTO = new StatisticsAccuracyDTO();
            List<AccuracyLoadFcRecallDTO> recallDTOS = accuracyLoadCityFcRecallService.getDTOS(cityId, caliberId, startDate, endDate, algorithmId);

            ArrayList<BigDecimal> accuracyFcList = new ArrayList<>();
            for (AccuracyLoadFcRecallDTO accuracyFc : recallDTOS) {
                if (accuracyFc.getFcAccuracy() != null) {
                    accuracyFcList.add(accuracyFc.getFcAccuracy());
                }
            }
            if (accuracyFcList.size() != 0) {
//                BigDecimal accuracyFc = BigDecimalFunctions.listAvg(accuracyFcList);
                BigDecimal accuracyFc = BigDecimalUtils.avgList(accuracyFcList, 2, false);
                if (accuracyFc != null) {
                    statisticsAccuracyDTO.setFcAccuracy(accuracyFc);
                }
            }

            ArrayList<BigDecimal> accuracyRecallList = new ArrayList<>();
            for (AccuracyLoadFcRecallDTO accuracyRecall : recallDTOS) {
                if (accuracyRecall.getRecallAccuracy() != null) {
                    accuracyRecallList.add(accuracyRecall.getRecallAccuracy());
                }
            }
            if (accuracyRecallList.size() != 0) {
//                BigDecimal accuracyRecall = BigDecimalFunctions.listAvg(accuracyRecallList);
                BigDecimal accuracyRecall = BigDecimalUtils.avgList(accuracyRecallList, 2, false);

                if (accuracyRecall != null) {
                    statisticsAccuracyDTO.setRecallAccuracy(accuracyRecall);
                }
            }

            int areaLhZeroCount = 0;
            int areaZeroToOneCount = 0;
            int areaGhOneCount = 0;
            int notEqualsNullNum = 0;
            ArrayList<BigDecimal> recallDeviationList = new ArrayList<>();
            for (AccuracyLoadFcRecallDTO accuracyLoadFcRecallDTO : recallDTOS) {
                recallDeviationList.add(accuracyLoadFcRecallDTO.getRecallDeviation());
            }

            for (BigDecimal recallDeviation : recallDeviationList) {
                if (recallDeviation != null) {
                    notEqualsNullNum++;
                    if (recallDeviation.compareTo(new BigDecimal("0")) == -1) {
                        areaLhZeroCount++;
                    } else if (recallDeviation.compareTo(new BigDecimal("0.01")) == 1) {
                        areaGhOneCount++;
                    } else {
                        areaZeroToOneCount++;
                    }
                }
            }

            if (notEqualsNullNum == 0) {
                areaLhZeroCount = 0;
                areaZeroToOneCount = 0;
                areaGhOneCount = 0;
                notEqualsNullNum = 1;
            }
            BigDecimal area1 = BigDecimal.valueOf((int) areaLhZeroCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
            BigDecimal area2 = BigDecimal.valueOf((int) areaZeroToOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
            BigDecimal area3 = BigDecimal.valueOf((int) areaGhOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
            if (area1 != null) {
                statisticsAccuracyDTO.setArea1(area1);
            }
            if (area2 != null) {
                statisticsAccuracyDTO.setArea2(area2);
            }
            if (area3 != null) {
                statisticsAccuracyDTO.setArea3(area3);
            }

            int standard = 0;
            String dayScaleValue = settingSystemService.getValue(SystemConstant.DAY_SCALE_VALUE);
            BigDecimal standardDayScale = null;
            if (StringUtils.isNotEmpty(dayScaleValue)){
                standardDayScale = new BigDecimal(dayScaleValue);
            }else {
                standardDayScale = new BigDecimal("0.3");
            }
            String showInfo = "";
            int peakFcDeviationGtOneDay = 0;
            for (AccuracyLoadFcRecallDTO accuracyLoadFcRecallDTO : recallDTOS) {
                BigDecimal recallDeviation = accuracyLoadFcRecallDTO.getRecallDeviation();
                if (recallDeviation != null) {
                    if (recallDeviation.compareTo(new BigDecimal("0")) == 1) {
                        peakFcDeviationGtOneDay++;
                        BigDecimal highestTemperatureFcDeviation = accuracyLoadFcRecallDTO.getHighestTemperatureFcDeviation();
                        if (highestTemperatureFcDeviation != null && highestTemperatureFcDeviation.abs().compareTo(new BigDecimal("0.5")) == 1) {
                            standard++;
                        }
                    }
                }
            }

            if (peakFcDeviationGtOneDay == 0) {
                standard = 0;
                peakFcDeviationGtOneDay = 1;
            }
            BigDecimal dayScale = BigDecimal.valueOf(standard).divide(BigDecimal.valueOf(peakFcDeviationGtOneDay), 4,
                RoundingMode.DOWN);
            SimpleDateFormat stf = new SimpleDateFormat("yyyy-MM-dd");
            String beginTime = stf.format(startDate);
            String endTime = stf.format(endDate);
            if (dayScale.compareTo(standardDayScale) > -1) {
                String value = dayScale.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString();
                showInfo = beginTime + "至" + endTime + "期间，由于气象预报偏差导致的预测精度下降天数占比为" + value + "%" + "，请提升气象预报质量!";
            } else {
                showInfo = beginTime + "至" + endTime + "期间，气象预报无明显偏差，请优化预测算法!";
            }
            statisticsAccuracyDTO.setShowInfo(showInfo);
            return statisticsAccuracyDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<StatisticsCityDayFcRecallDO> queryStatisticsCity(String cityId, String algorithmId, String
        caliberId, Date date, Boolean report) throws Exception {
        return statisticsCityDayFcRecallDAO.getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, date, date, report);
    }

    @Override
    public List<StatisticsCityDayFcRecallDO> doSaveOrUpdateBatch(
        List<StatisticsCityDayFcRecallDO> statisticsCityDayFcVOS) throws Exception {
        return statisticsCityDayFcRecallDAO.doSaveOrUpdateStatisticsCityDayFcDOs(statisticsCityDayFcVOS);
    }

    @Override
    public List<StatisticsCityDayFcRecallDO> getStatisticsCityDayFcRecallDOList(String cityId, String
        caliberId, Date startDate, Date endDate, String algorithmId) throws Exception {
        return statisticsCityDayFcRecallDAO.getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
    }

}