package com.tsintergy.lf.serviceimpl.load.impl;


import com.alibaba.fastjson.JSON;
import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcShortService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityWeekFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.IndexLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureCityMonthDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFc288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.*;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 负荷特性统计接口实现 User:taojingui Date:18-2-23 Time:下午12:04
 */
@Service("loadFeatureStatService")
public class LoadFeatureStatServiceImpl extends BaseServiceImpl implements LoadFeatureStatService {

    private static Logger logger = LoggerFactory.getLogger(LoadFeatureStatServiceImpl.class);

    @Autowired
    private LoadFeatureCityWeekFcService loadFeatureCityWeekFcService;

    @Autowired
    private LoadCityHisDAO loadCityHisDAO;

    @Autowired
    private LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;


    @Autowired
    private LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;


    @Autowired
    private LoadFeatureCityWeekHisDAO loadFeatureCityWeekHisDAO;

    @Autowired
    private LoadFeatureCityMonthHisDAO loadFeatureCityMonthHisDAO;

    @Autowired
    private LoadFeatureCityYearHisDAO loadFeatureCityYearHisDAO;

    @Autowired
    private LoadFeatureCityQuarterHisDAO loadFeatureCityQuarterHisDAO;

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private LoadFeatureCityDayFcService loadFeatureCityDayFcService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityFc288DAO loadCityFc288DAO;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    private LoadFeatureCityDayFcShortService loadFeatureCityDayFcShortService;

    @Autowired
    private LoadCityHisBasicByMinuteDAO loadCityHisBasicByMinuteDAO;

    @Override
    public List<LoadFeatureCityDayHisDO> doStatLoadFeatureCityDay(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception {
        try {
            List<LoadCityHisDO> LoadCityHisDOs = loadCityHisDAO
                .getLoadCityHisDOSByCityIds(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOs = (List<LoadFeatureCityDayHisDO>) doStatLoadFeatureCityDay(
                LoadCityHisDOs);
            return loadFeatureCityDayHisDAO.doSaveOrUpdateLoadFeatureCityDayHisDOs(LoadFeatureCityDayHisDOs);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-实际-日-负荷特性出错了", e);
        }
    }

    @Override
    public void doStatLoadFeatureCityDayClctHisSecond(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception {
        List<LoadCityHisBasicByMinuteDO> secondList = this.loadCityHisBasicByMinuteDAO
            .selectListByDate(cityIds, caliberId, startDate, endDate);
        //数据源是从0000开始的；
        List<String> allTimeListBySecond = DateUtil.getAllTimeListByInterval(2, 1, true);
        List<List<Integer>> peakTimes;
        List<List<Integer>> troughTimes;
        try {
            peakTimes = settingSystemDAO.getPeakTimesBackMinuteSecond(allTimeListBySecond);
            troughTimes = settingSystemDAO.getTroughTimesBackMinuteSecond(allTimeListBySecond);
        } catch (Exception e) {
            e.printStackTrace();
            throw TsieExceptionUtils.newBusinessException("数据库查询错误");
        }
        secondList.forEach(one -> {
            //一天的负荷数据；间隔60s一个点 1440
            List<BigDecimal> hisLoad = JSON.parseArray(one.getLoadData(), BigDecimal.class);
//            List<BigDecimal> hisLoad = Arrays.asList(load.split(Constants.SEPARATOR_PUNCTUATION)).stream()
//                .map(BigDecimal::new).collect(Collectors.toList());

            Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(hisLoad, 4);
            List<BigDecimal> peaks = new ArrayList<BigDecimal>();
            List<BigDecimal> troughs = new ArrayList<BigDecimal>();

            if (peakTimes != null) {
                for (List<Integer> peak : peakTimes) {
                    if (hisLoad.size() > peak.get(0)) {
                        peaks.addAll(hisLoad.subList(peak.get(0), Math.min(peak.get(1), hisLoad.size())));
                    }
                }
            }
            if (troughTimes != null) {
                for (List<Integer> trough : troughTimes) {
                    if (hisLoad.size() > trough.get(0)) {
                        troughs.addAll(hisLoad.subList(trough.get(0), Math.min(trough.get(1), hisLoad.size())));
                    }
                }
            }
            LoadFeatureCityDayHisDO statDO = new LoadFeatureCityDayHisDO();
            statDO.setMaxLoad(maxMixAvg.get("max"));
            statDO.setMaxTime(allTimeListBySecond.get(hisLoad.indexOf(statDO.getMaxLoad())).substring(0, 5));
            statDO.setMinLoad(maxMixAvg.get("min"));
            statDO.setMinTime(allTimeListBySecond.get(hisLoad.indexOf(statDO.getMinLoad())).substring(0, 5));
            statDO.setAveLoad(maxMixAvg.get("avg"));
            statDO.setCityId(one.getCityId());
            statDO.setDate(one.getDate());
            statDO.setCaliberId(one.getCaliberId());
            // 峰谷差 = 日最大负荷 – 日最小负荷
            statDO
                .setDifferent(BigDecimalUtils.sub(statDO.getMaxLoad(), statDO.getMinLoad()));
            if (statDO.getMaxLoad() != null && statDO.getMaxLoad().intValue() != 0) {
                // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
                statDO.setGradient(
                    BigDecimalUtils.divide(statDO.getDifferent(), statDO.getMaxLoad(), 4));
                // 负荷率 = 日平均负荷/日最大负荷
                statDO.setLoadGradient(
                    BigDecimalUtils.divide(statDO.getAveLoad(), statDO.getMaxLoad(), 4));
            }
            // 尖峰平均负荷 = average(尖峰时段负荷）。尖峰时段后台可配，默认值为5:30~7:00，10:00~12:00，18：00~20:00
            statDO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
            // 低谷平均负荷 = average（低谷时段负荷）。低谷时段可配，默认值为2:00~5:00，12:00~14:00，22:00~0:00
            statDO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
            // 日电量 = 96点负荷之和/4  (1分钟间隔 共1440个点，分母要*15)
            statDO
                .setEnergy(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(hisLoad), new BigDecimal(4 * 15), 4));
            try {
                loadFeatureCityDayHisDAO.doSaveOrUpdateLoadFeatureCityDayHisVO(statDO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }


    @Override
    public void doStatLoadFeatureCityDayFc(List<String> cityIds, Date startDate, Date endDate, String caliberId) {
        try {
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService.findLoadCityFc(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityDayFcDO> loadFeatureCityDayHisVOs = (List<LoadFeatureCityDayFcDO>) doStatLoadFeatureCityDay(
                loadCityFcVOS);
            loadFeatureCityDayFcService.doSaveOrUpdateLoadFeatureCityDayFcDOS(loadFeatureCityDayHisVOs);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-预测-日-负荷特性出错了", e);
        }
    }


    @Override
    public void doStatLoadFeatureCityWeekFc(List<String> cityIds, Date startDate, Date endDate, String caliberId) {
        try {
            List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcList = loadFeatureCityDayFcService
                .findReportLoadFeatureCityDayFcList(cityIds, new java.sql.Date(startDate.getTime()),
                    new java.sql.Date(endDate.getTime()), caliberId);
            List<LoadFeatureCityWeekFcDO> voList = loadFeatureCityWeekFcService
                .statisticsWeekFeatures(loadFeatureCityDayFcList);
            loadFeatureCityWeekFcService.doSaveOrUpdateLoadFeatureCityWeekFcDOS(voList);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-预测-周-负荷特性出错了", e);
        }
    }


    @Override
    public void doStatLoadFeatureCityMonthFc(List<String> cityIds, Date startDate, Date endDate, String caliberId) {
        try {
            List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcList = loadFeatureCityDayFcService
                .findReportLoadFeatureCityDayFcList(cityIds, new java.sql.Date(startDate.getTime()),
                    new java.sql.Date(endDate.getTime()), caliberId);
            List<LoadFeatureCityMonthFcDO> voList = loadFeatureCityMonthFcService
                .statisticsMonthFeatures(loadFeatureCityDayFcList);
            loadFeatureCityMonthFcService.doSaveOrUpdateLoadFeatureCityMonthFcDOS(voList);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-预测-月-负荷特性出错了", e);
        }
    }


    /**
     * 统计日负荷特性
     */
    @Override
    public List<? extends BaseLoadFeatureCityDayDO> doStatLoadFeatureCityDay(List<? extends BaseLoadCityDO> loadCityVOS)
        throws Exception {
        List<String> peakTimes = settingSystemDAO.getPeakTimes();
        List<String> troughTimes = settingSystemDAO.getTroughTimes();
        List<BaseLoadFeatureCityDayDO> BaseLoadFeatureCityDayDOS = new ArrayList<>();
        if (loadCityVOS != null) {
            for (BaseLoadCityDO loadCityVO : loadCityVOS) {
                try {
                    BaseLoadFeatureCityDayDO LoadFeatureCityDayHisDO = statisticsDayFeature(loadCityVO, peakTimes,
                        troughTimes);
                    BaseLoadFeatureCityDayDOS.add(LoadFeatureCityDayHisDO);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return BaseLoadFeatureCityDayDOS;
    }

    @Override
    public BaseLoadFeatureCityDayDO doStatLoadFeatureCityDay(BasePeriod96VO loadCityVO) {
        List<String> peakTimes;
        List<String> troughTimes;
        try {
            peakTimes = settingSystemDAO.getPeakTimes();
            troughTimes = settingSystemDAO.getTroughTimes();
        } catch (Exception e) {
            e.printStackTrace();
            throw TsieExceptionUtils.newBusinessException("数据库查询错误");
        }
        BaseLoadCityDO baseLoadCityDO = new BaseLoadCityDO();
        BeanUtils.copyProperties(loadCityVO, baseLoadCityDO);
        BaseLoadFeatureCityDayDO baseLoadFeatureCityDayDO = statisticsDayFeature(baseLoadCityDO, peakTimes,
            troughTimes);
        return baseLoadFeatureCityDayDO;
    }


    public BaseLoadFeatureCityDayDO statisticsDayFeature(BaseLoadCityDO loadCityVO, List<String> peakTimes,
        List<String> troughTimes) {

        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
        List<BigDecimal> troughs = new ArrayList<BigDecimal>();

        List<BigDecimal> loadList = BasePeriodUtils
            .toList(loadCityVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> loadMap = BasePeriodUtils
            .toMap(loadCityVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);

        String maxTime = null; // 最大负荷发生时刻
        String minTime = null; // 最小负荷发生时刻

        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
                if (peakTimes != null && peakTimes.contains(column)) {
                    peaks.add(load);
                }
                if (troughTimes != null && troughTimes.contains(column)) {
                    troughs.add(load);
                }
            }
        }

        BaseLoadFeatureCityDayDO featureCityDayHisVO = createBaseLoadFeatureCityDayDO(loadCityVO);

        featureCityDayHisVO.setCityId(loadCityVO.getCityId());
        featureCityDayHisVO.setDate(loadCityVO.getDate());
        featureCityDayHisVO.setCaliberId(loadCityVO.getCaliberId());
        if (maxTime != null) {
            featureCityDayHisVO.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            featureCityDayHisVO.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }

        // 最大负荷
        featureCityDayHisVO.setMaxLoad(maxMixAvg.get("max"));
        // 最小负荷
        featureCityDayHisVO.setMinLoad(maxMixAvg.get("min"));
        // 平均负荷
        featureCityDayHisVO.setAveLoad(maxMixAvg.get("avg"));
        // 峰谷差 = 日最大负荷 – 日最小负荷
        featureCityDayHisVO
            .setDifferent(BigDecimalUtils.sub(featureCityDayHisVO.getMaxLoad(), featureCityDayHisVO.getMinLoad()));
        if (featureCityDayHisVO.getMaxLoad() != null && featureCityDayHisVO.getMaxLoad().intValue() != 0) {
            // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
            featureCityDayHisVO.setGradient(
                BigDecimalUtils.divide(featureCityDayHisVO.getDifferent(), featureCityDayHisVO.getMaxLoad(), 4));
            // 负荷率 = 日平均负荷/日最大负荷
            featureCityDayHisVO.setLoadGradient(
                BigDecimalUtils.divide(featureCityDayHisVO.getAveLoad(), featureCityDayHisVO.getMaxLoad(), 4));
        }
// 尖峰平均负荷 = average(尖峰时段负荷）。尖峰时段后台可配，默认值为5:30~7:00，10:00~12:00，18：00~20:00
        featureCityDayHisVO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
        // 低谷平均负荷 = average（低谷时段负荷）。低谷时段可配，默认值为2:00~5:00，12:00~14:00，22:00~0:00
        featureCityDayHisVO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
        // 日电量 = 96点负荷之和/4
        featureCityDayHisVO
            .setEnergy(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));

        return featureCityDayHisVO;
    }

    private BaseLoadFeatureCityDayDO createBaseLoadFeatureCityDayDO(BaseVO loadCityVO) {
        if (loadCityVO instanceof LoadCityFcDO) {
            LoadFeatureCityDayFcDO LoadFeatureCityDayFcDO = new LoadFeatureCityDayFcDO();
            LoadCityFcDO LoadCityFcDO = (LoadCityFcDO) loadCityVO;
            LoadFeatureCityDayFcDO.setAlgorithmId(LoadCityFcDO.getAlgorithmId());
            LoadFeatureCityDayFcDO.setReport(LoadCityFcDO.getReport());
            return LoadFeatureCityDayFcDO;
        } else if (loadCityVO instanceof LoadCityHisDO) {
            return new LoadFeatureCityDayHisDO();
        }
        return new BaseLoadFeatureCityDayDO();
    }


    @Override
    public List<LoadFeatureCityWeekHisDO> doStatLoadFeatureCityWeek(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception {
        try {
            List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityWeekHisDO> LoadFeatureCityWeekHisDOs = loadFeatureCityWeekHisDAO
                .statisticsWeekFeatures(LoadFeatureCityDayHisDOS);
            return loadFeatureCityWeekHisDAO.doSaveOrUpdateLoadFeatureCityWeekHisDOs(LoadFeatureCityWeekHisDOs);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-实际-周-负荷特性出错了", e);
        }
    }


    @Override
    public List<LoadFeatureCityMonthHisDO> doStatLoadFeatureCityMonth(List<String> cityIds, Date startDate,
        Date endDate,
        String caliberId) throws Exception {
        try {
            List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityMonthHisDO> LoadFeatureCityMonthHisDOS = loadFeatureCityMonthHisDAO
                .statisticsMonthFeatures(LoadFeatureCityDayHisDOS);
            return loadFeatureCityMonthHisDAO.doSaveOrUpdateLoadFeatureCityMonthHisDOs(LoadFeatureCityMonthHisDOS);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-实际-月-负荷特性出错了", e);
        }
    }


    /**
     * 功能描述:统计时间段内 最大负荷，最小负荷，最大峰谷差，最大峰谷差率及其发生时间 <br> 〈〉
     *
     * @return:com.load.load.persistent.LoadFeatureCityMonthHisDO
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/22 13:15
     */
    @Override
    public IndexLoadFeatureDTO findStatLoadFeatureCityMonth(String cityId, String startStr, String endStr,
        String caliberId) throws Exception {
        Date startDate = DateUtils.string2Date(startStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(endStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<LoadFeatureExtendDTO> loadFeatureExtendDTOS = loadFeatureCityDayHisService
            .findLoadFeatureExtendDTOS(cityId, startDate, endDate, caliberId);

        //最大负荷
        Optional<LoadFeatureExtendDTO> maxLoadFeatureExtendDTO = loadFeatureExtendDTOS.stream()
            .max(Comparator.comparing(LoadFeatureExtendDTO::getMaxLoad)).filter(Objects::nonNull);
        LoadFeatureExtendDTO maxLoadDTO = maxLoadFeatureExtendDTO.orElse(new LoadFeatureExtendDTO());

        //最小负荷
        Optional<LoadFeatureExtendDTO> minloadFeatureExtendDTO = loadFeatureExtendDTOS.stream()
            .min(Comparator.comparing(LoadFeatureExtendDTO::getMinLoad)).filter(Objects::nonNull);
        LoadFeatureExtendDTO minLoadDTO = minloadFeatureExtendDTO.orElse(new LoadFeatureExtendDTO());

        //最大峰谷差
        Optional<LoadFeatureExtendDTO> maxDifferLoadFeatureExtendDTO = loadFeatureExtendDTOS.stream()
            .max(Comparator.comparing(LoadFeatureExtendDTO::getDifferent)).filter(Objects::nonNull);
        LoadFeatureExtendDTO maxDifferDTO = maxDifferLoadFeatureExtendDTO.orElse(new LoadFeatureExtendDTO());

        //最大峰谷差率
        Optional<LoadFeatureExtendDTO> maxGradientLoadFeatureExtendDTO = loadFeatureExtendDTOS.stream()
            .max(Comparator.comparing(LoadFeatureExtendDTO::getGradient)).filter(Objects::nonNull);
        LoadFeatureExtendDTO maxGradientDTO = maxGradientLoadFeatureExtendDTO.orElse(new LoadFeatureExtendDTO());

        IndexLoadFeatureDTO dto = new IndexLoadFeatureDTO();
        dto.setMaxLoad(maxLoadDTO.getMaxLoad());
        dto.setMaxLoadDate(maxLoadDTO.getDate());

        dto.setMinLoad(minLoadDTO.getMinLoad());
        dto.setMinLoadDate(minLoadDTO.getDate());

        dto.setDifferent(maxDifferDTO.getDifferent());
        dto.setDifferentDate(maxDifferDTO.getDate());

        dto.setGradient(
            maxGradientDTO.getGradient() == null ? null
                : (maxGradientDTO.getGradient().setScale(4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)))
                    .doubleValue());
        dto.setGradientDate(maxGradientDTO.getDate());

        return dto;
    }


    /**
     * 功能描述: 统计时间段内每个月的最大负荷和最小负荷，以及同期最大，最小负荷<br> 〈〉
     *
     * @return:java.util.List<com.load.load.dto.LoadFeatureCityMonthDTO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/27 13:13
     */

    @Override
    public List<LoadFeatureCityMonthDTO> findTypePowerFeatureDate(String dataType, Date start, Date end, String cityId,
        String caliberId) throws Exception {

        List<LoadFeatureCityMonthDTO> resultList = new ArrayList<LoadFeatureCityMonthDTO>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        while (start.before(end)) {
            String startStr = format.format(start);
            //今年负荷特性数据
            Calendar instance = Calendar.getInstance();
            instance.setTime(start);
            instance.set(Calendar.DATE, instance.getActualMaximum(Calendar.DATE));
            String thisYearEndStr = format.format(instance.getTime());
            IndexLoadFeatureDTO thisYearFeatureCityMonth = this
                .findStatLoadFeatureCityMonth(cityId, startStr, thisYearEndStr, caliberId);

            //去年负荷特性
            Date startDate = format.parse(startStr);
            Date lastYearStartDate = DateUtils.addMonths(startDate, -12);
            String lastYearStartStr = format.format(lastYearStartDate);
            instance.setTime(lastYearStartDate);

            instance.set(Calendar.DATE, instance.getActualMaximum(Calendar.DATE));
            String lastYearEndStr = format.format(instance.getTime());

            IndexLoadFeatureDTO lastYearFeatureCityMonth = this
                .findStatLoadFeatureCityMonth(cityId, lastYearStartStr, lastYearEndStr, caliberId);
            LoadFeatureCityMonthDTO dto = new LoadFeatureCityMonthDTO();
            dto.setMonth(startStr.substring(5, 7));
            dto.setThisYearMaxLoad(thisYearFeatureCityMonth.getMaxLoad());
            dto.setLastYearMaxLoad(lastYearFeatureCityMonth.getMaxLoad());
            dto.setThisYearMinLoad(thisYearFeatureCityMonth.getMinLoad());
            dto.setLastYearMinLoad(lastYearFeatureCityMonth.getMinLoad());
            start = DateUtils.addMonths(start, 1);
            resultList.add(dto);
        }
        return resultList;
    }

    @Override
    public void doShortFcLoadFeatureCityDay(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        List<LoadFeatureCityDayShortFcDO> shortFcFeatureDOS = new ArrayList<>();
        //统计 五分钟间隔的 超短期预测特性
        List<LoadCityFc288DO> fc288DOS = loadCityFc288DAO.getLoadCityFc288DOs(cityId, startDate, endDate, caliberId);
        List<LoadCityHis288DO> his288DOS = loadCityHis288DAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        if (CollectionUtils.isNotEmpty(fc288DOS) && CollectionUtils.isNotEmpty(his288DOS)) {
            Map<String, LoadCityHis288DO> his288Map = his288DOS.stream().collect(Collectors.toMap(
                e -> e.getDate().getTime() + e.getCityId() + e.getCaliberId(),
                e -> e, (oldV, curV) -> curV));
            for (LoadCityFc288DO fc288DO : fc288DOS) {
                LoadCityHis288DO his288DO = his288Map
                    .get(fc288DO.getDate().getTime() + fc288DO.getCityId() + fc288DO.getCaliberId());
                if (his288DO == null || fc288DO == null) {
                    continue;
                }
                LoadFeatureCityDayShortFcDO featureCityDayShortFcDO = statisticsShortFcFeature(his288DO, fc288DO, 1);
                if (featureCityDayShortFcDO == null) {
                    continue;
                }
                featureCityDayShortFcDO.setDate(fc288DO.getDate());
                featureCityDayShortFcDO.setCityId(fc288DO.getCityId());
                featureCityDayShortFcDO.setCaliberId(fc288DO.getCaliberId());
                featureCityDayShortFcDO.setAlgorithmId(fc288DO.getAlgorithmId());
                shortFcFeatureDOS.add(featureCityDayShortFcDO);
            }
        }

        //统计 十五分钟间隔的 超短期预测特性
        List<LoadCityFcDO> fcDOList = loadCityFcService
            .findFcByAlgorithmId(cityId, caliberId, AlgorithmEnum.SHORT_FORECAST.getId(), startDate, endDate);
        List<LoadCityHisDO> hisDOList = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        if (CollectionUtils.isNotEmpty(fcDOList) && CollectionUtils.isNotEmpty(hisDOList)) {
            Map<String, LoadCityHisDO> his96Map = hisDOList.stream().collect(Collectors.toMap(
                e -> e.getDate().getTime() + e.getCityId() + e.getCaliberId(),
                e -> e, (oldV, curV) -> curV));
            for (LoadCityFcDO fcDO : fcDOList) {
                LoadCityHisDO hisDO = his96Map.get(fcDO.getDate().getTime() + fcDO.getCityId() + fcDO.getCaliberId());
                if (hisDO == null || fcDO == null) {
                    continue;
                }
                LoadFeatureCityDayShortFcDO featureCityDayShortFcDO = statisticsShortFcFeature(hisDO, fcDO, 2);
                if (featureCityDayShortFcDO == null) {
                    continue;
                }
                featureCityDayShortFcDO.setDate(fcDO.getDate());
                featureCityDayShortFcDO.setCityId(fcDO.getCityId());
                featureCityDayShortFcDO.setCaliberId(fcDO.getCaliberId());
                featureCityDayShortFcDO.setAlgorithmId(fcDO.getAlgorithmId());
                shortFcFeatureDOS.add(featureCityDayShortFcDO);
            }
        }
        loadFeatureCityDayFcShortService.doSaveOrUpdateLoadFeatureCityDayFcDOS(shortFcFeatureDOS);
    }

    private LoadFeatureCityDayShortFcDO statisticsShortFcFeature(BasePeriod96VO hisDO, BasePeriod96VO fcDO,
        Integer type) throws Exception {
        List<BigDecimal> fcBigDecimals = BasePeriodUtils
            .toList(fcDO, type == 1 ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> hisBigDecimals = BasePeriodUtils
            .toList(hisDO, type == 1 ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        fcBigDecimals.removeAll(Collections.singleton(null));
        hisBigDecimals.removeAll(Collections.singleton(null));
        if (CollectionUtils.isEmpty(fcBigDecimals) || CollectionUtils.isEmpty(hisBigDecimals)) {
            return null;
        }
        BigDecimal maxFc = fcBigDecimals.stream().filter(Objects::nonNull).max(BigDecimal::compareTo).get();
        BigDecimal minFc = fcBigDecimals.stream().filter(Objects::nonNull).min(BigDecimal::compareTo).get();
        BigDecimal avgFc = fcBigDecimals.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(fcBigDecimals.size()), 4, BigDecimal.ROUND_HALF_UP);
        int maxFcIndex = fcBigDecimals.indexOf(maxFc);
        int minFcIndex = fcBigDecimals.indexOf(minFc);
        //最大负荷准确率  最小负荷准确率
        BigDecimal maxLoadAccuracy = LoadCalUtil.calcMaxMinAccuracy(hisBigDecimals.get(maxFcIndex), maxFc);
        BigDecimal minLoadAccuracy = LoadCalUtil.calcMaxMinAccuracy(hisBigDecimals.get(minFcIndex), minFc);
        List<BigDecimal> accuracyList = new ArrayList<>();
        for (int i = 0; i < fcBigDecimals.size(); i++) {
            accuracyList.add(LoadCalUtil.calcMaxMinAccuracy(hisBigDecimals.get(i), fcBigDecimals.get(i)));
        }
        accuracyList = accuracyList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accuracyList)) {
            return null;
        }
        BigDecimal maxAccuracy = accuracyList.stream().max(BigDecimal::compareTo).get();
        BigDecimal minAccuracy = accuracyList.stream().min(BigDecimal::compareTo).get();
        BigDecimal avgAccuracy = accuracyList.stream().reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(accuracyList.size()), 4, BigDecimal.ROUND_HALF_UP);
        LoadFeatureCityDayShortFcDO loadFeatureCityDayShortFcDO = new LoadFeatureCityDayShortFcDO();
        //五分钟间隔
        loadFeatureCityDayShortFcDO.setType(type);
        loadFeatureCityDayShortFcDO.setMaxLoad(maxFc);
        loadFeatureCityDayShortFcDO.setMinLoad(minFc);
        loadFeatureCityDayShortFcDO.setAvgLoad(avgFc);
        loadFeatureCityDayShortFcDO.setMaxTime(DateUtil.getHHmmTime(null, type, maxFcIndex + 1));
        loadFeatureCityDayShortFcDO.setMinTime(DateUtil.getHHmmTime(null, type, minFcIndex + 1));
        loadFeatureCityDayShortFcDO.setMaxLoadAccuracy(maxLoadAccuracy);
        loadFeatureCityDayShortFcDO.setMinLoadAccuracy(minLoadAccuracy);
        loadFeatureCityDayShortFcDO.setMaxAccuracy(maxAccuracy);
        loadFeatureCityDayShortFcDO.setMinAccuracy(minAccuracy);
        loadFeatureCityDayShortFcDO.setAvgAccuracy(avgAccuracy);
        return loadFeatureCityDayShortFcDO;
    }


    @Override
    public List<LoadFeatureCityQuarterHisDO> doStatLoadFeatureCityQuarter(List<String> cityIds, Date startDate,
        Date endDate,
        String caliberId) throws Exception {
        try {
            List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityQuarterHisDO> LoadFeatureCityQuarterHisDOS = loadFeatureCityQuarterHisDAO
                .statisticsQuarterFeatures(LoadFeatureCityDayHisDOS);
            return loadFeatureCityQuarterHisDAO
                .doSaveOrUpdateLoadFeatureCityQuarterHisDOs(LoadFeatureCityQuarterHisDOS);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-实际-季-负荷特性出错了", e);
        }
    }


    @Override
    public List<LoadFeatureCityYearHisDO> doStatLoadFeatureCityYear(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception {
        try {
            List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityIds, startDate, endDate, caliberId);
            List<LoadFeatureCityYearHisDO> LoadFeatureCityYearHisDOS = loadFeatureCityYearHisDAO
                .statisticsYearFeatures(LoadFeatureCityDayHisDOS);
            return loadFeatureCityYearHisDAO.doSaveOrUpdateLoadFeatureCityYearHisDOs(LoadFeatureCityYearHisDOS);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("统计-实际-年-负荷特性出错了", e);
        }
    }

    @Override
    public void doStatLoadFeatureCityAll(List<String> cityIds, Date startDate, Date endDate, String caliberId)
        throws Exception {
        this.doStatLoadFeatureCityDay(cityIds, startDate, endDate, caliberId);
        this.doStatLoadFeatureCityWeek(cityIds, startDate, endDate, caliberId);
        this.doStatLoadFeatureCityMonth(cityIds, startDate, endDate, caliberId);
        this.doStatLoadFeatureCityQuarter(cityIds, startDate, endDate, caliberId);
        this.doStatLoadFeatureCityYear(cityIds, startDate, endDate, caliberId);
    }


    /**
     * 功能描述: 统计数据中的最大 最大负荷，最小 最小负荷，最大 峰谷差，最大 峰谷差率<br> 〈〉
     *
     * @return:com.load.load.persistent.LoadFeatureCityMonthHisDO
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/22 13:24
     */

    public LoadFeatureCityMonthHisDO statStatLoadFeatureCityMonth(
        List<LoadFeatureCityMonthHisDO> LoadFeatureCityMonthHisDOS) throws Exception {

        BigDecimal maxLoad = LoadFeatureCityMonthHisDOS.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad)).get().getMaxLoad();
        BigDecimal minLoad = LoadFeatureCityMonthHisDOS.stream()
            .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad)).get().getMinLoad();
        BigDecimal different = LoadFeatureCityMonthHisDOS.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getDifferent)).get().getDifferent();
        BigDecimal gradient = LoadFeatureCityMonthHisDOS.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getGradient)).get().getGradient();
        LoadFeatureCityMonthHisDO featureCityMonthHisVO = new LoadFeatureCityMonthHisDO();
        featureCityMonthHisVO.setDifferent(different);
        featureCityMonthHisVO.setMaxLoad(maxLoad);
        featureCityMonthHisVO.setMinLoad(minLoad);
        featureCityMonthHisVO.setGradient(gradient);
        return featureCityMonthHisVO;
    }


    /**
     * 统计日负荷预测特性
     *
     * @param fcVO 预测负荷
     */
//    @Override
//    public LoadFeatureFcDTO findStatisticsDayFeature(BasePeriod96VO fcVO) throws Exception {
//        if (fcVO == null) {
//            return null;
//        }
//        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
//        List<String> peakSectionTimes = settingSystemDAO.getPeakSectionTime();
//        // 最小负荷发生时刻
//        String minTime = null;
//        // 最大负荷发生时刻
//        String maxTime = null;
//        Map<String, BigDecimal> loadMap = BasePeriodUtils
//            .toMap(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
//        List<BigDecimal> loadList = BasePeriodUtils
//            .toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
//        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
//        for (String column : loadMap.keySet()) {
//            BigDecimal load = loadMap.get(column);
//            if (null != load) {
//                column = column.substring(1);
//                if (load.compareTo(maxMixAvg.get("max")) == 0) {
//                    maxTime = column;
//                }
//                if (peakSectionTimes != null && peakSectionTimes.contains(column)) {
//                    peaks.add(load);
//                }
//                if (load.compareTo(maxMixAvg.get("min")) == 0) {
//                    minTime = column;
//                }
//            }
//        }
//        LoadFeatureFcDTO result = new LoadFeatureFcDTO();
//        if (maxTime != null) {
//            result.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
//        }
//        if (minTime != null) {
//            result.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
//        }
//        // 最大负荷
//        result.setMaxLoad(maxMixAvg.get("max"));
//        // 最小负荷
//        result.setMinLoad(maxMixAvg.get("min"));
//        // 平均负荷
//        result.setAveLoad(maxMixAvg.get("avg"));
//        // 峰谷差 = 日最大负荷 – 日最小负荷
//        result.setDifferent(BigDecimalUtils.sub(result.getMaxLoad(), result.getMinLoad()));
//        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
//        if (result.getMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
//            result.setGradient(BigDecimalUtils.divide(result.getDifferent(), result.getMaxLoad(), 4));
//        }
//        // 积分电量 = 96点负荷之和/4
//        result.setIntegralLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
//        // 段峰电量 = 尖峰时段在数据库setting_system_init中，默认值为08：00~22:00
//        result.setPeakSectionLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(peaks), new BigDecimal(4), 4));
//        return result;
//    }
    @Override
    public LoadFeatureFcDTO findStatisticsDayFeature(BasePeriod96VO fcVO)  {
        if (fcVO == null) {
            return null;
        }

        // 最小负荷发生时刻
        String minTime = null;
        // 最大负荷发生时刻
        String maxTime = null;
        // 午间高峰负荷
        BigDecimal noonPeakLoad = BigDecimal.ZERO;
        // 午间高峰时间
        String noonPeakTime = null;
        // 晚间高峰负荷
        BigDecimal eveningPeakLoad = BigDecimal.ZERO;
        // 晚间高峰时间
        String eveningPeakTime = null;

        Map<String, BigDecimal> loadMap = BasePeriodUtils
                .toMap(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> loadList = BasePeriodUtils
                .toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);

        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                // 解析时间为小时数用于时段判断
                int hour = Integer.parseInt(column.substring(0, 2));

                // 最大最小负荷判断
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }

                // 午间高峰判断(15:00之前)
                if (hour < 15) {
                    if (load.compareTo(noonPeakLoad) > 0) {
                        noonPeakLoad = load;
                        noonPeakTime = column;
                    }
                }
                // 晚间高峰判断(15:00及之后)
                else {
                    if (load.compareTo(eveningPeakLoad) > 0) {
                        eveningPeakLoad = load;
                        eveningPeakTime = column;
                    }
                }
            }
        }

        LoadFeatureFcDTO result = new LoadFeatureFcDTO();
        if (maxTime != null) {
            result.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            result.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }

        // 设置午间高峰数据
        if (noonPeakTime != null) {
            result.setNoonMaxTime(new StringBuffer(noonPeakTime).insert(2, ":").toString());
            result.setNoonMaxLoad(noonPeakLoad);
        }

        // 设置晚间高峰数据
        if (eveningPeakTime != null) {
            result.setEveningMaxTime(new StringBuffer(eveningPeakTime).insert(2, ":").toString());
            result.setEveningMaxLoad(eveningPeakLoad);
        }
        // 最大负荷
        result.setMaxLoad(maxMixAvg.get("max"));
        // 最小负荷
        result.setMinLoad(maxMixAvg.get("min"));
        // 平均负荷
        result.setAveLoad(maxMixAvg.get("avg"));
        // 峰谷差 = 日最大负荷 – 日最小负荷
        result.setDifferent(BigDecimalUtils.sub(result.getMaxLoad(), result.getMinLoad()));
        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
        if (result.getMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
            result.setGradient(BigDecimalUtils.divide(result.getDifferent(), result.getMaxLoad(), 4));
        }

        return result;
    }

}
