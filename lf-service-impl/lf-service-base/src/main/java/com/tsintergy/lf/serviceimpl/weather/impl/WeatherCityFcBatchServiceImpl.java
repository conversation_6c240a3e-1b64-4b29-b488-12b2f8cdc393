package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcBatchDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 17:12
 **/
@Service("weatherCityFcBatchService")
public class WeatherCityFcBatchServiceImpl implements WeatherCityFcBatchService {

    @Autowired
    private WeatherCityFcBatchDAO weatherCityFcBatchDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Override
    public List<WeatherCityFcBatchDO> findByCondition(String cityId, String weatherSource,Date preDate, Integer type, String batchId) throws Exception {
        SettingBatchInitDO batch = settingBatchInitService.getBatchById(batchId);
        List<WeatherCityFcBatchDO> fcBatchDOS = weatherCityFcBatchDAO.findAll(JpaWrappers.<WeatherCityFcBatchDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId),WeatherCityFcBatchDO::getCityId, cityId)
                .eq(Objects.nonNull(preDate),WeatherCityFcBatchDO::getPredLoadDate, preDate)
                .eq(!StringUtils.isEmpty(weatherSource),WeatherCityFcBatchDO::getSource, weatherSource)
                .eq(Objects.nonNull(type),WeatherCityFcBatchDO::getType, type).orderByAsc(WeatherCityFcBatchDO::getDate));
        return fcBatchDOS.stream().filter(t -> DateUtil
                .isWithinTimeRange(t.getCreatetime(), batch.getStartTime(), batch.getEndTime())).collect(Collectors.toList());
    }

    @Override
    public void doSaveOuUpdatePreLoadWeather(WeatherCityFcBatchDO weatherCityFcBatchDO) {
        List<WeatherCityFcBatchDO> weatherCityFc = weatherCityFcBatchDAO.findAll(JpaWrappers.<WeatherCityFcBatchDO>lambdaQuery()
                .eq(WeatherCityFcBatchDO::getCityId, weatherCityFcBatchDO.getCityId())
                .eq(WeatherCityFcBatchDO::getPredLoadDate, weatherCityFcBatchDO.getPredLoadDate())
                .eq(WeatherCityFcBatchDO::getType, weatherCityFcBatchDO.getType())
                .eq(WeatherCityFcBatchDO::getDate, weatherCityFcBatchDO.getDate())
                .eq(WeatherCityFcBatchDO::getSource, weatherCityFcBatchDO.getSource())
        );

        AtomicInteger batchId = new AtomicInteger(1);
        if (weatherCityFc != null) {
            Optional<WeatherCityFcBatchDO> max = weatherCityFc.stream().max(Comparator.comparing(WeatherCityFcBatchDO::getBatchId));
            max.ifPresent(t -> batchId.set(t.getBatchId()+1));
        }
        weatherCityFcBatchDO.setBatchId(batchId.get());
        weatherCityFcBatchDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityFcBatchDAO.saveOrUpdateByTemplate(weatherCityFcBatchDO);
    }
}
