/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.report.impl;

import com.tsintergy.lf.core.enums.ReportTypeEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.ParamDate;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeCumulativeService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyWeekService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAllAccuracyService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeCumulativeDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyWeekDO;
import com.tsintergy.lf.serviceimpl.common.util.ParamDateUtil;
import com.tsintergy.lf.serviceimpl.report.dao.ReportAccuracySynthesizeMonthDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/4/1 13:28
 * @Version: 1.0.0
 */
@Service("reportAllAccuracyService")
public class ReportAllAccuracyServiceImpl implements ReportAllAccuracyService {

    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;

    @Autowired
    private ReportAccuracyWeekService reportAccuracyWeekService;

    @Autowired
    private ReportAccuracyMonthService reportAccuracyMonthService;

    @Autowired
    private ReportAccuracySynthesizeMonthService reportAccuracySynthesizeMonthService;

    @Autowired
    private ReportAccuracySynthesizeCumulativeService reportAccuracySynthesizeCumulativeService;

    @Autowired
    ReportAccuracySynthesizeMonthDAO reportAccuracySynthesizeMonthDAO;
@Autowired
    CaliberService caliberService;
    @Autowired
    private CityService cityService;
    @Override
    public void doReportAccuracy(Date start, Date end, String cityId) throws Exception {
        ParamDate paramDate = new ParamDate();
        paramDate.setStartDate(start);
        paramDate.setEndDate(end);
        //统计填报的日准确率(昨日准确率)
        statDayAccuracy(paramDate, cityId);
        //统计填报的周准确率(统计所在周准确率)
//        paramDate.setStartDate(ParamDateUtil.findWeekFirstDayByDate(start));
//        paramDate.setEndDate(ParamDateUtil.findWeekLastDayByDate(end));
//        statWeekAccuracy(paramDate, cityId);
//        //统计填报的月准确率（统计填报准确率）
//        paramDate.setStartDate(ParamDateUtil.findMonthFirstDayByDate(start));
//        paramDate.setEndDate(ParamDateUtil.findMonthLastDayByDate(end));
//        List<ReportAccuracyMonthDO> reportAccuracyMonthDOS = statMonthAccuracy(paramDate, cityId);
//        //统计综合准确率（统计月综合准确率）
//        statSynthesizeMonthAccuracy(paramDate, cityId);
//        //统计累计月综合准确率
//        doStatAomprehensiveAccuracy(paramDate.getEndDate(), cityId);

    }


    @Override
    public void doStatAomprehensiveAccuracy(Date endDate, String cityId) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);
        calendar.add(Calendar.YEAR, -1);
        Date time = calendar.getTime();
        String yearByDate = DateUtil.getYearByDate(time);
        yearByDate = yearByDate + "-12-31";
        String formateDate = DateUtil.formateDate(endDate);
        String monthByDate = DateUtil.getMonthByDate(endDate);
        monthByDate = monthByDate + "-01";
        List<ReportAccuracySynthesizeMonthDO> synthesizeMonthVOS = reportAccuracySynthesizeMonthDAO
            .selectSynthesizeMonth(yearByDate, formateDate, cityId);
        Map<String, List<ReportAccuracySynthesizeMonthDO>> collect = synthesizeMonthVOS.stream()
            .collect(Collectors.groupingBy(ReportAccuracySynthesizeMonthDO::getCityId));
        Date finalMonthByDate = DateUtil.getDate(monthByDate, null);
        for (Entry<String, List<ReportAccuracySynthesizeMonthDO>> entry : collect.entrySet()) {
            String k = entry.getKey();
            List<ReportAccuracySynthesizeMonthDO> v = entry.getValue();
            if (v.size()==0)
            {
                continue;
            }
            BigDecimal reduce = v.stream().map(e -> e.getSynthesizeAccuracy())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide = reduce.divide(BigDecimal.valueOf(v.size()), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal point = v.stream().map(e -> e.getPointAccuracy())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal pointAccuracy = point.divide(BigDecimal.valueOf(v.size()), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal max = v.stream().map(e -> e.getMaxSynthesizeAccuracy())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal maxAccuracy = max.divide(BigDecimal.valueOf(v.size()), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal min = v.stream().map(e -> e.getMinSynthesizeAccuracy())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal minAccuracy = min.divide(BigDecimal.valueOf(v.size()), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal energy = v.stream().map(e -> e.getEnergySynthesizeAccuracy())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal energyAccuracy = energy.divide(BigDecimal.valueOf(v.size()), 4, BigDecimal.ROUND_HALF_UP);
            try {
                ReportAccuracySynthesizeCumulativeDO byDateAndCityId = reportAccuracySynthesizeCumulativeService
                    .findByDateAndCityId(new java.sql.Date(finalMonthByDate.getTime()), k,
                        ReportTypeEnum.MONTH.getType());
                if (byDateAndCityId != null) {
                    byDateAndCityId.setAccuracy(divide);
                    byDateAndCityId.setEnergySynthesizeAccuracy(energyAccuracy);
                    byDateAndCityId.setMaxSynthesizeAccuracy(maxAccuracy);
                    byDateAndCityId.setMinSynthesizeAccuracy(minAccuracy);
                    byDateAndCityId.setPointAccuracy(pointAccuracy);
                    byDateAndCityId.setUpdateTime(new Date());
                    reportAccuracySynthesizeCumulativeService.update(byDateAndCityId);
                } else {
                    ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = new ReportAccuracySynthesizeCumulativeDO();
                    reportAccuracySynthesizeCumulativeDO.setCityId(k);
                    reportAccuracySynthesizeCumulativeDO.setAccuracy(divide);
                    reportAccuracySynthesizeCumulativeDO.setEnergySynthesizeAccuracy(energyAccuracy);
                    reportAccuracySynthesizeCumulativeDO.setMaxSynthesizeAccuracy(maxAccuracy);
                    reportAccuracySynthesizeCumulativeDO.setMinSynthesizeAccuracy(minAccuracy);
                    reportAccuracySynthesizeCumulativeDO.setPointAccuracy(pointAccuracy);
                    reportAccuracySynthesizeCumulativeDO.setDate(new java.sql.Date(finalMonthByDate.getTime()));
                    reportAccuracySynthesizeCumulativeDO.setType(ReportTypeEnum.MONTH.getType());
                    reportAccuracySynthesizeCumulativeDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    reportAccuracySynthesizeCumulativeDO.setCreateTime(new Date());
                    reportAccuracySynthesizeCumulativeDO.setUpdateTime(new Date());
                    reportAccuracySynthesizeCumulativeService.insert(reportAccuracySynthesizeCumulativeDO);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 日准确率
     */
    @Override
    public void statDayAccuracy(ParamDate paramDate, String cityId) throws Exception {
        try {
            List<CaliberDO> allCalibers = caliberService.findAllCalibers();
            for (CaliberDO allCaliber : allCalibers) {
                //统计日负荷填报准确率
                List<ReportAccuracyDayDO> reportAccuracyDayVOS = reportAccuracyDayService
                    .doStatSaveReportAccuracy(cityId, paramDate.getStartDate(), paramDate.getEndDate(),allCaliber.getId());
                reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);
            }


        } catch (Exception e) {

        }
    }

    /**
     * 周准确率
     */
    @Override
    public void statWeekAccuracy(ParamDate paramDate, String cityId) throws Exception {
        try {

            //统计周负荷填报准确率
            List<ReportAccuracyWeekDO> reportAccuracyWeekVOList = reportAccuracyWeekService
                .doStatWeekAccuracy(cityId, paramDate.getStartDate(), paramDate.getEndDate());
            reportAccuracyWeekService.doSaveOrUpdate(reportAccuracyWeekVOList);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 月准确率
     */
    @Override
    public List<ReportAccuracyMonthDO> statMonthAccuracy(ParamDate paramDate, String cityId) throws Exception {
        try {

            List<ReportAccuracyMonthDO> monthVOList = reportAccuracyMonthService
                .statReportMonthAccuracy(cityId, paramDate.getStartDate(), paramDate.getEndDate());
            reportAccuracyMonthService.doSaveOrUpdate(monthVOList);
            return monthVOList;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 月综合准确率
     */
    public void statSynthesizeMonthAccuracy(ParamDate paramDate, String cityId) throws Exception {
        try {
            List<String> stringList = getYearMonthList(paramDate.getStartDate(), paramDate.getEndDate());
            for (String ym : stringList) {
                String year = ym.substring(0, 4);
                String month = ym.substring(5, 7);
                List<CityDO> cityVOList = new ArrayList<>();
                List<String> cityIds = new ArrayList<>();
                if (cityId == null) {
                    cityVOList = cityService.findAllCitys();
                    cityIds = cityVOList.stream().map(CityDO::getId).collect(Collectors.toList());
                } else {
                    cityIds.add(cityId);
                }

                List<ReportAccuracySynthesizeMonthDO> synthesizeMonthVO = reportAccuracySynthesizeMonthService
                    .statSynthesizeMonthAccuracy(cityIds, year, month);
                reportAccuracySynthesizeMonthService.doSaveOrUpdate(synthesizeMonthVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static List<String> getYearMonthList(Date startDate, Date endDate) {
        List<Date> dateList = com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(startDate, endDate);
        List<String> stringList = new ArrayList<>();
        for (Date date : dateList) {
            String ym = DateUtil.getMonthByDate(date);
            if (!stringList.contains(ym)) {
                stringList.add(ym);
            }
        }
        return stringList;
    }
}
