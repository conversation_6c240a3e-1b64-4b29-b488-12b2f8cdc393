package com.tsintergy.lf.serviceimpl.base.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;

import com.tsintergy.lf.serviceapi.base.base.api.AlgorithmParamService;
import com.tsintergy.lf.serviceapi.base.base.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceimpl.base.dao.AlgorithmParamDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AlgorithmParamServiceImpl implements AlgorithmParamService {

    @Autowired
    AlgorithmParamDAO algorithmParamDAO;

    @Override
    public List<AlgorithmParamDO> getListByAlgorithmId(String algorithmId) {

        return algorithmParamDAO.findAll(JpaWrappers.<AlgorithmParamDO>lambdaQuery()
                .eq(AlgorithmParamDO::getAlgorithmId, algorithmId));
    }
}
