package com.tsintergy.lf.serviceimpl.datamanage.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.core.constants.TableConstants;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.DataCheckInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DataCheckInfoDAO.java, v 0.1 2018-04-02 14:13:03 tao Exp $$
 */
@Component
public class DataCheckInfoDAO extends BaseAbstractDAO<DataCheckInfoDO> {

    public List<DataCheckInfoDO> findLoadDataCheckInfoDO(String cityId, Date startDate, Date endDate) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (!StringUtils.isBlank(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != startDate) {
            param.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        param.where(QueryOp.StringEqualTo, "table", TableConstants.LOAD_TABLE);
        param.addOrderByDesc("date");
        List<DataCheckInfoDO> dataCheckInfoDOS = query(param.build()).getDatas();
        return dataCheckInfoDOS;
    }

    public List<DataCheckInfoDO> queryDataCheckInfoDO(String cityId, String caliberId, java.sql.Date startDate,
        java.sql.Date endDate) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (caliberId != null) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        param.where(QueryOp.DateNoLessThan, "date", startDate);
        param.where(QueryOp.DateNoMoreThan, "date", endDate);
        return query(param.build()).getDatas();
    }


}