package com.tsintergy.lf.serviceimpl.industry.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityTimeHisFluctuationRateStatsDO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


public interface IndustryCityTimeHisFluctuationRateStatsDAO extends BaseJpaDAO<IndustryCityTimeHisFluctuationRateStatsDO, String> {
}
