package com.tsintergy.lf.serviceimpl.weather.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityQuarterHisDO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityQuarterHisDAO.java, v 0.1 2018-04-04 09:54:09 tao Exp $$
 */

@Component
@Slf4j
public class WeatherFeatureCityQuarterHisDAO extends BaseAbstractDAO<WeatherFeatureCityQuarterHisDO> {

    /**
     * 获取季气象特性
     *
     * @param cityId 城市ID
     * @param year 年(yyyy)
     * @param quarter 季度
     */
    public WeatherFeatureCityQuarterHisDO getWeatherFeatureCityQuarterHisDO(String cityId, String year, String quarter)
        throws Exception {

        if (cityId == null) {
            throw new BusinessException("", "城市ID不可为空");
        }

        if (year == null) {
            throw new BusinessException("", "年份不可为空");
        }

        if (quarter == null) {
            throw new BusinessException("", "季度不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_year", year);
        param.getQueryConditions().put("_ne_quarter", quarter);
        param.getQueryConditions().put("_ne_cityId", cityId);
        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisVOs = this.query(param).getDatas();
        if (weatherFeatureCityQuarterHisVOs.size() > 0) {
            return weatherFeatureCityQuarterHisVOs.get(0);
        }
        return null;
    }

    /**
     * 统计季气象特性
     *
     * @param weatherFeatureCityDayHisVOs 一个季的日气象特性
     */
    public WeatherFeatureCityQuarterHisDO statisticsQuarterFeature(
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs) throws Exception {

        if (weatherFeatureCityDayHisVOs == null || weatherFeatureCityDayHisVOs.size() == 0) {
            return null;
        }

        if (weatherFeatureCityDayHisVOs.size() > 92) {
            log.error("统计季气象特性有误：日气象特性数据的超过92天，无法统计");
            return null;
        }

        Map<String, List<BigDecimal>> dataMap = new HashMap<String, List<BigDecimal>>();
        Field[] field = WeatherFeatureCityQuarterHisDO.class.getDeclaredFields();
        for (int i = 0; i < field.length; i++) {     //遍历所有属性
            String name = field[i].getName();    //获取属性的名字
            if (!name.equals("id") && !name.equals("cityId") && !name.equals("year") && !name.equals("Quarter") && !name
                .equals("createtime") && !name.equals("updatetime")) {
                dataMap.put(name, new ArrayList<BigDecimal>());
            }
        }

        String cityId = weatherFeatureCityDayHisVOs.get(0).getCityId();
        String yq = DateUtil.getQuarterByDate(weatherFeatureCityDayHisVOs.get(0).getDate()); // 年季

        for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOs) {

            if (!yq.equals(DateUtil.getQuarterByDate(weatherFeatureCityDayHisVO.getDate()))) {
                log.error("统计季气象特性有误：日气象特性数据的日期不是同一个季，无法统计");
                return null;
            }

            if (!cityId.equals(weatherFeatureCityDayHisVO.getCityId())) {
                log.error("统计季气象特性有误：日气象特性数据的日期不是同一城市，无法统计");
                return null;
            }

            Field[] field2 = weatherFeatureCityDayHisVO.getClass().getDeclaredFields();
            for (int j = 0; j < field2.length; j++) {     //遍历所有属性
                String name = field2[j].getName();    //获取属性的名字
                if (dataMap.get(name) != null) {
                    Method m = weatherFeatureCityDayHisVO.getClass()
                        .getMethod("get" + name.substring(0, 1).toUpperCase() + name.substring(1));
                    BigDecimal value = (BigDecimal) m.invoke(weatherFeatureCityDayHisVO);
                    if (value == null) {
                        value = BigDecimal.ZERO;
                    }
                    dataMap.get(name).add(value);
                }
            }

        }

        WeatherFeatureCityQuarterHisDO weatherFeatureCityQuarterHisVO = new WeatherFeatureCityQuarterHisDO();
        weatherFeatureCityQuarterHisVO.setCityId(cityId);
        weatherFeatureCityQuarterHisVO.setYear(yq.substring(0, 4));
        weatherFeatureCityQuarterHisVO.setQuarter(yq.substring(5, 6));
        weatherFeatureCityQuarterHisVO.setAveColdness(BigDecimalUtils.avgList(dataMap.get("aveColdness"), 4, false));
        weatherFeatureCityQuarterHisVO.setAveComfort(BigDecimalUtils.avgList(dataMap.get("aveComfort"), 4, false));
        weatherFeatureCityQuarterHisVO
            .setAveEffectiveTemperature(BigDecimalUtils.avgList(dataMap.get("aveEffectiveTemperature"), 4, false));
        weatherFeatureCityQuarterHisVO.setAveHumidity(BigDecimalUtils.avgList(dataMap.get("aveHumidity"), 4, false));
        weatherFeatureCityQuarterHisVO
            .setAveTemperature(BigDecimalUtils.avgList(dataMap.get("aveTemperature"), 4, false));
        weatherFeatureCityQuarterHisVO
            .setAveTemperatureHumidity(BigDecimalUtils.avgList(dataMap.get("aveTemperatureHumidity"), 4, false));
        weatherFeatureCityQuarterHisVO.setAveWinds(BigDecimalUtils.avgList(dataMap.get("aveWinds"), 4, false));
        weatherFeatureCityQuarterHisVO.setHighestComfort(LoadCalUtil.max(dataMap.get("highestComfort")));
        weatherFeatureCityQuarterHisVO
            .setHighestEffectiveTemperature(LoadCalUtil.max(dataMap.get("highestEffectiveTemperature")));
        weatherFeatureCityQuarterHisVO.setHighestHumidity(LoadCalUtil.max(dataMap.get("highestHumidity")));
        weatherFeatureCityQuarterHisVO.setHighestTemperature(LoadCalUtil.max(dataMap.get("highestTemperature")));
        weatherFeatureCityQuarterHisVO.setLowestComfort(LoadCalUtil.min(dataMap.get("lowestComfort")));
        weatherFeatureCityQuarterHisVO
            .setLowestEffectiveTemperature(LoadCalUtil.min(dataMap.get("lowestEffectiveTemperature")));
        weatherFeatureCityQuarterHisVO.setLowestHumidity(LoadCalUtil.min(dataMap.get("lowestHumidity")));
        weatherFeatureCityQuarterHisVO.setLowestTemperature(LoadCalUtil.min(dataMap.get("lowestTemperature")));
        weatherFeatureCityQuarterHisVO.setMaxColdness(LoadCalUtil.max(dataMap.get("maxColdness")));
        weatherFeatureCityQuarterHisVO
            .setMaxTemperatureHumidity(LoadCalUtil.max(dataMap.get("maxTemperatureHumidity")));
        weatherFeatureCityQuarterHisVO.setMaxWinds(LoadCalUtil.max(dataMap.get("maxWinds")));
        weatherFeatureCityQuarterHisVO.setMinColdness(LoadCalUtil.max(dataMap.get("minColdness")));
        weatherFeatureCityQuarterHisVO
            .setMinTemperatureHumidity(LoadCalUtil.max(dataMap.get("minTemperatureHumidity")));
        weatherFeatureCityQuarterHisVO.setMinWinds(LoadCalUtil.max(dataMap.get("minWinds")));
        weatherFeatureCityQuarterHisVO.setRainfall(BigDecimalUtils.addAllValue(dataMap.get("rainfall")));

        return weatherFeatureCityQuarterHisVO;
    }


    /**
     * 统计季气象特性
     *
     * @param weatherFeatureCityDayHisVOs 日气象特性列表
     */
    public List<WeatherFeatureCityQuarterHisDO> statisticsQuarterFeatures(
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs) throws Exception {

        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisVOs = new ArrayList<WeatherFeatureCityQuarterHisDO>();

        if (weatherFeatureCityDayHisVOs != null) {

            Map<String, List<WeatherFeatureCityDayHisDO>> WeatherFeatureMap = new HashMap<String, List<WeatherFeatureCityDayHisDO>>(); // Map<cityId_ym,List<WeatherFeatureCityDayHisDO>>
            for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOs) {
                String yq = DateUtil.getQuarterByDate(weatherFeatureCityDayHisVO.getDate());
                if (!WeatherFeatureMap.containsKey(weatherFeatureCityDayHisVO.getCityId() + "_" + yq)) {
                    WeatherFeatureMap.put(weatherFeatureCityDayHisVO.getCityId() + "_" + yq,
                        new ArrayList<WeatherFeatureCityDayHisDO>());
                }
                WeatherFeatureMap.get(weatherFeatureCityDayHisVO.getCityId() + "_" + yq)
                    .add(weatherFeatureCityDayHisVO);
            }

            for (String key : WeatherFeatureMap.keySet()) {
                try {
                    WeatherFeatureCityQuarterHisDO weatherFeatureCityQuarterHisVO = statisticsQuarterFeature(
                        WeatherFeatureMap.get(key));
                    weatherFeatureCityQuarterHisVOs.add(weatherFeatureCityQuarterHisVO);
                } catch (Exception e) {
                    log.error("统计季气象特性出错了", e);
                }
            }

        }

        return weatherFeatureCityQuarterHisVOs;
    }

    /**
     * 保存或更新
     */
    public WeatherFeatureCityQuarterHisDO doSaveOrUpdateWeatherFeatureCityQuarterHisDO(
        WeatherFeatureCityQuarterHisDO weatherFeatureCityQuarterHisVO) throws Exception {
        if (weatherFeatureCityQuarterHisVO == null) {
            return null;
        }
        WeatherFeatureCityQuarterHisDO oldVO = getWeatherFeatureCityQuarterHisDO(
            weatherFeatureCityQuarterHisVO.getCityId(), weatherFeatureCityQuarterHisVO.getYear(),
            weatherFeatureCityQuarterHisVO.getQuarter());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(weatherFeatureCityQuarterHisVO, oldVO, "createtime");
            oldVO.setId(id);
            return (WeatherFeatureCityQuarterHisDO) this.updateAndFlush(oldVO);
        } else {
            return (WeatherFeatureCityQuarterHisDO) this.createAndFlush(weatherFeatureCityQuarterHisVO);
        }
    }

    /**
     * 保存或更新
     */
    public List<WeatherFeatureCityQuarterHisDO> doSaveOrUpdateWeatherFeatureCityQuarterHisDOs(
        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisVOs) throws Exception {
        List<WeatherFeatureCityQuarterHisDO> vos = new ArrayList<WeatherFeatureCityQuarterHisDO>();
        for (WeatherFeatureCityQuarterHisDO weatherFeatureCityQuarterHisVO : weatherFeatureCityQuarterHisVOs) {
            try {
                vos.add(this.doSaveOrUpdateWeatherFeatureCityQuarterHisDO(weatherFeatureCityQuarterHisVO));
            } catch (Exception e) {
                log.error("保存季气象特性出错了", e);
            }
        }
        return vos;
    }

    public List<WeatherFeatureCityQuarterHisDO> listWeatherFeatureCityQuarterHisStatDOS(String cityId, String startYear,
                                                                                        String endYear)
            throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_snl_year", startYear);
        param.getQueryConditions().put("_snm_year", endYear);
        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisVOs = this.query(param).getDatas();
        return weatherFeatureCityQuarterHisVOs;
    }
}