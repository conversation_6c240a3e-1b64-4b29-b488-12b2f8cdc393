package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.google.common.collect.ImmutableMap;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyAssessDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("batchDataFilterServiceImpl")
public class BatchDataFilterServiceImpl implements BatchDataFilterService {

    public static final Map<String, String> batchMap = ImmutableMap.of("1", "第一批次", "2", "第二批次");

    @Autowired
    private AccuracyAssessDAO accuracyAssessDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Override
    public List<AccuracyAssessDO> filterAssessByBatchId(List<AccuracyAssessDO> assessDOS,  String batchId, Integer day) {
        //根据批次id设置的时间范围过滤
        List<AccuracyAssessDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyAssessDO one : assessDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                    .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }

        //取批次内最新的一条数据
        List<AccuracyAssessDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<AccuracyAssessDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getAssessName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                        + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE + src.getAssessType(), TreeMap::new, Collectors.toList())
        );

        List<AccuracyAssessDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(AccuracyAssessDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public Map<Integer, List<AccuracyAssessDO>> filterAssessByLisyBatchId(List<AccuracyAssessDO> assessDOS, String batchId,
        List<Integer> days) {
        Map<Integer, List<AccuracyAssessDO>> result = new HashMap<>();
        List<AccuracyAssessDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyAssessDO one : assessDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        for (Integer day : days) {
            List<AccuracyAssessDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                        .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
            Map<String, List<AccuracyAssessDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getAssessName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                    + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                    .getCityId() + Constants.SEPARATOR_BROKEN_LINE + src.getAssessType(), TreeMap::new, Collectors.toList())
            );

            List<AccuracyAssessDO> resultList = new ArrayList<>();
            map.forEach((key, list) -> {
                list.sort(Comparator.comparing(AccuracyAssessDO::getCreatetime).reversed());
                resultList.add(list.get(0));
            });
            result.put(day, resultList);
        }
        return result;
    }

    @Override
    public List<AccuracyCompositeDO> filterCompositeByBatchId(List<AccuracyCompositeDO> compositeDOS, String batchId, Integer day) {
        //根据批次id设置的时间范围过滤
        List<AccuracyCompositeDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyCompositeDO one : compositeDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                    .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }

        //取批次内最新的一条数据
        List<AccuracyCompositeDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getAccuracyName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                        + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
        );

        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public Map<Integer, List<AccuracyCompositeDO>> filterCompositeByListBatchId(List<AccuracyCompositeDO> compositeDOS,
        String batchId, List<Integer> days) {
        Map<Integer, List<AccuracyCompositeDO>> result = new HashMap<>();
        List<AccuracyCompositeDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyCompositeDO one : compositeDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        for (Integer day : days) {
            List<AccuracyCompositeDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                        .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
            Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getAccuracyName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                    + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                    .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
            );
            List<AccuracyCompositeDO> resultList = new ArrayList<>();
            map.forEach((key, list) -> {
                list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
                resultList.add(list.get(0));
            });
            result.put(day, resultList);
        }
        return result;
    }

    @Override
    public List<StatisticsCityDayFcBatchDO> filterStatisticsByBatchId(List<StatisticsCityDayFcBatchDO> statisticsDOS,  String batchId, Integer day) {
        //根据批次id设置的时间范围过滤
        List<StatisticsCityDayFcBatchDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (StatisticsCityDayFcBatchDO one : statisticsDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                    .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }

        //取批次内最新的一条数据
        List<StatisticsCityDayFcBatchDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<StatisticsCityDayFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate() + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
        );

        List<StatisticsCityDayFcBatchDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(StatisticsCityDayFcBatchDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public Map<Integer, List<StatisticsCityDayFcBatchDO>> filterStatisticsByListBatchId(
        List<StatisticsCityDayFcBatchDO> statisticsDOS, String batchId, List<Integer> days) {
        Map<Integer, List<StatisticsCityDayFcBatchDO>> result = new HashMap<>();
        List<StatisticsCityDayFcBatchDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (StatisticsCityDayFcBatchDO one : statisticsDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        for (Integer day : days) {
            List<StatisticsCityDayFcBatchDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                        .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
            Map<String, List<StatisticsCityDayFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate() + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                    .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
            );
            List<StatisticsCityDayFcBatchDO> resultList = new ArrayList<>();
            map.forEach((key, list) -> {
                list.sort(Comparator.comparing(StatisticsCityDayFcBatchDO::getCreatetime).reversed());
                resultList.add(list.get(0));
            });
            result.put(day, resultList);
        }
        return result;
    }

    @Override
    public Map<Integer, List<LoadCityFcBatchDO>> filterCityBatchByListBatchId(List<LoadCityFcBatchDO> cityDOS,
        String batchId, List<Integer> days) {
        Map<Integer, List<LoadCityFcBatchDO>> result = new HashMap<>();
        List<LoadCityFcBatchDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (LoadCityFcBatchDO one : cityDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        for (Integer day : days) {
            List<LoadCityFcBatchDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                        .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
            Map<String, List<LoadCityFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate() + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                    .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
            );
            List<LoadCityFcBatchDO> resultList = new ArrayList<>();
            map.forEach((key, list) -> {
                list.sort(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed());
                resultList.add(list.get(0));
            });
            result.put(day, resultList);
        }
        return result;
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> filterWeatherForecastDOByBatchId(List<WeatherCityFcLoadForecastDO> weatherForecastDOS, String batchId, Integer day) {
        //根据批次id设置的时间范围过滤
        List<WeatherCityFcLoadForecastDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (WeatherCityFcLoadForecastDO one : weatherForecastDOS) {
            if (one.getCreatetime() != null && com.tsintergy.lf.core.util.DateUtil.isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }

        //取批次内最新的一条数据
        List<WeatherCityFcLoadForecastDO> collect = filterList.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<WeatherCityFcLoadForecastDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate() + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
        );

        List<WeatherCityFcLoadForecastDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(WeatherCityFcLoadForecastDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }
}
