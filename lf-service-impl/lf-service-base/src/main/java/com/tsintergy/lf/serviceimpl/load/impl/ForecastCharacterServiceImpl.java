/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/6/9 17:25 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.ForecastCharacterService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadAndWeatherFeatureRespDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/6/9
 * @since 1.0.0
 */
@Service("forecastCharacterService")
public class ForecastCharacterServiceImpl implements ForecastCharacterService {

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Override
    public List<LoadAndWeatherFeatureRespDTO> findSimilarDayHisFeature(String cityId, String caliberId,
        List<Date> dateList)
        throws Exception {
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<LoadAndWeatherFeatureRespDTO> resp = new ArrayList<>();
        // 查询历史气象特性
        Map<Date, WeatherFeatureCityDayHisDO> weatherFeatureMap = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
                dateList, weatherCityId).stream()
            .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity()));

        // 查询历史负荷特性
        Map<Date, LoadFeatureCityDayHisDO> loadFeatureMap = loadFeatureCityDayHisService.listLoadFeature(
                cityId, caliberId, dateList).stream()
            .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity()));

        Map<Date, LoadCityHisDO> loadCityHisDOMap = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, dateList,
            caliberId).stream().collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity()));


        for (Date date : dateList) {
            WeatherFeatureCityDayHisDO weatherFeature = weatherFeatureMap == null ? null : weatherFeatureMap.get(date);
            LoadFeatureCityDayHisDO loadFeature = loadFeatureMap == null ? null : loadFeatureMap.get(date);
            LoadAndWeatherFeatureRespDTO result = new LoadAndWeatherFeatureRespDTO();
            result.setDate(date);
            if (loadFeature != null) {
                result.setMaxLoad(loadFeature.getMaxLoad());
                result.setMinLoad(loadFeature.getMinLoad());
                result.setAveLoad(loadFeature.getAveLoad());
                result.setMaxTime(loadFeature.getMaxTime());
                result.setMinTime(loadFeature.getMinTime());
                result.setDifferent(loadFeature.getDifferent());
                result.setGradient(loadFeature.getGradient());
                // 96点负荷数据
                LoadCityHisDO loadCityHisDO = loadCityHisDOMap == null ? null : loadCityHisDOMap.get(date);
                if (loadCityHisDO != null) {
                    Map<String, BigDecimal> loadMap = BasePeriodUtils
                        .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                    List<BigDecimal> peaks = new ArrayList<BigDecimal>();
                    List<BigDecimal> loadList = BasePeriodUtils
                        .toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                    List<String> peakSectionTimes = settingSystemDAO.getPeakSectionTime();
                    for (String column : loadMap.keySet()) {
                        BigDecimal load = loadMap.get(column);
                        if (null != load) {
                            column = column.substring(1);
                            if (peakSectionTimes != null && peakSectionTimes.contains(column)) {
                                peaks.add(load);
                            }
                        }
                    }
                    // 积分电量 = 96点负荷之和/4
                    result.setIntegralLoad(
                        BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
                    // 段峰电量 = 尖峰时段在数据库setting_system_init中，默认值为08：00~22:00
                    result.setPeakSectionLoad(
                        BigDecimalUtils.divide(BigDecimalUtils.addAllValue(peaks), new BigDecimal(4), 4));
                }
            }
            if (weatherFeature != null) {
                result.setHighestTemperature(weatherFeature.getHighestTemperature());
                result.setLowestTemperature(weatherFeature.getLowestTemperature());
                result.setAveTemperature(weatherFeature.getAveTemperature());
                result.setMaxWinds(weatherFeature.getMaxWinds());
                result.setRainfall(weatherFeature.getRainfall());
            }
            resp.add(result);
        }
        return resp;
    }
}
