package com.tsintergy.lf.serviceimpl.forecast.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.forecast.api.BaseAlgorithmInfoService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.BaseAlgorithmInfoDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.BaseAlgorithmInfoDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BaseAlgorithmInfoServiceImpl implements BaseAlgorithmInfoService {

    @Autowired
    BaseAlgorithmInfoDAO baseAlgorithmInfoDAO;

    @Override
    public BaseAlgorithmInfoDO findById(String id) {
        return baseAlgorithmInfoDAO.findById(id).get();
    }

    @Override
    public BaseAlgorithmInfoDO findByCode(String code) {
        return baseAlgorithmInfoDAO.findOne(JpaWrappers.<BaseAlgorithmInfoDO>lambdaQuery().eq(BaseAlgorithmInfoDO::getCode, code));
    }
}
