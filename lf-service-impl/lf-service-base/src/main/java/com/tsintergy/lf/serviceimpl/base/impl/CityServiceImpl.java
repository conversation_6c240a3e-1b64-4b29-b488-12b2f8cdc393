
package com.tsintergy.lf.serviceimpl.base.impl;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.CacheVO;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.entity.CacheRequest;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceimpl.base.dao.CityDAO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $Id: CityServiceImpl.java, v 0.1 2018-01-31 09:44:51 tao Exp $$
 */
@Service("cityService")
public class CityServiceImpl extends BaseServiceImpl implements CityService {

    private static final Logger logger = LogManager.getLogger(CityServiceImpl.class);

    @Autowired
    private CacheService cacheService;

    @Autowired
    private CityDAO cityDAO;


    @Override
    public CityDO doCreate(CityDO vo) throws Exception {
        try {
            return cityDAO.createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }


    @Override
    public List<String> findCityIdsByBelongId(String BelongId) throws Exception {
        //-----------等修改了redis后，直接cacheService.queryData取-------------------
        List<String> cityIds = new ArrayList<String>(20);
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_CITY_PREFIX + BelongId + ".*");
        List<CacheVO> cacheVOS = cacheService.queryCacheItemList(request).getData();
        if (cacheVOS != null) {
            for (CacheVO cacheVO : cacheVOS) {
                CityDO cityVO = (CityDO) cacheVO;
                cityIds.add(cityVO.getId());
            }
        }
        //-------------结束----------------------------------------------------------
        return cityIds;
    }

    @Override
    public List<CityDO> findCitysByBelongId(String belongId) throws Exception {
        //-----------等修改了redis后，直接cacheService.queryData取-------------------
        List<CityDO> cityVOS = new ArrayList<CityDO>(20);
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_CITY_PREFIX + belongId + ".*");
        List<CacheVO> cacheVOS = cacheService.queryCacheItemList(request).getData();
        if (cacheVOS != null) {
            for (CacheVO cacheVO : cacheVOS) {
                cityVOS.add((CityDO) cacheVO);
            }
        }
        //-------------结束----------------------------------------------------------
        return (List) cityVOS;
    }

    @Override
    public List<CityDO> findAllCitys() throws Exception {
        List<CityDO> cityVOS = new ArrayList<CityDO>(20);
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_CITY_PREFIX + "*" + ".*");
        List<CacheVO> list = cacheService.queryCacheItemList(request).getData();
        if (list != null) {
            for (CacheVO cacheVO : list) {
                cityVOS.add((CityDO) cacheVO);
            }
        }
        Collections.sort(cityVOS, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                if (o1 == null || ((CityDO) o1).getOrderNo() == null) {
                    return -1;
                } else if (o2 == null || ((CityDO) o2).getOrderNo() == null) {
                    return 1;
                }
                return ((CityDO) o1).getOrderNo().compareTo(((CityDO) o2).getOrderNo());
            }
        });
        return cityVOS;
    }

    @Override
    public CityDO findCityById(String id) throws Exception {
        //-----------等修改了redis后，直接cacheService.queryData取-------------------
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_CITY_PREFIX + "*." + id);
//        CacheResponse cacheResponse = cacheService.queryCacheItemList(request);

        List<CacheVO> cacheVOS = cacheService.queryCacheItemList(request).getData();
//        List<CacheVO> cacheVOS = null;
        //-------------结束----------------------------------------------------------
        if (cacheVOS == null || cacheVOS.size() < 1) {
            return this.cityDAO.findVOByPk(id);
        }
        return (CityDO) cacheVOS.get(0);
    }

    @Override
    public CityDO findCityByName(String city) throws BusinessException {
        try {
            List<CityDO> cityVOS = this.findAllCitys();
            for (CityDO cityVO : cityVOS) {
                if (city.equals(cityVO.getCity())) {
                    return cityVO;
                }
            }
            return null;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }

    }


    /**
     * 查询省会城市
     */
    @Override
    public CityDO findProvincialCapital(String cityId) throws Exception {
        CityDO city = this.findCityById(cityId);
        if (city.getType() == 1) {//如果是省用户  默认查询排序第二位
            List<CityDO> cityVOS = this.findAllCitys();
            cityVOS.sort(Comparator.comparing(CityDO::getOrderNo));
            city = cityVOS.get(1);
        }
        return city;
    }
    @Override
    public List<CityDO> findAllCitysExcludeAoLocal(boolean guangdon) throws Exception {
        List<CityDO> allCitys = findAllCitys();
        List<CityDO> collect = allCitys.stream()
            .filter(s -> {
                if (guangdon)
                {
                    return  !s.getId().equals("23") && !s.getId().equals("24");
                }else {
                    return  !s.getId().equals("23") && !s.getId().equals("24") &&!s.getId().equals("1");
                }
            })
            .collect(Collectors.toList());
        return collect;
    }

    @Override
    public CityDO findCityByType(Integer type) throws Exception {
        List<CityDO> cityList = cityDAO.findCityDOByType(type);
        if (cityList.size() != 0) {
            return cityList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public CityDO findCityVOByPk(Serializable pk) throws Exception {
        return cityDAO.findVOByPk(pk);
    }


    @Override
    public String findWeatherCityId(String cityId) throws Exception {
        if(StringUtils.isEmpty(cityId)){return null;}
        try {
            return this.findCityById(cityId).getWeatherCityId();
        } catch (Exception e) {
            e.printStackTrace();
            return this.findWeatherCityId(CityConstants.PROVINCE_ID);
        }
    }

    @Override
    public List<String> findWeatherCityIds(List<String> cityIds) throws Exception {
        if (cityIds == null) {
            List<String> collect = findAllCitys().stream().map(CityDO::getWeatherCityId)
                .collect(Collectors.toList());
            return collect.stream().distinct().collect(Collectors.toList());
        }
        List<String> weatherCityIds = new ArrayList<>();
        for (String cityId : cityIds) {
            weatherCityIds.add(findWeatherCityId(cityId));
        }
        return weatherCityIds;
    }

    @Override
    public Map<String, String> cityIdAndWeatherCityMap() throws Exception {
        List<CityDO> allCitys = this.findAllCitys();
        return allCitys.stream()
            .collect(Collectors.toMap(CityDO::getId, CityDO::getWeatherCityId));
    }
}
