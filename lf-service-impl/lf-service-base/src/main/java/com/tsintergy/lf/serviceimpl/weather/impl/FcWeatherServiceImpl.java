
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseFcWeatherService;
import com.tsintergy.lf.serviceapi.base.weather.api.FcWeatherService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;

@Service("fcWeatherService")
public class FcWeatherServiceImpl  implements FcWeatherService {




    private static final Logger logger = LogManager.getLogger(FcWeatherServiceImpl.class);

    @Autowired
    CityService cityService;

    private Map<String, BaseFcWeatherService> baseFcWeatherServiceMap;

    @Autowired
    public void setBaseFcWeatherServiceMap(List<BaseFcWeatherService> baseFcWeatherServiceLists){
        baseFcWeatherServiceMap = baseFcWeatherServiceLists.stream().collect(Collectors.toMap(
            baseFcWeatherService ->
                AnnotationUtils.findAnnotation(baseFcWeatherService.getClass(), FcWeatherDataSource.class).source()
            , v -> v, (v1, v2) -> v1));
    }

    @Override
    public <T extends BaseWeatherDO>List<T> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate,String source)
        throws Exception {
        if(cityId != null){
            cityId = cityService.findWeatherCityId(cityId);
        }
        BaseFcWeatherService baseFcWeatherService = baseFcWeatherServiceMap.get(source);
        return  baseFcWeatherService.findFcWeatherData(cityId,type,startDate,endDate);
    }
}
