package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.BasePeriod24VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcTqwDAO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("weatherCityFcTqwService")
public class WeatherCityFcTqwServiceImpl extends BaseServiceImpl implements WeatherCityFcTqwService {

    private static final Logger logger = LogManager.getLogger(WeatherCityFcTqwServiceImpl.class);

    @Autowired
    private WeatherStationFcService weatherStationFcService;
    @Autowired
    private WeatherCityFcTqwDAO weatherCityFcTqwDAO;

    @Autowired
    private WeatherCityBasedStationFcService weatherCityBasedStationFcService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Override
    public List<WeatherCityFcTqwDO> findWeatherCityFcTqwDOS(String cityId, Integer type, Date startDate, Date endDate) {
        return weatherCityFcTqwDAO.findAll(JpaWrappers.<WeatherCityFcTqwDO>lambdaQuery()
                .eq(StringUtils.isNotEmpty(cityId), WeatherCityFcTqwDO::getCityId, cityId)
                .eq(type != null, WeatherCityFcTqwDO::getType, type)
                .ge(startDate != null, WeatherCityFcTqwDO::getDate, startDate)
                .le(endDate != null, WeatherCityFcTqwDO::getDate, endDate));
    }

    @Override
    public List<WeatherCityFcTqwDO> findByCondition(List<String> cityIds, Date startDate, Date endDate) {
        return weatherCityFcTqwDAO.findAll(JpaWrappers.<WeatherCityFcTqwDO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(cityIds), WeatherCityFcTqwDO::getCityId, cityIds)
                .ge(startDate != null, WeatherCityFcTqwDO::getDate, startDate)
                .le(endDate != null, WeatherCityFcTqwDO::getDate, endDate));
    }

    @Override
    public WeatherCityFcTqwDO findByDate(String cityId, Integer type, Date date) {
        return weatherCityFcTqwDAO.findOne(JpaWrappers.<WeatherCityFcTqwDO>lambdaQuery()
                .eq(cityId != null, WeatherCityFcTqwDO::getCityId, cityId)
                .eq(type != null, WeatherCityFcTqwDO::getType, type)
                .eq(date != null, WeatherCityFcTqwDO::getDate, date));
    }

    @SneakyThrows
    @Override
    public void calcTqwFcWeather(Date startDate, Date endDate) {
        List<WeatherCityFcTqwDO> fcTqwDOS = this.findWeatherCityFcTqwDOS(null, null, startDate, endDate);
        List<WeatherCityBasedStationFcDO> fcDkyDOS = weatherCityBasedStationFcService.findWeatherCityBasedStationFcDOs(null, null, DateUtils.addDays(startDate, -1), endDate);

        Map<String, WeatherCityBasedStationFcDO> oldWeatherMap = fcDkyDOS.stream()
                .collect(Collectors.toMap(
                        w -> w.getCityId() + "-" + w.getType() + "-" + w.getDate().getTime(),
                        w -> w,
                        (existing, replacement) -> existing
                ));

        List<WeatherCityFcTqwDO> filledFcTqwDOS = new ArrayList<>();
        for (WeatherCityFcTqwDO newWeather : fcTqwDOS) {
            /*
             * 计算当天2-23点数据
             */
            String compositeKey = newWeather.getCityId() + "-" + newWeather.getType() + "-" + newWeather.getDate().getTime();
            WeatherCityBasedStationFcDO oldWeather = oldWeatherMap.get(compositeKey);
            if (oldWeather != null) {
                // 处理从T0200开始的每三个小时的数据
                for (int hour = 2; hour < 23; hour += 3) {
                    // 计算当前时间点和下一个时间点, 如果逐三小时的数据为空，则取逐六小时
                    String currentTime = String.format("T%02d00", hour);
                    String nextTime = String.format("T%02d00", (hour + 3) % 24);
                    if (getFieldByTime(newWeather, nextTime) == null) {
                        nextTime = String.format("T%02d00", (hour + 6) % 24);
                    }

                    // 计算两个端点的差值Δ
                    BigDecimal delta0 = calculateDelta(newWeather, oldWeather, currentTime);
                    BigDecimal delta1 = calculateDelta(newWeather, oldWeather, nextTime);

                    // 获取当前时间和下一个时间的小时和分钟
                    int currentHour = Integer.parseInt(currentTime.substring(1, 3));
                    int nextHour = Integer.parseInt(nextTime.substring(1, 3));

                    LocalTime start = LocalTime.of(currentHour, 0);
                    LocalTime end = LocalTime.of(nextHour, 0);

                    // 计算两个时间点之间总共多少个15分钟间隔
                    long totalMinutes = ChronoUnit.MINUTES.between(start, end);
                    int totalIntervals = (int) (totalMinutes / 15);

                    // 遍历每个15分钟间隔点
                    for (int i = 1; i < totalIntervals; i++) {
                        LocalTime intermediate = start.plusMinutes(i * 15L);
                        String intermediateTime = String.format("T%02d%02d", intermediate.getHour(), intermediate.getMinute());

                        // 计算比例 p
                        BigDecimal p = BigDecimal.valueOf(i * 15L).divide(BigDecimal.valueOf(totalMinutes), 6, RoundingMode.HALF_UP);

                        // 线性插值
                        BigDecimal deltaIntermediate = delta0.add(p.multiply(delta1.subtract(delta0)));

                        // 获取旧数据中的值并加上插值得到新值
                        BigDecimal oldIntermediateValue = getFieldByTime(oldWeather, intermediateTime);
                        if (oldIntermediateValue != null) {
                            BigDecimal newValue = oldIntermediateValue.add(deltaIntermediate);
                            PeriodDataUtil.setFieldValue(newWeather, intermediateTime, newValue);
                        }
                    }
                }
                weatherCityFcTqwDAO.saveOrUpdateByTemplate(newWeather);
            }

            /*
             * 计算昨日23-今日2点数据
             */
            Date yesterday = DateUtils.addDays(newWeather.getDate(), -1);
            String yesterdayKey = newWeather.getCityId() + "-" + newWeather.getType() + "-" + yesterday.getTime();
            WeatherCityBasedStationFcDO yesterdayOldWeather = oldWeatherMap.get(yesterdayKey);
            if (yesterdayOldWeather != null) {
                WeatherCityFcTqwDO weatherCityFcTqwDO = new WeatherCityFcTqwDO();
                BeanUtils.copyProperties(yesterdayOldWeather, weatherCityFcTqwDO);
                weatherCityFcTqwDO.setId(null);
                // 获取前一日的 WeatherCityFcTqwDO 作为起点
                WeatherCityFcTqwDO yesterdayWeather = this.findByDate(yesterdayOldWeather.getCityId(), yesterdayOldWeather.getType(), yesterday);
                if (yesterdayWeather == null) continue;
                String currentTime = "T2300";
                long totalMinutes = 180;
                LocalTime start = LocalTime.of(23, 0);
                BigDecimal fieldByTime = getFieldByTime(yesterdayWeather, currentTime);
                if (fieldByTime == null) {
                    currentTime = "T2000";
                    totalMinutes = 360;
                    start = LocalTime.of(20, 0);
                }
                String nextTime = "T0200";

                BigDecimal delta0 = calculateDelta(yesterdayWeather, yesterdayOldWeather, currentTime);
                BigDecimal delta1 = calculateDelta(newWeather, oldWeather, nextTime);

                int totalIntervals = (int) (totalMinutes / 15);

                for (int i = 1; i < totalIntervals; i++) {
                    LocalTime intermediate = start.plusMinutes(i * 15L);
                    String intermediateTime = String.format("T%02d%02d", intermediate.getHour(), intermediate.getMinute());

                    // 计算比例 p
                    BigDecimal p = BigDecimal.valueOf(i * 15L)
                            .divide(BigDecimal.valueOf(totalMinutes), 6, RoundingMode.HALF_UP);

                    // 线性插值
                    BigDecimal deltaIntermediate = delta0.add(p.multiply(delta1.subtract(delta0)));

                    // 获取旧数据中的值并加上插值得到新值
                    BigDecimal oldIntermediateValue = getFieldByTime(oldWeather, intermediateTime);
                    if (oldIntermediateValue != null) {
                        BigDecimal newValue = oldIntermediateValue.add(deltaIntermediate);

                        // 判断是否属于跨天后的凌晨时间段（00:15 - 01:45）
                        boolean isNextDay = intermediate.getHour() < 2;

                        if (isNextDay) {
                            // 写入今天的对象（newWeather）
                            String targetTime = String.format("T%02d%02d", intermediate.getHour(), intermediate.getMinute());
                            PeriodDataUtil.setFieldValue(newWeather, targetTime, newValue);
                        } else {
                            // 写入昨天的对象（yesterdayWeather）
                            PeriodDataUtil.setFieldValue(yesterdayWeather, intermediateTime, newValue);
                        }
                    }
                }
                yesterdayWeather.setT2400(newWeather.getT0000());
                weatherCityFcTqwDAO.saveOrUpdateByTemplate(yesterdayWeather);
                weatherCityFcTqwDAO.saveOrUpdateByTemplate(newWeather);
                filledFcTqwDOS.add(yesterdayWeather);
            }
        }
    }

    @SneakyThrows
    private static BigDecimal getFieldByTime(BasePeriod24VO target, String timeStr) {
        return (BigDecimal) PeriodDataUtil.getFieldValue(target, timeStr);
    }

    private BigDecimal calculateDelta(WeatherCityFcTqwDO newWeather, WeatherCityBasedStationFcDO oldWeather, String time) {
        BigDecimal newValue = getFieldByTime(newWeather, time);
        BigDecimal oldValue = getFieldByTime(oldWeather, time);

        // 处理可能的空值情况
        if (newValue == null) newValue = BigDecimal.ZERO;

        return newValue.subtract(oldValue);
    }

    @SneakyThrows
    @Override
    public void generateFittedWeatherCurveByFeature(String cityId, Date startDate, Date endDate) {
        Date weatherStartDate = DateUtil.getMoveDay(startDate, -5);
        Date weatherEndDate = DateUtil.getMoveDay(startDate, -1);
        List<WeatherCityFcTqwDO> weatherCityFcDOs = this.findWeatherCityFcTqwDOS(cityId, WeatherEnum.TEMPERATURE.getType(), weatherStartDate, weatherEndDate);
        if (CollectionUtils.isEmpty(weatherCityFcDOs)) {
            return;
        }
        weatherCityFcDOs = weatherCityFcDOs.stream().filter(src -> countNonNullElements(src.getloadList()) == 96)
                .collect(Collectors.toList());

        //统计预报气象特性
        List<WeatherCityHisDO> weatherCityHisDOS = weatherCityFcDOs.stream().map(
                src -> {
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(src, weatherCityHisDO, "id");
                    return weatherCityHisDO;
                }
        ).collect(Collectors.toList());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureStatService.statisticsDayFeature(weatherCityHisDOS);
        Map<String, WeatherFeatureCityDayHisDO> featureCityDayHisDOMap = weatherFeatureCityDayHisDOS.stream()
                .collect(Collectors.toMap(t -> t.getDate() + "-" + t.getCityId(), t -> t));

        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService
                .getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
        if (CollectionUtils.isEmpty(weatherFeatureCityDayFcDOList)) {
            return;
        }

        //归一化后的数据集合
        List<BigDecimal> sumValues = BigDecimalFunctions.createList(BigDecimal.ZERO, Constants.WEATHER_CURVE_POINT_NUM);
        for (WeatherCityFcTqwDO weatherCityFcTqwDO : weatherCityFcDOs) {
            WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = featureCityDayHisDOMap.get(weatherCityFcTqwDO.getDate() + "-" + weatherCityFcTqwDO.getCityId());
            if (weatherFeatureCityDayHisDO == null) {
                continue;
            }
            BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
            BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
            List<BigDecimal> normWeather = getNormWeather(weatherCityFcTqwDO.getloadList(), highestTemperature, lowestTemperature);
            sumValues = BigDecimalFunctions.listAdd(sumValues, normWeather);
        }
        //取平均
        List<BigDecimal> result = BigDecimalFunctions.listDivideValue(sumValues, new BigDecimal(weatherCityFcDOs.size()));

        for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO : weatherFeatureCityDayFcDOList) {
            BigDecimal lowestTemperature = weatherFeatureCityDayFcDO.getLowestTemperature();
            BigDecimal highestTemperature = weatherFeatureCityDayFcDO.getHighestTemperature();
            //获取最终值
            List<BigDecimal> normWeather = getWeatherValueByNorm(result, highestTemperature, lowestTemperature);
            if (org.springframework.util.CollectionUtils.isEmpty(normWeather)){
                continue;
            }
            WeatherCityFcTqwDO weatherCityFcTqwDO = new WeatherCityFcTqwDO();
            weatherCityFcTqwDO.setCityId(weatherFeatureCityDayFcDO.getCityId());
            weatherCityFcTqwDO.setDate(weatherFeatureCityDayFcDO.getDate());
            weatherCityFcTqwDO.setType(WeatherEnum.TEMPERATURE.getType());
            BasePeriodUtils.setAllFiled(weatherCityFcTqwDO, ColumnUtil.listToMap(normWeather, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherCityFcTqwDAO.saveOrUpdateByTemplate(weatherCityFcTqwDO);
        }
    }

    private Integer countNonNullElements(List<BigDecimal> weatherList) {
        if (weatherList == null) {
            return 0;
        }
        return (int) weatherList.stream()
                .filter(Objects::nonNull)
                .count();
    }

    private List<BigDecimal> getNormWeather(List<BigDecimal> weatherList, BigDecimal highestTemperature,
                                            BigDecimal lowestTemperature) {
        if (highestTemperature == null || lowestTemperature == null){
            return null;
        }
        List<BigDecimal> result = new ArrayList<>();
        for (BigDecimal bigDecimal : weatherList) {
            if (bigDecimal!=null){
                BigDecimal value = (bigDecimal.subtract(lowestTemperature))
                        .divide(highestTemperature.subtract(lowestTemperature), 2,
                                RoundingMode.HALF_UP);
                result.add(value);
            }
        }
        return result;
    }

    private List<BigDecimal> getWeatherValueByNorm(List<BigDecimal> values, BigDecimal highestTemperature,
                                                   BigDecimal lowestTemperature) {
        if (highestTemperature == null || lowestTemperature == null){
            return null;
        }
        List<BigDecimal> result = new ArrayList<>();
        for (BigDecimal bigDecimal : values) {
            if (bigDecimal!=null){
                BigDecimal value = (bigDecimal.multiply(highestTemperature.subtract(lowestTemperature))).add(lowestTemperature);
                result.add(value);
            }
        }
        return result;
    }

    @SneakyThrows
    @Override
    public void fillTqwFcWeather(Date startDate, Date endDate) {
        List<WeatherCityFcTqwDO> fcTqwDOS = this.findWeatherCityFcTqwDOS(null, null, startDate, endDate);
        List<WeatherCityBasedStationFcDO> fcDkyDOS = weatherCityBasedStationFcService.findWeatherCityBasedStationFcDOs(null, null, startDate, endDate);
        Map<String, WeatherCityBasedStationFcDO> oldWeatherMap = fcDkyDOS.stream()
                .collect(Collectors.toMap(
                        w -> w.getCityId() + "-" + w.getType() + "-" + w.getDate().getTime(),
                        w -> w,
                        (existing, replacement) -> existing
                ));
        List<WeatherCityFcTqwDO> saveList = new ArrayList<>();
        for (WeatherCityFcTqwDO weatherCityFcTqwDO : fcTqwDOS) {
            String key = weatherCityFcTqwDO.getCityId() + "-" + weatherCityFcTqwDO.getType() + "-" + weatherCityFcTqwDO.getDate().getTime();
            WeatherCityBasedStationFcDO weatherCityBasedStationFcDO = oldWeatherMap.get(key);
            if(weatherCityBasedStationFcDO == null){
                continue;
            }
            WeatherCityFcTqwDO saveDO = new WeatherCityFcTqwDO();
            BeanUtils.copyProperties(weatherCityFcTqwDO, saveDO, "id");
            saveDO.setT0000(weatherCityBasedStationFcDO.getT0000());
            saveDO.setT0015(weatherCityBasedStationFcDO.getT0015());
            saveDO.setT2400(weatherCityBasedStationFcDO.getT2400());
            List<BigDecimal> bigDecimals = saveDO.getloadList();
            weatherCityHisService.extracted(bigDecimals);
            Map<String, BigDecimal> bigDecimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(saveDO, bigDecimalMap);
            saveList.add(saveDO);
        }
        saveList.forEach(weatherCityFcTqwDO -> weatherCityFcTqwDAO.saveOrUpdateByTemplate(weatherCityFcTqwDO));
    }

    @Override
    public void clctToBasic(Date startDate, Date endDate) {
        // 查询开始日期前一天的气象，为了将前一天2400点修正
        startDate = DateUtils.addDays(startDate, -1);
        List<WeatherCityFcTqwDO> clctList = this.findWeatherCityFcTqwDOS(null, null, startDate, endDate);
        if (CollectionUtils.isNotEmpty(clctList)) {

            // 修改前一天2400点为当前0000点,并且把2315，2330，2345都置为null，保证这几个点正常拟合，避免出现突变
            Map<String, WeatherCityFcTqwDO> clctDOMap = clctList.stream().collect(
                    Collectors.toMap(clctDO -> clctDO.getCityId() + clctDO.getDate().getTime() + clctDO.getType(),
                            Function.identity(), (o, n) -> n));
            for (WeatherCityFcTqwDO weatherCityHisClctDO : clctList) {
                String key = weatherCityHisClctDO.getCityId() + DateUtils.addDays(weatherCityHisClctDO.getDate(), -1).getTime()
                        + weatherCityHisClctDO.getType();
                WeatherCityFcTqwDO yesterdayClctDO = clctDOMap.get(key);
                if (yesterdayClctDO != null) {
                    yesterdayClctDO.setT2315(null);
                    yesterdayClctDO.setT2330(null);
                    yesterdayClctDO.setT2345(null);
                    yesterdayClctDO.setT2400(weatherCityHisClctDO.getT0000());
                }
            }

            // 气象数据插值
            List<WeatherCityFcTqwDO> basicList = new ArrayList<>();
            for (WeatherCityFcTqwDO weatherCityHisClctDO : clctList) {
                try {
                    PeriodDataUtil.do24To96VO(weatherCityHisClctDO);
                    WeatherCityFcTqwDO basicDO = new WeatherCityFcTqwDO();
                    BeanUtils.copyProperties(weatherCityHisClctDO, basicDO);
                    basicDO.setId(null);
                    basicList.add(basicDO);
                } catch (Exception e) {
                    log.error("气象24点转96点异常", e);
                }
            }
            basicList.forEach(weatherCityHisDO -> weatherCityFcTqwDAO.saveOrUpdateByTemplate(weatherCityHisDO));
        }
    }
}
