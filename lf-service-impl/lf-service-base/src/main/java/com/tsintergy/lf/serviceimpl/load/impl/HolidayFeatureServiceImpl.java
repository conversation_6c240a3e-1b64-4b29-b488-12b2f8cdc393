/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wa<PERSON><PERSON>
 * Date:  2020/3/4 2:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.SeasonEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.core.util.LunarCalendar;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService;
import com.tsintergy.lf.serviceapi.base.load.api.HolidayFeatureService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.CityFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.HolidayFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.MaxDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.PeakDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/3/4
 * @since 1.0.0
 */
@Service(value = "holidayFeatureService")
public class HolidayFeatureServiceImpl implements HolidayFeatureService {

    @Autowired
    private LoadCityHisService loadCityHisService;


    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private SimilarDayService similarDayService;

    @Autowired
    private CityService cityService;


    /**
     * 获取长假期负荷特性分析对比
     *
     * @param cityId
     * @param startDate
     * @param endDate
     * @param type
     * @param year
     * @return
     * @throws Exception
     */
    @Override
    public List<HolidayFeatureDTO> getHolidayFeature(String cityId, String caliberId, Date startDate, Date endDate, Integer type, String year) throws Exception {
        Date lastDay = DateUtils.addDays(startDate, -1);
        List<LoadCityHisDO> LoadCityHisDOList = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, lastDay, endDate);
        //统计负荷特性
        List<HolidayFeatureDTO> featureDTOS = statLoadFeature(LoadCityHisDOList);
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        //转map
        Map<Date, HolidayFeatureDTO> featureDTOMap = featureDTOS.stream().collect(Collectors.toMap(HolidayFeatureDTO::getDate, Function.identity(), (o, n) -> n));
        List<HolidayFeatureDTO> holidayFeatureDTOList = new ArrayList<>();
        for (Date date : dateList) {
            Date yesterday = DateUtils.addDays(date, -1);
            HolidayFeatureDTO featureDTO = featureDTOMap.get(date);
            if (featureDTO == null) {
                continue;
            }
            //和昨日相比（计算环比增长）
            HolidayFeatureDTO holidayFeatureDTO = featureDTOMap.get(yesterday);
            if (holidayFeatureDTO != null) {
                //早 午  晚的环比增长
                featureDTO.setmLink(calcBasicsNumber(featureDTO.getmMax(), holidayFeatureDTO.getmMax()));
                featureDTO.setaLink(calcBasicsNumber(featureDTO.getaMax(), holidayFeatureDTO.getaMax()));
                featureDTO.setnLink(calcBasicsNumber(featureDTO.getnMin(), holidayFeatureDTO.getnMin()));
                featureDTO.seteLink(calcBasicsNumber(featureDTO.geteMax(), holidayFeatureDTO.geteMax()));
            }
            //同比增长的日期
            Date basicDate = null;
            HolidayFeatureDTO sameDTO = null;
            //早 午 晚的同比增长
            if (type == 1) {
                //假期期间同一日
                basicDate = transformDate(year, date);
            } else {
                String basicDateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR).substring(5, 10);
                //公历同一日（只需要修改年份即可）
                basicDate = DateUtils.string2Date(year + "-" + basicDateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            }
            List<LoadCityHisDO> sameList = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, basicDate, basicDate);
            if (sameList != null && sameList.size() > 0) {
                sameDTO = statLoadFeature(sameList).get(0);
            }
            if (sameDTO != null) {
                //早 午  晚的同比增长
                featureDTO.setmBasics(calcBasicsNumber(featureDTO.getmMax(), sameDTO.getmMax()));
                featureDTO.setaBasics(calcBasicsNumber(featureDTO.getaMax(), sameDTO.getaMax()));
                featureDTO.seteBasics(calcBasicsNumber(featureDTO.geteMax(), sameDTO.geteMax()));
                featureDTO.setnBasics(calcBasicsNumber(featureDTO.getnMin(), sameDTO.getnMin()));
            }
            //早晚高峰的峰值差
            featureDTO.setDifference(LoadCalUtil.sub(holidayFeatureDTO.getmMax(), holidayFeatureDTO.geteMax()));
            holidayFeatureDTOList.add(featureDTO);
        }
        return holidayFeatureDTOList;
    }


    /**
     * 转换时间（找到日期的同比年份的公历日期）
     *
     * @param year
     * @param date
     * @return
     * @throws Exception
     */
    private Date transformDate(String year, Date date) throws Exception {
        //先将时间转换为农历日期
        String LunarDate = LunarCalendar.solarToLunar(date);
        String dateYear = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR).substring(0, 4);
        String lunarYear = LunarDate.substring(0, 4);
        Integer intYear = Integer.valueOf(year);
        if (!dateYear.equals(lunarYear)) {
            intYear = intYear - 1;
        }
        //根据所选的年份重新拼凑农历日期
        LunarDate = intYear + "-" + LunarDate.substring(5, 10);
        //农历转回公历
        Date solarDate = LunarCalendar.lunarToSolar(LunarDate, false);
        return solarDate;
    }


    private List<HolidayFeatureDTO> statLoadFeature(List<LoadCityHisDO> LoadCityHisDOS) throws Exception {
        Map<Integer, List<String>> spring = settingSystemService.getTimes("spring_winter");
        Map<Integer, List<String>> summer = settingSystemService.getTimes("aestivo-autumnal");
        if (spring == null || summer == null) {
            throw TsieExceptionUtils.newBusinessException("数据库中缺少初始化早、午、晚高峰的时间段，请补充！");
        }
        List<HolidayFeatureDTO> holidayFeatureDTOS = new ArrayList<>();
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            HolidayFeatureDTO holidayFeatureDTO = new HolidayFeatureDTO();
            Date date = LoadCityHisDO.getDate();
            holidayFeatureDTO.setDate(date);
            //step 1: 先判断日期是哪个季节，取不同的早午晚高峰(春冬一样，夏秋一样)
            int season = DateUtil.getSeason(date);
            //根据季节获取不同的早高晚峰时间
            PeakDTO peakDTO = getPeakTime(season, spring, summer);
            //切割为早高峰，午高峰，晚高峰数据
            Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            Map<String, BigDecimal> morningMap = new HashMap<>();
            Map<String, BigDecimal> afterMap = new HashMap<>();
            Map<String, BigDecimal> eveningMap = new HashMap<>();
            Map<String, BigDecimal> nightMap = new HashMap<>();
            for (String column : loadMap.keySet()) {
                BigDecimal load = loadMap.get(column);
                if (null != load) {
                    column = column.substring(1);
                    if (peakDTO.getMorning() != null && peakDTO.getMorning().contains(column)) {
                        morningMap.put(column, load);
                    } else if (peakDTO.getAfternoon() != null && peakDTO.getAfternoon().contains(column)) {
                        afterMap.put(column, load);
                    } else if (peakDTO.getNight() != null && peakDTO.getNight().contains(column)) {
                        nightMap.put(column, load);
                    } else if (peakDTO.getEvening() != null && peakDTO.getEvening().contains(column)) {
                        eveningMap.put(column, load);
                    }
                }
            }
            //分别取最大值及最大值的时刻
            MaxDTO dto = statMaxLoad(morningMap);
            holidayFeatureDTO.setmMax(dto.getMaxLoad());
            holidayFeatureDTO.setmMaxTime(dto.getMaxTime());
            MaxDTO afterDTO = statMaxLoad(afterMap);
            holidayFeatureDTO.setaMax(afterDTO.getMaxLoad());
            holidayFeatureDTO.setaMaxTime(afterDTO.getMaxTime());
            MaxDTO evenDTO = statMaxLoad(eveningMap);
            holidayFeatureDTO.seteMax(evenDTO.getMaxLoad());
            holidayFeatureDTO.seteMaxTime(evenDTO.getMaxTime());
            MaxDTO nightDTO = statMaxLoad(nightMap);
            holidayFeatureDTO.setnMin(nightDTO.getMinLoad());
            holidayFeatureDTO.setnMinTime(nightDTO.getMinTime());
            holidayFeatureDTOS.add(holidayFeatureDTO);
        }
        return holidayFeatureDTOS;
    }

    /**
     * 根据不同的季节获取早午晚高峰的时间
     *
     * @param season
     * @param spring
     * @param summer
     * @return
     * @throws Exception
     */
    private PeakDTO getPeakTime(Integer season, Map<Integer, List<String>> spring, Map<Integer, List<String>> summer) throws Exception {
        List<String> morning = null;
        List<String> after = null;
        List<String> evening = null;
        List<String> night = null;
        if (season == SeasonEnum.Spring.getSeason() || season == SeasonEnum.Winter.getSeason()) {
            morning = spring.get(0);
            after = spring.get(1);
            evening = spring.get(2);
            night = spring.get(3);
        } else {
            morning = summer.get(0);
            after = summer.get(1);
            evening = summer.get(2);
            night = summer.get(3);
        }
        PeakDTO peakDTO = new PeakDTO();
        peakDTO.setMorning(morning);
        peakDTO.setAfternoon(after);
        peakDTO.setEvening(evening);
        peakDTO.setNight(night);
        return peakDTO;
    }


    /**
     * 统计map中最大最小值及最大时间最小时间出现的值
     *
     * @param map
     * @return
     * @throws Exception
     */
    private MaxDTO statMaxLoad(Map<String, BigDecimal> map) throws Exception {
        MaxDTO maxDTO = new MaxDTO();
        BigDecimal max = null;
        String maxTime = null;
        BigDecimal mix = null;
        String mixTime = null;
        for (String key : map.keySet()) {
            BigDecimal load = map.get(key);
            if (null == load) {
                continue;
            }
            //循环去最大值
            if (null == max) {
                max = load;
                maxTime = key;
            } else if (load.compareTo(max) > 0) {
                max = load;
                maxTime = key;
            }
            //循环取最小值
            if (null == mix) {
                mix = load;
                mixTime = key;
            } else if (load.compareTo(mix) < 0) {
                mix = load;
                mixTime = key;
            }
        }
        maxDTO.setMaxLoad(max);
        maxDTO.setMaxTime(maxTime);
        maxDTO.setMinLoad(mix);
        maxDTO.setMinTime(mixTime);
        return maxDTO;
    }


    /**
     * 环比 =（对比日数值-参考日数值）/对比日数值X100%（亦适用于同比）
     *
     * @param comparison 对比日
     * @param reference  参考日
     * @return
     * @throws Exception
     */
    private BigDecimal calcBasicsNumber(BigDecimal comparison, BigDecimal reference) throws Exception {
        BigDecimal number = null;
        if (comparison != null && reference != null) {
            BigDecimal sub = LoadCalUtil.sub(comparison, reference);
            number = LoadCalUtil.divide(sub, comparison, 4);
        }
        return number;
    }

    /**
     * 获取长假期地区负荷特性
     *
     * @param
     * @param caliberId
     * @param time
     * @param date
     * @param userId
     * @return
     * @throws Exception
     */
    @Override
    public List<CityFeatureDTO> getCityFeature(String caliberId, String time, Date date, Integer type,
        String userId) throws Exception {
        Date lastDate = DateUtils.addDays(date, -1);
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisService.getLoadCityHisDOS(null, caliberId, lastDate, date);
        List<LoadCityHisDO> hisVOS = processData(LoadCityHisDOS);
        //先统计当日负荷和昨日负荷
        List<CityFeatureDTO> cityFeatureDTOS = statCityFeature(hisVOS, time, type);
        //转map
        Map<String, CityFeatureDTO> featureDTOMap = new HashMap<>();
        for (CityFeatureDTO cityFeatureDTO : cityFeatureDTOS) {
            String key = cityFeatureDTO.getCityId() + cityFeatureDTO.getDate().getTime();
            featureDTOMap.put(key, cityFeatureDTO);
        }
        List<CityFeatureDTO> featureDTOS = new ArrayList<>();
        for (CityFeatureDTO featureDTO : cityFeatureDTOS) {
            String key = featureDTO.getCityId() + featureDTO.getDate().getTime();
            CityFeatureDTO cityFeatureDTO = featureDTOMap.get(key);
            if (!featureDTO.getDate().equals(date)) {
                continue;
            }
            //昨日
            Date lastDay = DateUtils.addDays(featureDTO.getDate(), -1);
            String lastKey = featureDTO.getCityId() + lastDay.getTime();
            CityFeatureDTO dto = featureDTOMap.get(lastKey);
            if (dto != null) {
                BigDecimal lastMax = dto.getpMax();
                cityFeatureDTO.setpBefore(lastMax);
                //计算环比增长
                cityFeatureDTO.setpLink(calcBasicsNumber(cityFeatureDTO.getpMax(), lastMax));
                Date searchStart = DateUtils.addDays(date, -14);
                Date searchEnd = DateUtils.addDays(date, -1);
                //查询气象相似度
                List<SimilarDateBean> similarDateBeans = null;
                try {
                    similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityFeatureDTO.getCityId(), caliberId, date, searchStart, searchEnd, 1, userId);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (similarDateBeans != null && similarDateBeans.size() > 0) {
                        //获取相似日期
                        Date similarDate = similarDateBeans.get(0).getDate();
                        List<LoadCityHisDO> cityHisVOS = loadCityHisService.getLoadCityHisDOS(featureDTO.getCityId(), caliberId, similarDate, similarDate);
                        List<LoadCityHisDO> hisVOList = processData(cityHisVOS);
                        List<CityFeatureDTO> cityFeatureDTOList = statCityFeature(hisVOList, time, type);
                        if (cityFeatureDTOList.size() > 0 && cityFeatureDTOList != null) {
                            BigDecimal maxLoad = cityFeatureDTOList.get(0).getpMax();
                            cityFeatureDTO.setpHoliday(maxLoad);
                            //计算恢复比例
                            BigDecimal recovery = BigDecimalUtils.divide(lastMax, maxLoad, 4);
                            cityFeatureDTO.setpRecovery(recovery);
                        }
                    }
                }
            }
            featureDTOS.add(cityFeatureDTO);
        }
        return featureDTOS;
    }


    /**
     * 特殊处理（广东） 珠海的数据=珠海+澳门
     *
     * @param LoadCityHisDOS
     * @return
     * @throws Exception
     */
    private List<LoadCityHisDO> processData(List<LoadCityHisDO> LoadCityHisDOS) throws Exception {
        List<LoadCityHisDO> newData = new ArrayList<>();
        //珠海数据 需要特殊处理（加澳门的数据）
        List<LoadCityHisDO> hisVOS = LoadCityHisDOS.stream().filter(LoadCityHisDO -> LoadCityHisDO.getCityId().equals("4") || LoadCityHisDO.getCityId().equals("23")).collect(Collectors.toList());
        LoadCityHisDOS.removeAll(hisVOS);
        Map<Date, List<LoadCityHisDO>> listMap = hisVOS.stream().collect(Collectors.groupingBy(LoadCityHisDO::getDate));
        for (Date key : listMap.keySet()) {
            List<LoadCityHisDO> hisVOList = listMap.get(key);
            Map<String, BigDecimal> map = LoadCalUtil.addListObject(hisVOList, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityHisDO LoadCityHisDO = new LoadCityHisDO();
            BasePeriodUtils.setAllFiled(LoadCityHisDO, map);
            LoadCityHisDO.setCityId("4");
            LoadCityHisDO.setDate(new java.sql.Date(key.getTime()));
            newData.add(LoadCityHisDO);
        }
        newData.addAll(LoadCityHisDOS);
        return newData;
    }


    private List<CityFeatureDTO> statCityFeature(List<LoadCityHisDO> LoadCityHisDOS, String time, Integer type) throws Exception {
        List<CityFeatureDTO> featureDTOS = new ArrayList<>();
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            String cityId = LoadCityHisDO.getCityId();
            CityDO CityDO = cityService.findCityById(cityId);
            if (CityDO.getType() == 1) {
                continue;
            }
            //计算最大最小值
            MaxDTO maxDTO = calcMax(LoadCityHisDO, time, type);
            CityFeatureDTO cityFeatureDTO = new CityFeatureDTO();
            cityFeatureDTO.setCityId(cityId);
            cityFeatureDTO.setCityName(CityDO.getCity());
            cityFeatureDTO.setDate(LoadCityHisDO.getDate());
            cityFeatureDTO.setpMax(maxDTO.getMaxLoad());
            cityFeatureDTO.setpMaxTime(maxDTO.getMaxTime());
            featureDTOS.add(cityFeatureDTO);
        }
        return featureDTOS;
    }


    private MaxDTO calcMax(LoadCityHisDO LoadCityHisDO, String time, Integer type) throws Exception {
        Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
//        List<String> columnsBetween = ColumnUtil.getColumnsBetween(startTime, endTime, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO, false);
//        for (String column : loadMap.keySet()) {
//            BigDecimal load = loadMap.get(column);
//            if (null != load) {
//                column = column.substring(1);
//                if (columnsBetween.contains(column)) {
//                    dataMap.put(column, load);
//                }
//            }
//        }
        MaxDTO maxDTO = new MaxDTO();
        if (type == 1) {
            //计算的是采集时刻的负荷
            time = "t"+time.replace(":", "");
            BigDecimal load = loadMap.get(time);
            maxDTO.setMaxLoad(load);
            maxDTO.setMaxTime(time);

        } else {
            maxDTO = statMaxLoad(loadMap);
        }
        return maxDTO;
    }

}