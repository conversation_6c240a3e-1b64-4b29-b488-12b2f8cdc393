
package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.forecast.api.ThresholdCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.ThresholdCityFcDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.ThresholdCityFcDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $Id: ThresholdCityFcServiceImpl.java, v 0.1 2018-02-28 15:20:50 tao Exp $$
 */

@Service("thresholdCityFcService")
public class ThresholdCityFcServiceImpl extends BaseServiceImpl implements ThresholdCityFcService {

    @Autowired
    ThresholdCityFcDAO thresholdCityFcDAO;

        @Override
    public ThresholdCityFcDO findThresholdCityFcVO(String cityId, Date date, Integer type, String caliberId) throws Exception {
        List<ThresholdCityFcDO> thresholdCityFcDOS = thresholdCityFcDAO.findThresholdCityFcVOS(cityId,date,type,caliberId);
        if (thresholdCityFcDOS.size()<1){
            throw new BusinessException("T706","");
        }
        return thresholdCityFcDOS.get(0);
    }

}
