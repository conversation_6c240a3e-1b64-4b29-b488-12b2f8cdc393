
package com.tsintergy.lf.serviceimpl.evalucation.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DispersionLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.DispersionLoadCityFcDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: DispersionLoadCityFcServiceImpl.java, v 0.1 2018-01-31 10:16:08 tao Exp $$
 */

@Service("dispersionLoadCityFcService")
public class DispersionLoadCityFcServiceImpl extends BaseServiceImpl implements DispersionLoadCityFcService {
    private static final Logger logger = LogManager.getLogger( DispersionLoadCityFcServiceImpl.class);

    @Autowired
    private DispersionLoadCityFcDAO dispersionLoadCityFcDAO;


    @Override
    public DataPackage queryDispersionLoadCityFcDO(DBQueryParam param) throws Exception{
        try {
            return dispersionLoadCityFcDAO.query(param);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public DispersionLoadCityFcDO doCreate(DispersionLoadCityFcDO vo) throws Exception{
        try {
            return (DispersionLoadCityFcDO)dispersionLoadCityFcDAO.create(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public void doRemoveDispersionLoadCityFcDO(DispersionLoadCityFcDO vo) throws Exception{
         try {
             dispersionLoadCityFcDAO.remove(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public void doRemoveDispersionLoadCityFcDOByPK(Serializable pk) throws Exception{
        try {
            dispersionLoadCityFcDAO.removeByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public DispersionLoadCityFcDO doUpdateDispersionLoadCityFcDO(DispersionLoadCityFcDO vo) throws Exception{
         try {
            return dispersionLoadCityFcDAO.update(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public DispersionLoadCityFcDO findDispersionLoadCityFcDOByPk(Serializable pk) throws Exception{
         try {
            return dispersionLoadCityFcDAO.findByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<DispersionLoadCityFcDO> doSaveOrUpdateDispersionLoadCityFcVOs(
        List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS) throws Exception {
        return dispersionLoadCityFcDAO.doSaveOrUpdateDispersionLoadCityFcVOs(dispersionLoadCityFcDOS);
    }
}
