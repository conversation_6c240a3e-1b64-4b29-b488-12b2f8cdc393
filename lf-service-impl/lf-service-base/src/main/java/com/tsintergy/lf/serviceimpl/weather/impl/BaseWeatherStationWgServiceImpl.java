package com.tsintergy.lf.serviceimpl.weather.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseWeatherStationWgService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherStationWgDO;
import com.tsintergy.lf.serviceimpl.weather.dao.BaseWeatherStationWgDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("baseWeatherStationWgService")
public class BaseWeatherStationWgServiceImpl extends ServiceImpl<BaseWeatherStationWgDAO, BaseWeatherStationWgDO> implements BaseWeatherStationWgService {
}
