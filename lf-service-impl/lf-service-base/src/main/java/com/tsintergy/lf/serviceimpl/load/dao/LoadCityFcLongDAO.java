/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/17 10:07
 * @Version: 1.0.0
 */
public class LoadCityFcLongDAO extends BaseAbstractDAO<LoadCityFcLongDO> {


    public List<LoadCityFcLongDO> findLoadLongDo(String cityId, String algorithmId, String caliberId,
        Date startDate, Date endDate){
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != algorithmId){
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcLongDO> datas = this.query(param).getDatas();
        return datas;

    }
}