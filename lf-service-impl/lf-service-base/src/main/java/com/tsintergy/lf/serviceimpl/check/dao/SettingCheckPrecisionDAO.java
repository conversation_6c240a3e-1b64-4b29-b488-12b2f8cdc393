package com.tsintergy.lf.serviceimpl.check.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckPrecisionDO;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: SettingReportDAO.java, v 0.1 2018-02-01 18:34:11 tao Exp $$
 */

@Component
public class SettingCheckPrecisionDAO extends BaseAbstractDAO<SettingCheckPrecisionDO> {

    /**
     * 查询准确率标准值
     *
     * @param cityId 城市ID
     * @param year 年份
     * <AUTHOR>
     */
    public List<SettingCheckPrecisionDO> findList(String cityId, String year) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (year != null) {
            param.where(QueryOp.StringEqualTo, "year", year);
        }
        return  query(param.build()).getDatas();
    }

    /**
     * 查询准确率标准值
     *
     * @param id 主键
     * <AUTHOR>
     */
    public SettingCheckPrecisionDO findById(String id) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (id != null) {
            param.where(QueryOp.StringEqualTo, "id", id);
        }
        List<SettingCheckPrecisionDO> list = query(param.build()).getDatas();
        if (list.size() != 0) {
            return list.get(0);
        } else {
            return null;
        }

    }

}