package com.tsintergy.lf.serviceimpl.forecast.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;

import java.util.List;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: AlgorithmDAO.java, v 0.1 2018-01-31 10:23:16 tao Exp $$
 */

@Component
public class AlgorithmDAO extends BaseAbstractDAO<AlgorithmDO> {

    /**
     * 根据算法ID获取算法
     *
     * @param algorithmId
     * @return
     * @throws Exception
     */
    public AlgorithmDO getAlgorithmDOById(String algorithmId) throws Exception {
        return (AlgorithmDO) this.findByPk(algorithmId);
    }


    public List<AlgorithmDO> listAlgorithmByType(Integer type) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create();
        if (type != null) {
            builder.where(QueryOp.NumberEqualTo, "type", type);
        }
        DBQueryParam param = builder.build();
        return this.query(param).getDatas();
    }

}