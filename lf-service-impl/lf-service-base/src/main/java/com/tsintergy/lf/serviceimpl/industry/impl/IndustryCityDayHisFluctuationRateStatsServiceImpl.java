package com.tsintergy.lf.serviceimpl.industry.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityDayHisFluctuationRateStatsService;
import com.tsintergy.lf.serviceapi.base.industry.dto.IndustryCityFluctuationRateStatsDTO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityDayHisFluctuationRateStatsDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctNewDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityTimeHisFluctuationRateStatsDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryRelationDO;
import com.tsintergy.lf.serviceimpl.industry.dao.IndustryCityDayHisFluctuationRateStatsDAO;
import com.tsintergy.lf.serviceimpl.industry.dao.IndustryRelationDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("IndustryCityDayHisFluctuationRateStatsService")
public class IndustryCityDayHisFluctuationRateStatsServiceImpl extends BaseServiceImpl implements IndustryCityDayHisFluctuationRateStatsService {
    private static final Logger logger = LogManager.getLogger(IndustryCityDayHisFluctuationRateStatsServiceImpl.class);

    @Autowired
    private IndustryCityDayHisFluctuationRateStatsDAO industryCityDayHisFluctuationRateStatsDAO;
    @Autowired
    private CityService cityService;
    @Autowired
    private IndustryRelationDAO industryRelationDAO;

    @Override
    public List<IndustryCityDayHisFluctuationRateStatsDO> FluctuationRateStats(
            Date beforeDate, List<IndustryCityLoadDayHisClctNewDO> hisClctDOS, List<IndustryCityLoadDayHisClctNewDO> lastYearIndustryCityHisDOS,
            List<IndustryCityTimeHisFluctuationRateStatsDO> beforeIndustryCityTimeHisFluctuationRateStatsDOs,
            List<IndustryCityTimeHisFluctuationRateStatsDO> lastYearIndustryCityTimeHisFluctuationRateStatsDOS) {

        // 前一天数据的map，方便后续算波动时第一个点的计算
        Map<String, IndustryCityLoadDayHisClctNewDO> beforeDOMap = hisClctDOS.stream().collect(
                Collectors.toMap(e ->
                        e.getCity_id() + "-" + e.getTradeCode() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv
                ));

        // 剔除开始时间前一天数据的正常数据map
        Map<String, IndustryCityLoadDayHisClctNewDO> hisDOMap = hisClctDOS.stream().filter(t -> t.getDate().compareTo(beforeDate) != 0).collect(
                Collectors.toMap(e ->
                        e.getCity_id() + "-" + e.getTradeCode() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv
                ));

        // 去年行业负荷数据的map
        Map<String, IndustryCityLoadDayHisClctNewDO> lastYearDOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lastYearIndustryCityHisDOS)) {
            lastYearDOMap = lastYearIndustryCityHisDOS.stream().collect(
                    Collectors.toMap(e ->
                            e.getCity_id() + "-" + e.getTradeCode() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv
                    ));
        }

        // 波动率数据的map
        Map<String, IndustryCityTimeHisFluctuationRateStatsDO> fluctuationRateStatsDOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(beforeIndustryCityTimeHisFluctuationRateStatsDOs)) {
            fluctuationRateStatsDOMap = beforeIndustryCityTimeHisFluctuationRateStatsDOs.stream().collect(
                    Collectors.toMap(e ->
                            e.getCityId() + "-" + e.getTradeCode() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv
                    ));
        }

        // 去年波动率数据的map
        Map<String, IndustryCityTimeHisFluctuationRateStatsDO> lastYearFluctuationRateStatsDOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lastYearIndustryCityTimeHisFluctuationRateStatsDOS)) {
            lastYearFluctuationRateStatsDOMap = lastYearIndustryCityTimeHisFluctuationRateStatsDOS.stream().collect(
                    Collectors.toMap(e ->
                            e.getCityId() + "-" + e.getTradeCode() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv
                    ));
        }

        List<IndustryCityDayHisFluctuationRateStatsDO> cityDayHisFluctuationRateStatsDOS = new ArrayList<>();
        for (Map.Entry<String, IndustryCityLoadDayHisClctNewDO> hisDOEntry : hisDOMap.entrySet()) {
            IndustryCityLoadDayHisClctNewDO hisDO = hisDOEntry.getValue();
            IndustryCityTimeHisFluctuationRateStatsDO fluctuationRateStatsDO = null;
            if (MapUtils.isNotEmpty(fluctuationRateStatsDOMap) && fluctuationRateStatsDOMap.containsKey(hisDOEntry.getKey())) {
                fluctuationRateStatsDO = fluctuationRateStatsDOMap.get(hisDOEntry.getKey());
            }

            Date date = DateUtils.addDays(hisDO.getDate(), -1);
            String key = hisDO.getCity_id() + "-" + hisDO.getTradeCode() + "-" + date.getTime();
            IndustryCityLoadDayHisClctNewDO beforeDO = MapUtils.isEmpty(beforeDOMap) ? null : beforeDOMap.get(key);
            IndustryCityTimeHisFluctuationRateStatsDO beforeFluctuationRateStatsDO = null;
            if (MapUtils.isNotEmpty(fluctuationRateStatsDOMap) && fluctuationRateStatsDOMap.containsKey(key)) {
                beforeFluctuationRateStatsDO = fluctuationRateStatsDOMap.get(key);
            }

            Date lastYearDate = DateUtils.addYears(hisDO.getDate(), -1);
            String lastYearKey = hisDO.getCity_id() + "-" + hisDO.getTradeCode() + "-" + lastYearDate.getTime();
            IndustryCityLoadDayHisClctNewDO lastYearDO = MapUtils.isEmpty(lastYearDOMap) ? null : lastYearDOMap.get(lastYearKey);
            IndustryCityTimeHisFluctuationRateStatsDO lastYearFluctuationRateStatsDO = null;
            if (MapUtils.isNotEmpty(lastYearFluctuationRateStatsDOMap) && lastYearFluctuationRateStatsDOMap.containsKey(lastYearKey)) {
                lastYearFluctuationRateStatsDO = lastYearFluctuationRateStatsDOMap.get(lastYearKey);
            }

            // 构建日实际气象波动率统计数据
            IndustryCityDayHisFluctuationRateStatsDO dayHisFluctuationRateStatsDO = new IndustryCityDayHisFluctuationRateStatsDO();
            dayHisFluctuationRateStatsDO.setCityId(hisDO.getCity_id());
            dayHisFluctuationRateStatsDO.setDate(hisDO.getDate());
            dayHisFluctuationRateStatsDO.setTradeCode(hisDO.getTradeCode());
            dayHisFluctuationRateStatsDO.setTradeCodeDsc(hisDO.getTradeCodeDsc());

            // 计算最大值，最小值以及对应的同比环比
            BigDecimal maxValue = BigDecimalFunctions.listMax(hisDO.getloadList());
            BigDecimal beforeMaxValue = beforeDO == null ? null : BigDecimalFunctions.listMax(beforeDO.getloadList());
            BigDecimal lastYearMaxValue = lastYearDO == null ? null : BigDecimalFunctions.listMax(lastYearDO.getloadList());
            BigDecimal maxValueHb = getTbAndHb(maxValue, beforeMaxValue);
            BigDecimal maxValueTb = getTbAndHb(maxValue, lastYearMaxValue);

            BigDecimal minValue = BigDecimalFunctions.listMin(hisDO.getloadList());
            BigDecimal beforeMinValue = beforeDO == null ? null : BigDecimalFunctions.listMin(beforeDO.getloadList());
            BigDecimal lastYearMinValue = lastYearDO == null ? null : BigDecimalFunctions.listMin(lastYearDO.getloadList());
            BigDecimal minValueHb = getTbAndHb(minValue, beforeMinValue);
            BigDecimal minValueTb = getTbAndHb(minValue, lastYearMinValue);

            dayHisFluctuationRateStatsDO.setMaxValue(maxValue);
            dayHisFluctuationRateStatsDO.setMaxValueTb(maxValueTb);
            dayHisFluctuationRateStatsDO.setMaxValueHb(maxValueHb);
            dayHisFluctuationRateStatsDO.setMinValue(minValue);
            dayHisFluctuationRateStatsDO.setMinValueTb(minValueTb);
            dayHisFluctuationRateStatsDO.setMinValueHb(minValueHb);

            // 计算平均波动率以及对应的同比，环比
            BigDecimal aveFluctuationRate = fluctuationRateStatsDO == null ? null : getRms(fluctuationRateStatsDO.getloadList());
            BigDecimal beforeAveFluctuationRate = beforeFluctuationRateStatsDO == null ? null : getRms(beforeFluctuationRateStatsDO.getloadList());
            BigDecimal lastYearAveFluctuationRate = lastYearFluctuationRateStatsDO == null ? null : getRms(lastYearFluctuationRateStatsDO.getloadList());
            BigDecimal aveFluctuationRateTb = getTbAndHb(aveFluctuationRate, lastYearAveFluctuationRate);
            BigDecimal aveFluctuationRateHb = getTbAndHb(aveFluctuationRate, beforeAveFluctuationRate);

            dayHisFluctuationRateStatsDO.setAveFluctuationRate(aveFluctuationRate);
            dayHisFluctuationRateStatsDO.setAveFluctuationRateTb(aveFluctuationRateTb);
            dayHisFluctuationRateStatsDO.setAveFluctuationRateHb(aveFluctuationRateHb);


            BigDecimal aveFluctuation = getFluctuation(hisDO.getloadList(),beforeDO == null ? null : beforeDO.getloadList());
            dayHisFluctuationRateStatsDO.setAveFluctuation(aveFluctuation);

            cityDayHisFluctuationRateStatsDOS.add(dayHisFluctuationRateStatsDO);
        }
        return cityDayHisFluctuationRateStatsDOS;
    }

    private BigDecimal getFluctuation(List<BigDecimal> oldList, List<BigDecimal> beforeList) {
        List<BigDecimal> loadList = new ArrayList<>();
        for(int i = 0; i < oldList.size(); i++){

            BigDecimal var1 = oldList.get(i);
            BigDecimal var2;
            if (i == 0) {
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(beforeList)) {
                    var2 = null;
                } else {
                    var2 = beforeList.get(beforeList.size() - 1);
                }
            } else {
                var2 = oldList.get(i - 1);
            }
            if (var1 == null || var2 == null) {
                loadList.add(null);
            } else {
                loadList.add(var1.subtract(var2).abs());
            }
        }
        return getRms(loadList);
    }

    private BigDecimal getRms(List<BigDecimal> numbers) {
        if (numbers.isEmpty()) {
            return null;
        }

        List<BigDecimal> notNullList = new ArrayList<>();
        for (BigDecimal number : numbers) {
            if (number == null) {
                continue;
            }
            notNullList.add(number);
        }
        if (notNullList.isEmpty()) {
            return null;
        }

        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal number : notNullList) {
            if (number == null) {
                continue;
            }
            sum = sum.add(number.pow(2));
        }

        BigDecimal mean = sum.divide(BigDecimal.valueOf(notNullList.size()), 20, RoundingMode.HALF_UP);
        return BigDecimal.valueOf(Math.sqrt(mean.doubleValue())).multiply(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal getTbAndHb(BigDecimal value, BigDecimal hisValue) {
        if (value == null || hisValue == null || hisValue.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        return (value.subtract(hisValue)).multiply(new BigDecimal(100)).divide(hisValue, 4, RoundingMode.HALF_UP);
    }

    @Override
    public void doSaveOrUpdateWeatherCityDayHisFluctuationRateStatsDOs(
            List<IndustryCityDayHisFluctuationRateStatsDO> cityDayHisFluctuationRateStatsDOS) {
        try {
            industryCityDayHisFluctuationRateStatsDAO.saveOrUpdateBatchByTemplate(cityDayHisFluctuationRateStatsDOS, 5000);
        } catch (Exception e) {
            logger.error("保存日行业负荷波动率统计数据结果出错了", e);
        }
    }

    @Override
    public List<IndustryCityFluctuationRateStatsDTO> findIndustryCityFluctuationRateStats(
            String cityId, String tradeCode, Date startDate, Date endDate) throws Exception {

        List<IndustryCityDayHisFluctuationRateStatsDO> industryCityDayHisFluctuationRateStatsDOS =
                industryCityDayHisFluctuationRateStatsDAO.findAll(JpaWrappers.<IndustryCityDayHisFluctuationRateStatsDO>lambdaQuery()
                        .ge(startDate != null, IndustryCityDayHisFluctuationRateStatsDO::getDate, startDate)
                        .le(endDate != null,IndustryCityDayHisFluctuationRateStatsDO::getDate, endDate)
                        .eq(org.apache.commons.lang3.StringUtils.isNotEmpty(cityId), IndustryCityDayHisFluctuationRateStatsDO::getCityId, cityId)
                        .eq(org.apache.commons.lang3.StringUtils.isNotEmpty(tradeCode), IndustryCityDayHisFluctuationRateStatsDO::getTradeCode, tradeCode)
                );

        List<IndustryCityFluctuationRateStatsDTO> fluctuationRateStatsDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(industryCityDayHisFluctuationRateStatsDOS)) {

            List<String> tradeCodeList = new ArrayList<>();
            for (IndustryCityDayHisFluctuationRateStatsDO fluctuationRateStatsDO : industryCityDayHisFluctuationRateStatsDOS) {
                tradeCodeList.add(fluctuationRateStatsDO.getTradeCode());
            }
            List<IndustryRelationDO> baseTradeInfo = industryRelationDAO.findAllById(tradeCodeList);
            Map<String, String> tradeCodeMap = baseTradeInfo.stream().collect(Collectors.toMap(IndustryRelationDO::getId, IndustryRelationDO::getName));

            for (IndustryCityDayHisFluctuationRateStatsDO fluctuationRateStatsDO : industryCityDayHisFluctuationRateStatsDOS) {
                IndustryCityFluctuationRateStatsDTO fluctuationRateStatsDTO = new IndustryCityFluctuationRateStatsDTO();
                BeanUtils.copyProperties(fluctuationRateStatsDO, fluctuationRateStatsDTO);
                fluctuationRateStatsDTO.setCity("福建");
                if (MapUtils.isNotEmpty(tradeCodeMap) && tradeCodeMap.containsKey(fluctuationRateStatsDO.getTradeCode())) {
                    fluctuationRateStatsDTO.setTradeCodeDsc(tradeCodeMap.get(fluctuationRateStatsDO.getTradeCode()));
                }
//                fluctuationRateStatsDTO.setMaxValueHb(fluctuationRateStatsDO.getMaxValueHb() + "%");
//                fluctuationRateStatsDTO.setMaxValueTb(fluctuationRateStatsDO.getMaxValueTb() + "%");
//                fluctuationRateStatsDTO.setMinValueHb(fluctuationRateStatsDO.getMinValueHb() + "%");
//                fluctuationRateStatsDTO.setMinValueTb(fluctuationRateStatsDO.getMinValueTb() + "%");
//                fluctuationRateStatsDTO.setAveFluctuationRate(fluctuationRateStatsDO.getAveFluctuationRate() + "%");
//                fluctuationRateStatsDTO.setAveFluctuationRateTb(fluctuationRateStatsDO.getAveFluctuationRateTb() + "%");
//                fluctuationRateStatsDTO.setAveFluctuationRateHb(fluctuationRateStatsDO.getAveFluctuationRateHb() + "%");
                fluctuationRateStatsDTO.setAveFluctuation(fluctuationRateStatsDO.getAveFluctuation().divide(new BigDecimal(100),4, RoundingMode.HALF_UP));
                fluctuationRateStatsDTOList.add(fluctuationRateStatsDTO);
            }
        }

        return fluctuationRateStatsDTOList;
    }

}
